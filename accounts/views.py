import logging

from accounts.models import User
from accounts.serializers import UserDeletionSerializer
from accounts.serializers import UserSerializer
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class UserprofileView(RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserSerializer
    queryset = User.objects.all()

    def get_object(self):
        return get_object_or_404(User, pk=self.request.user.id)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = UserDeletionSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )

        instance.deleted_at = timezone.now()
        instance.deletion_reason = serializer.validated_data["reason"]
        instance.deletion_notes = serializer.validated_data.get("notes")
        instance.is_active = False
        instance.save()

        return Response(status=status.HTTP_204_NO_CONTENT)
