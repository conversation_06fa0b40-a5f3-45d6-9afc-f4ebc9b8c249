import logging

import phonenumbers
from core.serializers import TimezoneConverterMixin
from drf_extra_fields.fields import Base64Image<PERSON>ield
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from .models import User


logger = logging.getLogger(__name__)


class PhoneNumberField(serializers.CharField):
    """
    Custom field that formats phone numbers.
    """

    def __init__(self, **kwargs):
        # Make field optional by default
        kwargs.setdefault("required", False)
        kwargs.setdefault("allow_blank", True)
        kwargs.setdefault("allow_null", True)
        kwargs.setdefault("read_only", True)
        super().__init__(**kwargs)

    def get_attribute(self, instance):
        # This tells DRF how to get the attribute from the instance
        if self.source:
            return getattr(instance, self.source)
        return super().get_attribute(instance)

    def to_representation(self, value):
        """Format the phone number when returning data."""
        if not value:
            return value

        # Get user from context
        request = self.context.get("request", None)
        user = request.user if request else None

        # Get user's preferred region or default to "US"
        region = "US"
        if user:
            from general.models import Setting

            try:
                region = Setting.objects.get_user_phone_region(user)
            except Exception:
                pass  # Fall back to "US" if there's an error

        try:
            # Parse the number with the user's preferred region
            parsed_number = phonenumbers.parse(value, region)

            national_format = phonenumbers.format_number(
                parsed_number, phonenumbers.PhoneNumberFormat.NATIONAL
            )

            # Add the country code manually with the desired format (e.g., +****************)
            formatted_number = (
                f"+{parsed_number.country_code} {national_format}"
            )

            # Replace any dashes with a space
            formatted_number = formatted_number.replace("-", " ")

            return formatted_number
        except Exception:
            # If formatting fails, return the original value
            return value


class UserSerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    company_id = serializers.SerializerMethodField()
    profile_picture = Base64ImageField(required=False)
    formatted_mobile = PhoneNumberField(source="mobile", required=False)

    class Meta:
        model = User
        exclude = ["password", "user_permissions", "groups", "is_superuser"]
        read_only_fields = [
            "last_login",
            "date_joined",
            "is_registration_completed",
            "is_active",
            "is_staff",
            "email",
            "source",
        ]
        extra_kwargs = {"password": {"write_only": True}}

    def get_company_id(self, obj: User):
        company = obj.company_set.first()
        return company.id if company else None


class UserSummarySerializer(serializers.ModelSerializer):

    timezone = serializers.SerializerMethodField()
    formatted_mobile = PhoneNumberField(source="mobile")

    class Meta:
        model = User
        fields = [
            "id",
            "first_name",
            "last_name",
            "email",
            "mobile",
            "profile_picture",
            "date_joined",
            "last_login",
            "last_active",
            "is_active",
            "deletion_reason",
            "deletion_notes",
            "deleted_at",
            "timezone",
            "formatted_mobile",
        ]

    def get_timezone(self, obj: User):
        from general.models import Setting

        user_settings = Setting.objects.get_or_create_settings(obj)
        return user_settings.timezone


class UserDetailsSerializer(serializers.ModelSerializer):
    from company.serializers import CompanySummarySerializer
    from storage.serializers import StorageSummarySerializer

    companies = serializers.SerializerMethodField()
    storage = serializers.SerializerMethodField()
    file_size_by_project = serializers.SerializerMethodField()
    project_summary = serializers.SerializerMethodField()
    formatted_mobile = PhoneNumberField(source="mobile")

    class Meta:
        model = User
        exclude = ["password", "user_permissions", "groups", "is_superuser"]

    @swagger_serializer_method(
        serializer_or_field=CompanySummarySerializer(many=True)
    )
    def get_companies(self, obj: User):
        from company.serializers import CompanySummarySerializer

        return CompanySummarySerializer(obj.company_set, many=True).data

    @swagger_serializer_method(serializer_or_field=StorageSummarySerializer())
    def get_storage(self, obj: User):
        from storage.serializers import StorageSummarySerializer
        from storage.models import File

        try:
            return StorageSummarySerializer(
                File.objects.get_storage_summary(obj)
            ).data
        except Exception as err:
            logger.error(
                f"Failed to get storage summary for user {obj.id}",
                exc_info=err,
            )
        return {}

    def get_file_size_by_project(self, obj: User):

        from storage.models import File

        try:
            return File.objects.get_file_size_by_project(obj)

        except Exception as err:
            logger.error(
                f"Failed to get file size per project for user {obj.id}",
                exc_info=err,
            )

        return {}

    def get_project_summary(self, obj: User):

        from storage.models import File

        try:
            return File.objects.get_project_summary(obj)

        except Exception as err:
            logger.error(
                f"Failed to get project summary for user {obj.id}",
                exc_info=err,
            )

        return {}


class UserDeletionSerializer(serializers.Serializer):
    reason = serializers.ChoiceField(choices=User.DeletionReason.CHOICES)
    notes = serializers.CharField(required=False, allow_blank=True)
