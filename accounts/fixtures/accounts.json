[{"model": "admin.logentry", "pk": 1, "fields": {"action_time": "2023-03-31T06:22:40.499Z", "user": "6c3b6143-881f-47a4-a3c9-e82bd7299eff", "content_type": 7, "object_id": "01cf2feb-7b72-4f43-8d5c-b738e5feaf14", "object_repr": "Demo Company", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add user", "content_type": 6, "codename": "add_user"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change user", "content_type": 6, "codename": "change_user"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete user", "content_type": 6, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view user", "content_type": 6, "codename": "view_user"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add company", "content_type": 7, "codename": "add_company"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change company", "content_type": 7, "codename": "change_company"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete company", "content_type": 7, "codename": "delete_company"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view company", "content_type": 7, "codename": "view_company"}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "accounts", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "company", "model": "company"}}, {"model": "sessions.session", "pk": "un07d3lvb8to78uewnjzw38wz3a06jhh", "fields": {"session_data": ".eJxVjEsOwjAMBe-SNY2SxooTluw5Q2XHMeWjVupnhbg7rdQF7J40M-9tOlqXvlvnOnV3MWcTS-DoITQpeW0ACRoKJTc1tSzY5lxVzek3YyrPOuytPGi4jbaMwzLd2e6KPehsr6PU1-Vw_w56mvuthgxKTOIoOdDMRTRrVK_ZFdbgsSbRALE4QEAEUe_YY4u4udsyny8gyEG9:1pi81c:cA8IXknCEDRk5r23wHZLg8TBEEOtTKupVg1bWGtIDdY", "expire_date": "2023-04-14T06:14:12.243Z"}}, {"model": "sessions.session", "pk": "w03qacgwqndbutpkc3r12ry850uj61hz", "fields": {"session_data": ".eJxVjEsOwjAMBe-SNY2SxooTluw5Q2XHMeWjVupnhbg7rdQF7J40M-9tOlqXvlvnOnV3MWcTS-DoITQpeW0ACRoKJTc1tSzY5lxVzek3YyrPOuytPGi4jbaMwzLd2e6KPehsr6PU1-Vw_w56mvuthgxKTOIoOdDMRTRrVK_ZFdbgsSbRALE4QEAEUe_YY4u4udsyny8gyEG9:1pjGrW:kljGQFDKibWJpj8ot6lGUaSsoPN-AuZs_iWau7bVCPA", "expire_date": "2023-04-17T09:52:30.579Z"}}, {"model": "accounts.user", "pk": "6c3b6143-881f-47a4-a3c9-e82bd7299eff", "fields": {"password": "pbkdf2_sha256$260000$kIy9j1ltQ14Ym4mkf1lxc2$H/QjAibwtFO90KJCXv7SOz1skQ58AepOjEcLadoSpHA=", "last_login": "2023-04-03T09:52:30.574Z", "is_superuser": true, "first_name": "", "last_name": "", "is_staff": true, "is_active": true, "date_joined": "2023-03-31T06:13:45.724Z", "email": "<EMAIL>", "mobile": "<EMAIL>", "is_registration_completed": false, "gender": "Other", "groups": [], "user_permissions": []}}, {"model": "accounts.user", "pk": "929b50e5-dfc3-4c6d-81aa-b3e61484595d", "fields": {"password": "", "last_login": null, "is_superuser": false, "first_name": "", "last_name": "", "is_staff": false, "is_active": true, "date_joined": "2023-03-31T16:10:08.593Z", "email": "+<EMAIL>", "mobile": "+************", "is_registration_completed": false, "gender": "Other", "groups": [], "user_permissions": []}}]