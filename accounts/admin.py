from django.contrib import admin
from django.contrib import messages
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from import_export.admin import ExportActionMixin
from import_export.admin import ImportExportModelAdmin

from . import models


@admin.action(description="Update Users Last Active")
def update_users_last_active(modeladmin, request, queryset):
    users = models.User.objects.filter(last_active__isnull=True)
    updated_count = 0

    for user in users:
        user: models.User
        user.last_active = user.date_joined
        updated_count += 1

    models.User.objects.bulk_update(users, ["last_active"])

    messages.success(
        request,
        _(f"Successfully updated last_active for {updated_count} users."),
    )


@admin.action(description="Generate JWT tokens for selected users")
def generate_jwt_tokens(modeladmin, request, queryset):
    from authentication.serializers import MyTokenObtainPairSerializer

    tokens = {}
    for user in queryset:
        tokens[user.email] = MyTokenObtainPairSerializer().get_tokens_for_user(
            user
        )

    # export the tokens to a csv file
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = 'attachment; filename="tokens.csv"'
    writer = csv.writer(response)
    writer.writerow(["email", "token"])
    for email, token in tokens.items():
        writer.writerow([email, token["access"]])
    return response


@admin.action(description="Update users last login from Firebase")
def update_users_last_login(modeladmin, request, queryset):
    from utils.firebase.authentification import FirebaseAuthentication

    users = models.User.objects.all()
    updated_count = 0

    for user in users:
        user: models.User
        last_login = (
            FirebaseAuthentication.get_last_login_from_firebase_by_email(
                user.email, user.date_joined
            )
        )
        if last_login:
            user.last_login = last_login
            user.save(update_fields=["last_login"])
            updated_count += 1

    messages.success(
        request,
        _(f"Successfully updated last login for {updated_count} users."),
    )


class CustomUserAdmin(UserAdmin, ImportExportModelAdmin, ExportActionMixin):
    model = models.User
    list_display = (
        "email",
        "first_name",
        "last_name",
        "is_staff",
        "is_active",
        "last_login",
        "last_active",
        "is_registration_completed",
    )
    search_fields = ("email", "first_name", "last_name")
    list_filter = ("email", "is_registration_completed")
    ordering = ("email",)
    list_filter = ["last_active", "last_login", "is_staff"]
    fieldsets = (
        (None, {"fields": ("email", "password")}),
        (_("Personal info"), {"fields": ("first_name", "last_name")}),
        (
            _("Permissions"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                ),
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "date_joined")}),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("email", "password1", "password2"),
            },
        ),
    )
    actions = [
        update_users_last_login,
        update_users_last_active,
        generate_jwt_tokens,
    ]


admin.site.register(models.User, CustomUserAdmin)
