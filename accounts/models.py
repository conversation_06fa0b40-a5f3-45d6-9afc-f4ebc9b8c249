import json
import typing
import uuid
from datetime import datetime

import requests
from core.models import BaseAddress
from django.conf import settings
from django.contrib.auth.base_user import BaseUserManager
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.db.models.signals import post_save
from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.utils.translation import gettext as _

if typing.TYPE_CHECKING:
    from project.models import Project
    from contacts.models import Contact
    from company.models import Company
    from storage.models import File


class UserQuerySet(models.QuerySet):
    def active(self):
        return self.filter(is_active=True)

    def inactive(self):
        return self.filter(is_active=False)

    def staff(self):
        return self.filter(is_staff=True)

    def superusers(self):
        return self.filter(is_superuser=True)

    def registration_completed(self):
        return self.filter(is_registration_completed=True)

    def registration_not_completed(self):
        return self.filter(is_registration_completed=False)

    def source(self, source):
        return self.filter(source=source)

    def filter_by_date(self, before=None, after=None):
        if before and after:
            return self.filter(date_joined__range=[after, before])

        if before:
            return self.filter(date_joined__lte=before)

        if after:
            return self.filter(date_joined__gte=after)

        return self


class UserManager(BaseUserManager):
    """
    Custom user model manager where email is the unique identifiers
    for authentication instead of usernames.
    """

    def get_queryset(self):
        return UserQuerySet(self.model, using=self._db)

    def create_user(self, email, password, **extra_fields):
        """
        Create and save a User with the given email and password.
        """
        if not email:
            raise ValueError(_("The Email must be set"))
        if not password:
            raise ValueError(_("The Password must be set"))
        extra_fields.setdefault("is_active", False)
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password, **extra_fields):
        """
        Create and save a SuperUser with the given email and password.
        """
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError(_("Superuser must have is_staff=True."))
        if extra_fields.get("is_superuser") is not True:
            raise ValueError(_("Superuser must have is_superuser=True."))
        return self.create_user(email, password, **extra_fields)


class User(AbstractUser, BaseAddress):

    project_set: models.QuerySet["Project"]
    file_set: models.QuerySet["File"]
    contacts: models.QuerySet["Contact"]
    company_set: models.QuerySet["Company"]
    objects: UserManager

    class Gender:
        MALE = "Male"
        FEMALE = "Female"
        OTHER = "Other"

        CHOICES = (
            (MALE, _("Male")),
            (FEMALE, _("Female")),
            (OTHER, _("Other")),
        )

    class Source:
        WEB = "Web"
        MOBILE = "Mobile"

        CHOICES = ((WEB, _("Web")), (MOBILE, _("Mobile")))

    class DeletionReason:
        NOT_USEFUL = "not_useful"
        NOT_WORKING = "not_working"
        PRIVACY_CONCERNS = "privacy_concerns"
        OTHER = "other"

        CHOICES = (
            (NOT_USEFUL, _("Limited utility/features")),
            (NOT_WORKING, _("Performance issues")),
            (PRIVACY_CONCERNS, _("Privacy concerns")),
            (OTHER, _("Other")),
        )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = None
    email = models.EmailField(_("user email"), max_length=254, unique=True)
    mobile = models.CharField(_("mobile number"), max_length=20, blank=True)
    extension = models.CharField(
        max_length=5, null=True, blank=True, default=""
    )
    is_registration_completed = models.BooleanField(default=False)
    gender = models.CharField(
        max_length=10, choices=Gender.CHOICES, default=Gender.OTHER
    )
    source = models.CharField(
        max_length=10, choices=Source.CHOICES, default=Source.WEB
    )
    profile_picture = models.ImageField(
        upload_to="profile_pictures", blank=True, null=True
    )
    deleted_at = models.DateTimeField(null=True, blank=True)
    deletion_reason = models.CharField(
        max_length=50, choices=DeletionReason.CHOICES, null=True, blank=True
    )
    deletion_notes = models.TextField(null=True, blank=True)
    last_active = models.DateTimeField(null=True, blank=True)
    objects = UserManager()
    USERNAME_FIELD = "email"
    EMAIL_FIELD = "email"
    REQUIRED_FIELDS = []

    def default_email(self):
        return f"{self.mobile}@default.com"

    def default_mobile(self):
        return ""

    @property
    def full_name(self):
        return (
            f"{self.first_name} {self.last_name}"
            if self.first_name or self.last_name
            else self.email or self.username
        )


@receiver(pre_save, sender=User)
def create_a_default_email(sender, instance: User, *args, **kwargs):
    if not instance.email:
        instance.email = instance.default_email()
    if not instance.mobile:
        instance.mobile = instance.default_mobile()[:20]


@receiver(pre_save, sender=User)
def change_user_registation_status(sender, instance: User, **kwargs):
    required_fields = [
        instance.email,
        instance.mobile,
        instance.first_name,
    ]

    if (
        None in required_fields
        or "" in required_fields
        or instance.email == instance.default_email()
    ):
        instance.is_registration_completed = False
    else:
        instance.is_registration_completed = True


@receiver(post_save, sender=User)
def create_a_default_company(sender, instance: User, created, **kwargs):
    from company.models import Company

    if created:
        Company.objects.create(user=instance)

    # if user has no company, create a default company for user
    if not instance.company_set.all():
        Company.objects.create(user=instance)

    # Send notification to Slack
    if created:
        send_slack_notification(instance)


def send_slack_notification(user):
    slack_token = settings.SLACK_TOKEN

    LOGS_CHANNEL_ID = settings.LOGS_CHANNEL_ID

    user_source = user.source if hasattr(user, "source") else "Unknown"

    message = {
        "channel": LOGS_CHANNEL_ID,
        "text": f"*Message*\nNew sign up from {user_source}  \
            on:\nhttps://www.tuulbox.app/\n*Name* \
            \n{user.get_full_name()}\n*When* \
            \n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
    }

    headers = {
        "Content-type": "application/json",
        "Authorization": f"Bearer {slack_token}",
    }

    response = requests.post(
        "https://slack.com/api/chat.postMessage",
        headers=headers,
        data=json.dumps(message),
    )
    if response.status_code != 200:
        print("Failed to send notification to Slack")
