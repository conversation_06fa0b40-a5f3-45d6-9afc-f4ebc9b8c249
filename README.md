# Tuulbox core

> Django Backend api for Tuulbox app.
> ![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-green.svg) > ![contributions welcome](https://img.shields.io/badge/contributions-welcome-brightgreen.svg?style=flat)

## Requirements (Prerequisites)

Tools and packages required to successfully install this project.
For example:

- Python 3.7 and up [Install](https://python.org)
- Postgres 14.0 and up [Install](https://postgres.com/)

## Installation (DEVELOPMENT SETUP)

- Create a .env file in root directory to override all environmental variables
- You can also set the variable as system variables

A step by step list of commands.

`$ git clone project-ul`

`$ python3 -m venv env`

`$ source env/bin/activate`

`$ pip install -r requirements.txt`

`$ python manage.py migrate`

`$ pre-commit install`

`$ bash scripts/load_data.sh`

`$ python manage.py runserver_plus 7000`

## Installation (DOCKER SETUP)

- Remane the .env.example file in root directory to .env
- Ask the PM for valid cedentials and use it the update the .env file
- Install Docker Desktop [Install](https://python.org)

A step by step list of commands.

`$ git clone project-ul`

`$ cd tuulbox_core`

`$ docker-compose up --build -d`

- Visit localhost:7000 in your browser

## How View the Swagger documentation

- Visit localhost:7000/admin
- <NAME_EMAIL>:asdf1234
- Visit localhost:7000/swagger

## Features

- Account registration

## Running the tests

Give code examples as:

- `$ python manage.py test`

## Test account

    admin <EMAIL>:asdf1234

    clent <EMAIL>:asdf1234

## Running Celery Tasks in the Background (for testing)

To run Celery tasks in the background, follow these steps:

1. Open a terminal window.
2. Navigate to the root directory of the project.
3. Run the following command to start the Celery worker:

   ```
   $ celery -A core worker -l INFO --pool=solo
   ```

4. Open another terminal window.
5. Navigate to the root directory of the project.
6. Run the following command to start the Celery beat scheduler:

   ```
   $ celery -A core beat -l INFO
   ```

## Tech Stack / Built With

1. [Django](https://django.com/) - The Python framework
2. [Postgres](https://postgres.com/) - The Database

## Authors

Mohammed Musah – <EMAIL>

You can find me here at:
[Github](https://github.com/sagelieoff)
[LinkedIn](https://www.linkedin.com/sageliteoff/)

## License

This project is licensed under the MIT License - see the LICENSE.md file for details

MIT © KayakTech
