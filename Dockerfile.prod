# The first instruction is what image we want to base our container on
# We Use an official Python runtime as a parent image
FROM python:3.10

# Get Chrome
RUN apt-get update
RUN apt-get install -y chromium libreoffice poppler-utils

# Set the working directory to /backend
WORKDIR /backend

# Install any needed packages specified in requirements.txt
RUN pip install --upgrade pip setuptools 'ipython[notebook]'
ADD ./requirements.txt /backend
RUN pip install -r requirements.txt

RUN playwright install chromium

# Copy the current directory contents into the container at /backend
ADD . /backend

EXPOSE 8000 8002 8888