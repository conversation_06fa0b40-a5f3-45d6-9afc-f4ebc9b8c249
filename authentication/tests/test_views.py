from accounts.factories import UserFactory
from accounts.models import User
from django.urls import reverse
from rest_framework import status
from testing.base import BaseAPITest


class AuthenticationTest(BaseAPITest):
    def setUp(self):
        super().setUp()

    def test_login_with_firebase(self):
        url = reverse(
            "auth_token",
        )

        res = self.client.post(
            url,
            data={},
            HTTP_AUTHORIZATION="Firebase abcd",
        )
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_with_email_password(self):
        url = reverse(
            "staff_login",
        )

        # Correct credentials but not staff
        credentials = {
            "email": "<EMAIL>",
            "password": "password",
        }
        user = User.objects.create_user(**credentials)
        user.is_active = True
        user.save()

        res = self.client.post(
            url,
            data=credentials,
        )

        self.assertEqual(res.status_code, status.HTTP_403_FORBIDDEN)

        # Correct credentials and is staff
        credentials = {
            "email": "<EMAIL>",
            "password": "password",
        }
        User.objects.create_user(**credentials, is_staff=True, is_active=True)

        res = self.client.post(
            url,
            data=credentials,
        )
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # Wrong password
        credentials = {
            "email": "<EMAIL>",
            "password": "wrongpassword",
        }

        res = self.client.post(
            url,
            data=credentials,
        )
        self.assertEqual(res.status_code, status.HTTP_400_BAD_REQUEST)

    def test_change_password(self):
        url = reverse(
            "change_password",
        )

        old_password = "password"
        data = {
            "old_password": old_password,
            "new_password": "newpassword",
        }

        user: User = UserFactory()
        user.set_password(old_password)
        self.assertEqual(user.check_password(old_password), True)

        # WHEN not authenticated
        self.client.force_authenticate(user=None)
        res = self.client.post(url, data)

        # THEN
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # WHEN is authenticated but not staff
        self.client.force_authenticate(user=user)
        res = self.client.post(url, data)

        # THEN
        self.assertEqual(res.status_code, status.HTTP_403_FORBIDDEN)

        # WHEN authenticated and staff
        user.is_staff = True
        user.save()
        self.client.force_authenticate(user=user)
        res = self.client.post(url, data)

        # THEN
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)
        self.assertEqual(user.check_password("newpassword"), True)
