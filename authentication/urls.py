from django.urls import path

from . import views


urlpatterns = [
    path(
        "signup/",
        views.FirebaseSignupView.as_view(),
        name="signup",
    ),
    path(
        "verify_email/",
        views.VerifyEmailView.as_view(),
        name="verify_email_view",
    ),
    path("gimme-jwt/", views.GimmeJWTTokenView.as_view(), name="auth_token"),
    path(
        "staff-login/",
        views.LoginWithEmailAndPassword.as_view(),
        name="staff_login",
    ),
    path(
        "change-password/",
        views.ChangePasswordView.as_view(),
        name="change_password",
    ),
    path(
        "user-login-success/<str:id>/",
        views.UserLoginSuccessView.as_view(),
        name="user_login_success",
    ),
    path(
        "user-login-expired/<str:id>/",
        views.UserLoginExpiredView.as_view(),
        name="user_login_expired",
    ),
    path(
        "resend-verification-email/<str:user_id>/",
        views.ResendVerificationEmailView.as_view(),
        name="resend_verification_email",
    ),
]
