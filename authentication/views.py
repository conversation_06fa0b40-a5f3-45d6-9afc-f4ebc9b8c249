import uuid
from datetime import datetime

from accounts.models import User
from authentication.serializers import ChangePasswordSerializer
from authentication.serializers import EmailPasswordLoginSerializer
from authentication.serializers import FirebaseSignupSerializer
from authentication.serializers import MyTokenObtainPairSerializer
from core.dependency_injection import service_locator
from django.conf import settings
from django.core.cache import cache
from django.shortcuts import redirect
from django.urls import reverse
from django.views.generic import TemplateView
from drf_yasg.utils import swagger_auto_schema
from general.cache_keys import REDIS_CACHE_KEY
from rest_framework import authentication
from rest_framework import generics
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAdminUser
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from utils.firebase.authentification import FirebaseAuthentication


class FirebaseSignupView(generics.CreateAPIView):
    serializer_class = FirebaseSignupSerializer

    permission_classes = []

    def post(self, request, *args, **kwargs):
        serializer = FirebaseSignupSerializer(
            data=request.data, context=self.get_serializer_context()
        )

        serializer.is_valid(raise_exception=True)

        user_data = serializer.save()

        return Response(user_data, status=status.HTTP_201_CREATED)


class GimmeJWTTokenView(CreateAPIView):
    """
    Generate a Tuulbox JWT token for the user
    """

    authentication_classes = [FirebaseAuthentication]
    serializer_class = MyTokenObtainPairSerializer


class LoginWithEmailAndPassword(GenericAPIView):
    serializer_class = EmailPasswordLoginSerializer
    authentication_classes = [authentication.BasicAuthentication]

    @swagger_auto_schema(
        request_body=EmailPasswordLoginSerializer,
        responses={
            200: MyTokenObtainPairSerializer,
        },
    )
    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validate(request.data)
        FirebaseAuthentication.update_django_user_last_login(user.email)

        return Response(
            MyTokenObtainPairSerializer(
                context=self.get_serializer_context(user),
            ).create({})
        )

    def get_serializer_context(self, user):
        context = super().get_serializer_context()
        self.request.user = user
        context["request"] = self.request
        return context


class ChangePasswordView(CreateAPIView):
    serializer_class = ChangePasswordSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]


class VerifyEmailView(APIView):
    def get(self, request: Request, *args, **kwargs):

        token = request.query_params.get("token", None)

        query_user_id = request.query_params.get("user_id", None)

        user_id_cache_key = f"token_to_user_id_{token}"
        cache_user_id = cache.get(user_id_cache_key)

        user_id = cache_user_id if cache_user_id else query_user_id

        if not user_id:
            redirect_url = service_locator.general_service.get_user_agent_url(
                request
            )
            return redirect(redirect_url)

        user: User = User.objects.filter(id=user_id).first()
        if not user:
            front_end_404 = f"{settings.FRONTEND_URL}/404"
            return redirect(front_end_404)

        token_cache_key = REDIS_CACHE_KEY.get_email_verification_key(user.id)
        stored_token = cache.get(token_cache_key)

        is_already_active = user.is_active
        is_token_expired = False if stored_token else True

        if cache_user_id:
            cache.delete(user_id_cache_key)
        if stored_token:
            cache.delete(token_cache_key)

        try:
            if not is_already_active and not is_token_expired:
                FirebaseAuthentication.activate_firebase_user(user.email)
                user.is_active = True
                user.save()
                return redirect("user_login_success", id=user.id)

            elif is_already_active:
                redirect_url = (
                    service_locator.general_service.get_user_agent_url(request)
                )
                return redirect(redirect_url)

            elif not is_already_active and is_token_expired:
                return redirect("user_login_expired", id=user.id)

            else:
                redirect_url = (
                    service_locator.general_service.get_user_agent_url(request)
                )
                return redirect(redirect_url)

        except Exception:
            front_end_404 = f"{settings.FRONTEND_URL}/404"
            return redirect(front_end_404)


class UserLoginSuccessView(TemplateView):
    template_name = "templates/user_login_success.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = None
        try:
            user_id = kwargs.get("id", "")
            user = User.objects.filter(id=user_id).first()
            context["email"] = user.email if user else None
        except Exception:
            pass

        redirect_url = service_locator.general_service.get_user_agent_url(
            self.request
        )

        # If web append login query param
        if settings.FRONTEND_URL == redirect_url and user:
            redirect_url += f"/login?email={user.email}"

        context["redirect_url"] = redirect_url
        context["show_url_as_link"] = "testing" in f"{settings.APP_DOMAIN}"

        return context


class UserLoginExpiredView(TemplateView):
    template_name = "templates/user_login_expired.html"

    def get_context_data(self, **kwargs):

        context = super().get_context_data(**kwargs)
        context[
            "redirect_url"
        ] = service_locator.general_service.get_user_agent_url(self.request)

        user = User.objects.filter(id=kwargs.get("id", "")).first()
        context["email"] = user.email if user else None
        context["user_id"] = user.id if user else None

        return context


class ResendVerificationEmailView(APIView):
    def post(self, request, user_id):

        user = User.objects.filter(id=user_id).first()

        if not user:
            return Response(
                {"error": "User not found"}, status=status.HTTP_404_NOT_FOUND
            )

        if user.is_active:
            redirect_url = service_locator.general_service.get_user_agent_url(
                request
            )

            return redirect(redirect_url)

        cooldown_cache_key = f"verification_email_cooldown_{user.id}"
        cooldown_active = cache.get(cooldown_cache_key)

        if cooldown_active:
            # Get the remaining time in seconds
            remaining_time = cache.ttl(cooldown_cache_key)
            if remaining_time < 0:
                remaining_time = settings.EMAIL_RESEND_COOLDOWN_SECONDS

            return Response(
                {
                    "error": "Please wait before requesting another verification email",
                    "remaining_seconds": remaining_time,
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS,
            )

        verification_token = str(uuid.uuid4())

        token_cache_key = REDIS_CACHE_KEY.get_email_verification_key(user.id)
        user_id_cache_key = f"token_to_user_id_{verification_token}"

        timeout = (
            service_locator.general_service.app_setting.account_verification_timeout_seconds
            or 86400
        )

        cache.set(token_cache_key, verification_token, timeout=timeout)
        cache.set(user_id_cache_key, user.id, timeout=timeout)

        verify_email_url = (
            reverse("verify_email_view")
            + f"?token={verification_token}&user_id={user.id}"
        )
        account_verification_url = request.build_absolute_uri(verify_email_url)

        service_locator.core_service.send_email(
            template_path="emails/email_verification.html",
            subject="Email Verification",
            template_context={
                "account_verification_url": account_verification_url,
                "user": user.get_full_name(),
                "user_id": user.id,
                "play_store_app_url": settings.PLAY_STORE_APP_URL,
                "app_store_app_url": settings.APP_STORE_APP_URL,
                "current_year": datetime.now().year,
            },
            to_emails=[user.email],
        )

        cooldown_period = settings.EMAIL_RESEND_COOLDOWN_SECONDS
        cache.set(cooldown_cache_key, True, timeout=cooldown_period)

        return Response(
            {
                "success": "Verification email sent successfully",
                "cooldown_seconds": cooldown_period,
            },
            status=status.HTTP_200_OK,
        )
