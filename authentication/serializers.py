import uuid
from datetime import datetime

from accounts.models import User
from core.dependency_injection import service_locator
from django.conf import settings
from django.contrib.auth import authenticate
from django.core.cache import cache
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from general.cache_keys import REDIS_CACHE_KEY
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied
from rest_framework_simplejwt.tokens import RefreshToken
from utils.firebase.authentification import FirebaseAuthentication
from utils.utils import chat_client


class FirebaseSignupSerializer(serializers.Serializer):

    email = serializers.EmailField(required=True)
    password = serializers.CharField(
        write_only=True, min_length=6, style={"input_type": "password"}
    )

    first_name = serializers.Char<PERSON>ield()
    last_name = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    phone_number = serializers.Char<PERSON><PERSON>()

    def validate(self, data):

        firebase_user = FirebaseAuthentication.get_firebase_user_by_email(
            data.get("email")
        )
        if firebase_user:

            raise serializers.ValidationError(
                {"email": _("User with this email already exists")}
            )

        first_name = data.get("first_name", "")
        last_name = data.get("last_name", "")
        if first_name or last_name:
            data["display_name"] = f"{first_name} {last_name}".strip()
        else:
            data["display_name"] = ""

        return data

    def create(self, validated_data):

        request = self.context.get("request")

        validated_data.pop("confirm_password", None)

        firebase_data = {
            "email": validated_data.get("email"),
            "password": validated_data.get("password"),
            "display_name": validated_data.get("display_name", ""),
            "phone_number": validated_data.get("phone_number"),
            "request": request,
        }

        (
            firebase_user,
            django_user,
        ) = FirebaseAuthentication.create_firebase_user(**firebase_data)

        return {
            "email": firebase_user.get("email"),
            "first_name": django_user.first_name,
            "last_name": django_user.last_name,
            "phone_number": django_user.mobile,
        }


class MyTokenObtainPairSerializer(serializers.Serializer):
    refresh = serializers.CharField(read_only=True)
    access = serializers.CharField(read_only=True)
    source = serializers.CharField(write_only=True, required=False)
    participant_token = serializers.CharField(read_only=True, allow_null=True)

    def get_tokens_for_user(self, user: User):

        refresh: RefreshToken = RefreshToken.for_user(user)

        return {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
        }

    def get_participant_token(self, user):
        if settings.TEST_DEBUG:
            return

        try:

            token_response = chat_client.get_token_for_participant(
                email=user.email
            )
            return token_response

        except Exception as e:

            print(f"Error getting chat token for user {user.email}: {str(e)}")

            return None

    def create(self, validated_data):
        user = self.context.get("request").user

        self.activate_user_account(user)
        user.last_login = timezone.now()
        user.source = validated_data.get("source", User.Source.WEB)
        user.save()

        # Get JWT tokens
        tokens = self.get_tokens_for_user(user)

        # Add participant token, which may be None for new users
        participant_token = self.get_participant_token(user)
        tokens["participant_token"] = participant_token

        return tokens

    def activate_user_account(self, user: User):

        FirebaseAuthentication.activate_user_if_firebase_is_verified(user)
        if not user.is_active:
            self.send_reactivation_email(user)
            raise PermissionDenied(detail="User account is inactive.")

    def send_reactivation_email(self, user: User):

        token_cache_key = REDIS_CACHE_KEY.get_email_verification_key(user)
        stored_token = cache.get(token_cache_key)

        if stored_token:
            return
        request = self.context.get("request")

        verification_token = str(uuid.uuid4())
        user_id_cache_key = f"token_to_user_id_{verification_token}"

        timeout = (
            service_locator.general_service.app_setting.account_verification_timeout_seconds
        )
        cache.set(token_cache_key, verification_token, timeout=timeout)
        cache.set(user_id_cache_key, user.id, timeout=timeout)

        verify_email_url = (
            reverse("verify_email_view")
            + f"?token={verification_token}&user_id={user.id}"
        )
        account_verification_url = request.build_absolute_uri(verify_email_url)

        service_locator.core_service.send_email(
            template_path="emails/email_verification.html",
            subject="Verify Your Email Address",
            template_context={
                "account_verification_url": account_verification_url,
                "user": user.full_name,
                "user_id": user.id,
                "play_store_app_url": settings.PLAY_STORE_APP_URL,
                "app_store_app_url": settings.APP_STORE_APP_URL,
                "current_year": datetime.now().year,
            },
            to_emails=[user.email],
        )


class EmailPasswordLoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()

    def validate(self, attrs):
        email = attrs.get("email")
        password = attrs.get("password")
        user = authenticate(email=email, password=password)

        if not user:
            raise serializers.ValidationError("Invalid credentials")

        # not staff return 403
        if not user.is_staff:
            raise PermissionDenied("You are not allowed to login here")

        return user


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True, write_only=True)
    new_password = serializers.CharField(
        required=True, write_only=True, min_length=8
    )

    def validate(self, attrs):
        user: User = self.context.get("request").user
        old_password = attrs.get("old_password")

        if not user.check_password(old_password):
            raise serializers.ValidationError("Invalid old password")

        return attrs

    def create(self, validated_data):
        user: User = self.context.get("request").user
        new_password = validated_data.get("new_password")
        user.set_password(new_password)
        user.save()
        return user
