# The first instruction is what image we want to base our container on
# We Use an official Python runtime as a parent image
FROM python:3.10

# The enviroment variable ensures that the python output is set straight
# to the terminal with out buffering it first
ENV PYTHONUNBUFFERED 1


# Set the working directory to /tuulbox_core
WORKDIR /tuulbox_core

# Install any needed packages specified in requirements.txt
ADD ./requirements.txt /tuulbox_core/
RUN pip install -r requirements.txt

# Copy the current directory contents into the container at /tuulbox_core
ADD . /tuulbox_core/

EXPOSE 7000