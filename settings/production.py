import os

from dotenv import dotenv_values
from settings.base import *  # noqa: F403


config = {
    **dotenv_values(".env"),  # load sensitive variables
    **os.environ,  # override loaded values with environment variables
}


DEBUG = config.get("DEBUG", "False") == "True"


PUBLIC_MEDIA_LOCATION = "media/public/"
PRIVATE_MEDIA_LOCATION = "media/private/"
STATIC_LOCATION = "static/"


AWS_ACCESS_KEY_ID = config["AWS_ACCESS_KEY_ID"]
AWS_SECRET_ACCESS_KEY = config["AWS_SECRET_ACCESS_KEY"]
AWS_STORAGE_BUCKET_NAME = config["AWS_STORAGE_BUCKET_NAME"]
AWS_S3_REGION_NAME = config["AWS_S3_REGION_NAME"]
AWS_S3_CUSTOM_DOMAIN = config["AWS_S3_CUSTOM_DOMAIN"]
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = "public-read"
AWS_S3_OBJECT_PARAMETERS = {"CacheControl": "max-age=86400"}
STATIC_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}"
STATICFILES_STORAGE = "core.storage_backends.StaticStorage"

MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{PUBLIC_MEDIA_LOCATION}"
DEFAULT_FILE_STORAGE = "core.storage_backends.PublicMediaStorage"
PRIVATE_FILE_STORAGE = "core.storage_backends.PrivateMediaStorage"

FILE_UPLOAD_STORAGE = config.get("FILE_UPLOAD_STORAGE", "s3")
AWS_PRESIGNED_EXPIRY = int(config.get("AWS_PRESIGNED_EXPIRY", 10))
FILE_MAX_SIZE = int(config.get("FILE_MAX_SIZE", 1024))
