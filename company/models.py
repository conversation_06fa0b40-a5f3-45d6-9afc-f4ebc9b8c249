import logging

from accounts.models import User
from contacts.models import Subcontractor
from core.models import BaseAddress
from core.models import BaseModel
from django.contrib.auth import get_user_model
from django.db import models
from django.db.models import Q
from django.db.models.functions import Lower
from django.utils import timezone
from storage.models import File
from utils.common import get_user_setting

from .mixins import DocumentBaseMixin


# create a manager for the Company model
class CompanyManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    # get user's company
    def get_user_company(self, user: User):
        return self.get_queryset().filter(user=user).first()

    # get user's companies
    def get_user_companies(self, user: User):
        # Get companies where the user is an accepted member
        member_companies = Member.objects.get_accepted_member_companies(user)

        # Combine user's own companies and companies they're a member of
        return (
            self.get_queryset()
            .filter(Q(user=user) | Q(id__in=member_companies))
            .distinct()
        )

    # get user's current company

    def get_user_current_company(self, user: User) -> "Company | None":
        user_setting = get_user_setting(user)

        return self.get_queryset().filter(id=user_setting.company).first()


class OfficerManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    # get officers for a company
    def get_company_officers(self, company: "Company"):
        return (
            self.get_queryset()
            .filter(company=company)
            .order_by(Lower("firstname"))
        )


class InsuranceManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    # get insurance for a company
    def get_company_insurance(self, company: "Company"):
        return (
            self.get_queryset()
            .filter(company=company)
            .order_by(Lower("carrier"))
        )

    # get insurance for a user
    def get_user_insurance(self, user: User):
        return (
            self.get_queryset()
            .filter(created_by=user)
            .order_by(Lower("carrier"))
        )

    def get_insurances_that_expire_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            valid_to__range=[start_of_day, end_of_day]
        )

    def get_insurances_that_need_remind_today(
        self,
    ) -> models.QuerySet["Insurance"]:
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            reminder__range=[start_of_day, end_of_day]
        )

    def get_filtered_company_insurance(self, company: "Company"):
        return (
            self.get_queryset()
            .filter(company=company, owner_contact__isnull=True)
            .order_by(Lower("carrier"))
        )

    def get_filtered_company_insurance_for_owner(
        self, company: "Company", user: User
    ):
        return (
            self.get_queryset()
            .filter(company=company, created_by=user)
            .order_by(Lower("carrier"))
        )


class LicensesManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False)
            .order_by(Lower("name"))
        )

    # get licenses for a company
    def get_company_licenses(self, company: "Company"):
        return (
            self.get_queryset().filter(company=company).order_by(Lower("name"))
        )

    # get licenses for a user
    def get_user_licenses(self, user: User):
        return (
            self.get_queryset().filter(created_by=user).order_by(Lower("name"))
        )

    def get_licenses_that_expire_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            valid_to__range=[start_of_day, end_of_day]
        )

    def get_licenses_that_need_remind_today(
        self,
    ) -> models.QuerySet["Licenses"]:
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            reminder__range=[start_of_day, end_of_day]
        )

    def get_filtered_company_licenses(self, company: "Company"):
        return (
            self.get_queryset()
            .filter(company=company, owner_contact__isnull=True)
            .order_by(Lower("name"))
        )

    def get_filtered_company_licenses_for_owner(
        self, company: "Company", user: User
    ):
        return (
            self.get_queryset()
            .filter(company=company, created_by=user)
            .order_by(Lower("name"))
        )


class BusinessID(BaseModel):
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, related_name="business_ids"
    )
    type_id = models.CharField(max_length=255, default="")
    number_id = models.CharField(max_length=255, default="")

    def __str__(self):
        return f"{self.type_id}: {self.number_id}"


class Company(BaseModel, BaseAddress):
    officers = models.QuerySet["Officer"]
    licenses = models.QuerySet["Licenses"]
    insurance_set = models.QuerySet["Insurance"]

    objects: CompanyManager = CompanyManager()
    name = models.CharField(max_length=255, blank=True, null=True)
    tax_id = models.CharField(max_length=255, blank=True, null=True)
    email = models.CharField(max_length=255)
    website = models.CharField(max_length=255, blank=True, null=True)
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    logo = models.ImageField(upload_to="company/logo", blank=True, null=True)

    def __str__(self):
        return f"{self.name} - {self.user.email}"


class Officer(BaseModel, BaseAddress):
    objects = OfficerManager()
    title = models.CharField(max_length=255, blank=True, null=True, default="")
    firstname = models.CharField(max_length=255, default="")
    lastname = models.CharField(max_length=255)
    job_position = models.CharField(
        max_length=255, blank=True, null=True, default=""
    )
    mobile_number = models.CharField(max_length=255, blank=True, null=True)
    extension = models.CharField(
        max_length=5, null=True, blank=True, default=""
    )
    email = models.EmailField(max_length=255, blank=True, null=True)
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, related_name="officers"
    )

    def __str__(self):
        return self.firstname


class Licenses(DocumentBaseMixin, BaseModel):
    objects: LicensesManager = LicensesManager()

    class LicenseType:
        # Define your license types here
        CONTRACTOR_LICENSE = "contractor_license"
        BUSINESS_LICENSE = "business_license"
        OTHER = "other"

        ALL = (CONTRACTOR_LICENSE, BUSINESS_LICENSE, OTHER)

        CHOICES = (
            (CONTRACTOR_LICENSE, "Contractor License"),
            (BUSINESS_LICENSE, "Business License"),
            (OTHER, "Other"),
        )

    license_type = models.CharField(
        max_length=255, choices=LicenseType.CHOICES
    )
    custom_license_type = models.CharField(
        max_length=255, null=True, blank=True
    )
    name = models.CharField(max_length=255)
    license_number = models.CharField(max_length=255)
    valid_from = models.DateField()
    valid_to = models.DateTimeField()
    reminder = models.DateTimeField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, related_name="licenses"
    )
    attachment = models.ForeignKey(
        File,
        related_name="licenses",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )

    owner_contact = models.ForeignKey(
        Subcontractor,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="licenses",
    )

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.clear_cache()

    def delete(self, *args, **kwargs):
        super().delete(*args, **kwargs)
        self.clear_cache()

    def clear_cache(self):
        try:

            from core.dependency_injection import service_locator

            service_locator.company_service.clear_license_response_cache(
                company_id=self.company.id
            )
        except Exception as e:
            logging.warning(f"Failed to clear insurance cache: {e}")


class Insurance(DocumentBaseMixin, BaseModel):
    objects: InsuranceManager = InsuranceManager()

    class InsuranceType:
        GENERAL_LIABILITY = "general_liability"
        WORKERS_COMPENSATION = "workers_compensation"
        OTHER = "other"

        ALL = (GENERAL_LIABILITY, WORKERS_COMPENSATION, OTHER)

        CHOICES = (
            (GENERAL_LIABILITY, "General Liability"),
            (WORKERS_COMPENSATION, "Workers Compensation"),
            (OTHER, "Other"),
        )

    insurance_type = models.CharField(
        max_length=255, choices=InsuranceType.CHOICES
    )
    custom_insurance_type = models.CharField(
        max_length=255, null=True, blank=True
    )
    carrier = models.CharField(max_length=255, blank=True, null=True)
    broker = models.CharField(max_length=255, blank=True, null=True)
    agent = models.CharField(max_length=255, blank=True, null=True)
    contact = models.CharField(max_length=255, blank=True, null=True)
    extension = models.CharField(
        max_length=5, null=True, blank=True, default=""
    )
    email = models.EmailField(max_length=255, blank=True, null=True)
    valid_from = models.DateField()
    valid_to = models.DateTimeField()
    reminder = models.DateTimeField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    policy = models.ForeignKey(
        File,
        related_name="insurance",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    policy_number = models.CharField(max_length=255, blank=True, null=True)

    owner_contact = models.ForeignKey(
        Subcontractor,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="insurances",
    )

    def __str__(self):
        return f"Insurance - {self.insurance_type} - {self.company.name}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.clear_cache()

    def delete(self, *args, **kwargs):
        super().delete(*args, **kwargs)
        self.clear_cache()

    def clear_cache(self):
        try:
            from core.dependency_injection import service_locator

            service_locator.company_service.clear_insurance_response_cache(
                company_id=self.company.id
            )
        except Exception as e:
            logging.warning(f"Failed to clear insurance cache: {e}")


class MemberManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_company_members(self, company):
        return self.get_queryset().filter(company=company)

    def get_active_invitations(self):
        return self.get_queryset().filter(
            invitation_expires_at__gt=timezone.now(),
            status=Member.STATUS_PENDING,
        )

    def get_invites_that_expire_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            status=Member.STATUS_PENDING,
            created_at__range=[
                start_of_day - timezone.timedelta(days=7),
                end_of_day - timezone.timedelta(days=7),
            ],
        )

    def get_invites_that_need_to_remind_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            status=Member.STATUS_PENDING,
            created_at__range=[
                start_of_day - timezone.timedelta(days=6),
                end_of_day - timezone.timedelta(days=6),
            ],
        )

    def get_invites_to_delete(self):
        fourteen_days_ago = timezone.now() - timezone.timedelta(days=14)
        return self.get_queryset().filter(
            status=Member.STATUS_EXPIRED, created_at__lte=fourteen_days_ago
        )

    def get_accepted_member_companies(self, user):
        return (
            self.get_queryset()
            .filter(user=user, status=Member.STATUS_ACCEPTED)
            .values_list("company", flat=True)
        )


class ModulePermission:
    """Available modules with their choices."""

    COMPANY = "company"
    OFFICERS = "officers"
    LICENSES = "licenses"
    INSURANCE = "insurance"
    RESOURCES = "resources"
    STORAGE = "storage"
    CALENDAR = "calendar"
    CONTACTS = "contacts"
    SUBCONTRACTORS = "subcontractors"
    CERTIFICATES = "certificate"

    CHOICES = [
        (COMPANY, "Company"),
        (OFFICERS, "Officers"),
        (LICENSES, "Licenses"),
        (INSURANCE, "Insurance"),
        (RESOURCES, "Resources"),
        (STORAGE, "Storage"),
        (CALENDAR, "Calendar"),
        (CONTACTS, "Contacts"),
        (SUBCONTRACTORS, "Subcontractors"),
        (CERTIFICATES, "Certificate"),
    ]


class Member(BaseModel):
    """Represents a member with a role and permissions."""

    STATUS_PENDING = "pending"
    STATUS_ACCEPTED = "accepted"
    STATUS_REJECTED = "rejected"
    STATUS_EXPIRED = "expired"

    STATUS_CHOICES = [
        (STATUS_PENDING, "Pending"),
        (STATUS_ACCEPTED, "Accepted"),
        (STATUS_REJECTED, "Rejected"),
        (STATUS_EXPIRED, "Expired"),
    ]

    # Role choices embedded directly into the Member model
    ROLE_GUEST = "guest"
    # Add more roles as needed

    ROLE_CHOICES = [
        (ROLE_GUEST, "Guest"),
        # Add more role choices as needed
    ]

    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, related_name="members"
    )
    user = models.ForeignKey(
        get_user_model(), on_delete=models.CASCADE, related_name="memberships"
    )
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING
    )
    role = models.CharField(
        max_length=20, choices=ROLE_CHOICES, default=ROLE_GUEST
    )
    invitation_expires_at = models.DateTimeField(blank=True, null=True)
    created_by = models.ForeignKey(
        get_user_model(),
        on_delete=models.CASCADE,
        related_name="created_members",
    )

    objects = MemberManager()

    def has_module_permission(self, module, access_level="read"):
        if self.status != self.STATUS_ACCEPTED:
            return False

        permissions = RolePermissionConfig.get_permissions(self.role)
        module_permission = permissions.get(module, AccessLevel.READ)

        if access_level == AccessLevel.READ:
            return module_permission in [AccessLevel.READ, AccessLevel.WRITE]
        return module_permission == AccessLevel.WRITE


class AccessLevel:
    """Define access levels."""

    READ = "read"
    WRITE = "write"

    CHOICES = [
        (READ, "Read"),
        (WRITE, "Write"),
    ]


class RolePermissionConfig:
    """Configuration of permissions per role."""

    ROLE_PERMISSIONS = {
        Member.ROLE_GUEST: {
            ModulePermission.COMPANY: AccessLevel.READ,
            ModulePermission.OFFICERS: AccessLevel.READ,
            ModulePermission.LICENSES: AccessLevel.READ,
            ModulePermission.INSURANCE: AccessLevel.READ,
            ModulePermission.RESOURCES: AccessLevel.READ,
            ModulePermission.STORAGE: AccessLevel.READ,
            ModulePermission.CALENDAR: AccessLevel.READ,
            ModulePermission.CONTACTS: AccessLevel.READ,
            ModulePermission.SUBCONTRACTORS: AccessLevel.READ,
            ModulePermission.CERTIFICATES: AccessLevel.READ,
        }
    }

    @classmethod
    def get_permissions(cls, role):
        """Return the permissions for a given role."""
        return cls.ROLE_PERMISSIONS.get(role, {})
