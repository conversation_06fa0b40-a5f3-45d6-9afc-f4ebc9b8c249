from company.models import AccessLevel
from company.models import Company
from company.models import Member
from general.models import Setting
from rest_framework import permissions


class CompanyModulePermission(permissions.BasePermission):
    def has_permission(self, request, view):
        user = request.user
        if not user.is_authenticated:
            return False

        # Get the company ID from user's settings
        user_setting = Setting.objects.filter(user=user).first()
        settings_company_id = user_setting.company if user_setting else None

        company_id = settings_company_id

        if not company_id:
            return False

        company = Company.objects.filter(id=company_id).first()
        if not company:
            return False
        # Check if the user is the company owner
        if company.user == user:
            return True

        # Check if the user is an invited member
        member = Member.objects.filter(
            user=user, company=company, status=Member.STATUS_ACCEPTED
        ).first()
        if not member:
            return False

        # Get the module name from the view
        module_name = getattr(view, "module_name", None)
        if not module_name:
            return False

        access_level = (
            AccessLevel.WRITE
            if request.method not in permissions.SAFE_METHODS
            else AccessLevel.READ
        )
        return member.has_module_permission(module_name, access_level)

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)
