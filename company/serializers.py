# create a serializer for the Company model
import logging
from datetime import date
from datetime import datetime

from accounts.models import User
from accounts.serializers import PhoneNumberField
from accounts.serializers import UserSerializer
from company.models import BusinessID
from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.models import Officer
from core.serializers import TimezoneConverterMixin
from dateutil import parser
from django.shortcuts import get_object_or_404
from drf_extra_fields.fields import Base64ImageField
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied
from storage.models import File
from storage.serializers import FileSerializer

from .models import Member

logger = logging.getLogger(__name__)


class BusinessIDSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = BusinessID
        read_only_fields = ("created", "updated", "company")
        exclude = ("is_deleted",)


class CompanySerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    logo = Base64ImageField(required=False)
    business_ids = BusinessIDSerializer(many=True, required=False)

    class Meta:
        model = Company
        read_only_fields = ("created", "updated", "user")
        exclude = ("is_deleted",)

    def create(self, validated_data):
        business_ids_data = validated_data.pop("business_ids", [])
        company = Company.objects.create(**validated_data)
        for business_id_data in business_ids_data:
            BusinessID.objects.create(company=company, **business_id_data)
        return company

    def update(self, instance, validated_data):
        business_ids_data = validated_data.pop("business_ids", [])
        instance = super().update(instance, validated_data)

        # Delete existing business IDs that are not present in the payload
        instance.business_ids.exclude(
            type_id__in=[item.get("type_id") for item in business_ids_data]
        ).delete()

        # Update existing business IDs or create new ones
        for business_id_data in business_ids_data:
            business_id, created = BusinessID.objects.get_or_create(
                company=instance, type_id=business_id_data.get("type_id")
            )
            business_id.number_id = business_id_data.get("number_id")
            business_id.save()

        return instance


class CompanySummarySerializer(serializers.ModelSerializer):
    total_number_of_officers = serializers.SerializerMethodField()
    total_number_of_licenses = serializers.SerializerMethodField()
    total_number_of_insurances = serializers.SerializerMethodField()
    total_number_of_projects = serializers.SerializerMethodField()

    class Meta:
        model = Company
        read_only_fields = ("created", "updated", "user")
        exclude = ("is_deleted",)

    def get_total_number_of_officers(self, obj: Company) -> int:
        return obj.officers.all().count()

    def get_total_number_of_licenses(self, obj: Company) -> int:
        return obj.licenses.all().count()

    def get_total_number_of_insurances(self, obj: Company) -> int:
        return obj.insurance_set.all().count()

    def get_total_number_of_projects(self, obj: Company) -> int:
        from project.models import Project

        return Project.objects.filter(created_by=obj.user).count()


class OfficerSerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    company = CompanySerializer(read_only=True)
    formatted_mobile = PhoneNumberField(source="mobile_number")

    class Meta:
        model = Officer
        fields = "__all__"
        read_only_fields = ("created", "updated")

    def create(self, validated_data):
        company_id = self.context["view"].kwargs["company_id"]
        user = self.context["request"].user
        # get the company object or 404
        company = get_object_or_404(Company, pk=company_id, user=user)

        validated_data["company"] = company
        return super().create(validated_data)


class OfficerCreateSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    formatted_mobile = PhoneNumberField(source="mobile_number", required=False)

    class Meta:
        model = Officer
        exclude = ("is_deleted",)

    def create(self, validated_data):
        company_id = self.context["view"].kwargs["company_id"]
        user = self.context["request"].user
        company = get_object_or_404(Company, pk=company_id, user=user)

        validated_data["company"] = company
        return super().create(validated_data)


class OfficerListSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    formatted_mobile = PhoneNumberField(source="mobile_number", read_only=True)

    class Meta:
        model = Officer
        fields = [
            "id",
            "title",
            "firstname",
            "lastname",
            "job_position",
            "mobile_number",
            "formatted_mobile",
            "extension",
            "email",
            "company",
        ]
        read_only_fields = fields


class DateTimeToDateField(serializers.Field):
    """Field that can accept both date and datetime values and converts to date."""

    def to_internal_value(self, value):
        if not value:
            return None

        # If it's already a date object, return it
        if isinstance(value, date) and not isinstance(value, datetime):
            return value

        # If it's a datetime object, convert to date
        if isinstance(value, datetime):
            return value.date()

        # Try to parse value as string
        try:
            # First try to parse as date
            return datetime.strptime(value, "%Y-%m-%d").date()
        except (ValueError, TypeError):
            try:
                # Then try to parse as datetime
                parsed_datetime = parser.parse(value)
                return parsed_datetime.date()
            except (ValueError, TypeError) as e:
                raise serializers.ValidationError from e

    def to_representation(self, value):
        if value:
            return value.isoformat()
        return None


class FileOperation:
    ADD = "add"
    REMOVE = "remove"
    UPDATE_NAMES = "update_names"
    CHOICES = ((ADD, ADD), (REMOVE, REMOVE), (UPDATE_NAMES, UPDATE_NAMES))


class FileOperationSerializer(serializers.Serializer):
    operation = serializers.ChoiceField(choices=FileOperation.CHOICES)
    files = serializers.ListField(
        child=serializers.UUIDField(), allow_empty=True
    )
    # Add filename field (optional)
    filenames = serializers.DictField(
        child=serializers.CharField(max_length=1024),
        required=False,
        help_text="Dict mapping file IDs to new filenames",
    )


class AttachmentHandlerMixin:
    def _filter_valid_files(self, file_ids):
        """Retrieve non-deleted files based on provided IDs."""
        return File.objects.filter(id__in=file_ids, is_deleted=False)

    def _update_file_names(self, files_dict):
        """Update file names if provided in the files_dict."""
        if not files_dict:
            return

        for file_id, filename in files_dict.items():
            if not filename or not filename.strip():
                continue

            try:
                file = File.objects.get(id=file_id, is_deleted=False)
                file.original_file_name = filename
                file.save(update_fields=["original_file_name"])
            except File.DoesNotExist:
                continue

    def _handle_attachment_operations(self, instance, operations):
        """Handle add/remove/update operations for additional attachments."""
        operation_actions = {
            FileOperation.ADD: instance.additional_attachments.add,
            FileOperation.REMOVE: instance.additional_attachments.remove,
            # No action for UPDATE_NAMES as we just update filenames
            FileOperation.UPDATE_NAMES: None,
        }

        for operation in operations:
            try:
                op_type = operation.get("operation")
                file_ids = operation.get("files", [])
                filenames = operation.get("filenames", {})

                if not op_type:
                    logger.warning(f"Invalid operation format: {operation}")
                    continue

                # For UPDATE_NAMES operation, we just update filenames
                if op_type == FileOperation.UPDATE_NAMES:
                    if filenames:
                        # Filter to only files that are already attached to this instance
                        attached_files = (
                            instance.additional_attachments.filter(
                                id__in=file_ids, is_deleted=False
                            )
                        )
                        attached_ids = [str(f.id) for f in attached_files]

                        # Only update filenames for files that are attached
                        filtered_filenames = {
                            fid: name
                            for fid, name in filenames.items()
                            if fid in attached_ids and name and name.strip()
                        }
                        self._update_file_names(filtered_filenames)
                    continue

                if not file_ids:
                    logger.warning(
                        f"No files specified for operation: {operation}"
                    )
                    continue

                action = operation_actions.get(op_type)
                if not action:
                    logger.warning(f"Unsupported operation type: {op_type}")
                    continue

                valid_files = self._filter_valid_files(file_ids)
                if not valid_files.exists():
                    logger.warning(
                        f"No valid files found for operation: {operation}"
                    )
                    continue

                # First perform the attachment operation
                action(*valid_files)

                # Then update filenames if provided and it's an ADD operation
                if op_type == FileOperation.ADD and filenames:
                    # Filter out empty filenames
                    filtered_filenames = {
                        fid: name
                        for fid, name in filenames.items()
                        if name and name.strip()
                    }
                    self._update_file_names(filtered_filenames)

            except Exception as e:
                logger.error(
                    f"Error processing attachment operation: {str(e)}"
                )
                continue

    def validate_additional_attachments(self, value):
        """Validate that all files exist and are not deleted."""
        if not value:
            return value

        all_file_ids = []
        for operation in value:
            all_file_ids.extend(operation["files"])

        if all_file_ids:
            existing_files = self._filter_valid_files(all_file_ids)
            existing_ids = {str(x.id) for x in existing_files}
            missing_ids = {str(x) for x in all_file_ids} - existing_ids

            if missing_ids:
                raise serializers.ValidationError(
                    f"Some files do not exist or are deleted: {missing_ids}"
                )

        return value


class BaseLicensesSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = Licenses
        read_only_fields = (
            "created",
            "updated",
            "created_by",
            "owner_contact",
        )
        ordering = ("name",)
        exclude = ("is_deleted",)


class LicensesV2Serializer(BaseLicensesSerializer):
    pass


class LicensesSerializer(BaseLicensesSerializer, AttachmentHandlerMixin):
    valid_from = DateTimeToDateField()
    company = CompanySerializer(read_only=True)
    file = serializers.SerializerMethodField()
    attachment = serializers.UUIDField(required=False, write_only=True)
    attachment_filename = serializers.CharField(
        required=False, write_only=True
    )
    additional_attachments = serializers.ListField(
        child=FileOperationSerializer(), required=False, write_only=True
    )
    all_attachments = serializers.SerializerMethodField()
    id = serializers.UUIDField(required=False)

    def create(self, validated_data):
        company_id = self.context["view"].kwargs["company_id"]
        user = self.context["request"].user
        company = get_object_or_404(Company, pk=company_id, user=user)

        # Handle attachment
        attachment_id = validated_data.pop("attachment", None)
        attachment_filename = validated_data.pop("attachment_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        validated_data["company"] = company
        validated_data["created_by"] = user

        instance = super().create(validated_data)

        # Attach main file if provided
        if attachment_id:
            attachment = get_object_or_404(
                File, pk=attachment_id, is_deleted=False
            )
            instance.attachment = attachment

            # Update filename if provided and not empty
            if attachment_filename and attachment_filename.strip():
                attachment.original_file_name = attachment_filename
                attachment.save(update_fields=["original_file_name"])

            instance.save()

        # Handle additional attachments if provided
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)

        return instance

    def update(self, instance, validated_data):
        # Handle attachment and additional attachments
        attachment_id = validated_data.pop("attachment", None)
        attachment_filename = validated_data.pop("attachment_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        # Check if the new attachment ID is different from the current one before archiving
        if (
            attachment_id
            and instance.attachment
            and str(instance.attachment.id) != str(attachment_id)
        ):
            old_attachment = instance.attachment
            # Archive the old file by calling its archive method.
            old_attachment.archive()
            # Add the archived file to the additional attachments.
            instance.additional_attachments.add(old_attachment)

        instance = super().update(instance, validated_data)

        # If a new attachment is provided, retrieve it and assign.
        if attachment_id:
            new_attachment = get_object_or_404(
                File, pk=attachment_id, is_deleted=False
            )

            # Update filename if provided and not empty
            if attachment_filename and attachment_filename.strip():
                new_attachment.original_file_name = attachment_filename
                new_attachment.save(update_fields=["original_file_name"])

            # Only set the attachment if it's different or not set
            if not instance.attachment or str(instance.attachment.id) != str(
                attachment_id
            ):
                instance.attachment = new_attachment
                instance.save()
        # Update filename of existing attachment if no new attachment is provided
        elif (
            attachment_filename
            and attachment_filename.strip()
            and instance.attachment
        ):
            instance.attachment.original_file_name = attachment_filename
            instance.attachment.save(update_fields=["original_file_name"])

        # Handle additional attachments operations
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)
        return instance

    def get_file(self, obj: Licenses):
        if not obj.attachment or obj.attachment.is_deleted:
            return None
        return FileSerializer(obj.attachment).data

    def get_all_attachments(self, obj: Licenses):
        attachments = list(obj.additional_attachments.filter(is_deleted=False))
        return FileSerializer(attachments, many=True).data


class BaseInsuranceSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = Insurance
        read_only_fields = (
            "created",
            "updated",
            "created_by",
            "owner_contact",
        )
        exclude = ("is_deleted",)


class InsuranceSerializer(BaseInsuranceSerializer, AttachmentHandlerMixin):
    valid_from = DateTimeToDateField()
    company = CompanySerializer(read_only=True)
    file = serializers.SerializerMethodField()
    policy = serializers.UUIDField(required=False, write_only=True)
    policy_filename = serializers.CharField(required=False, write_only=True)
    additional_attachments = serializers.ListField(
        child=FileOperationSerializer(), required=False, write_only=True
    )
    all_attachments = serializers.SerializerMethodField()
    id = serializers.UUIDField(required=False)

    def validate_policy(self, value):
        """Validate that the policy file exists and is not deleted."""
        if (
            value
            and not File.objects.filter(id=value, is_deleted=False).exists()
        ):
            raise serializers.ValidationError(
                "The specified policy file does not exist or is deleted"
            )
        return value

    def create(self, validated_data):
        company_id = self.context["view"].kwargs["company_id"]
        user = self.context["request"].user
        company = get_object_or_404(Company, pk=company_id, user=user)

        # Handle policy and additional attachments
        policy_id = validated_data.pop("policy", None)
        policy_filename = validated_data.pop("policy_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        validated_data["company"] = company
        validated_data["created_by"] = user

        instance = super().create(validated_data)

        # Attach policy file if provided
        if policy_id:
            policy = get_object_or_404(File, pk=policy_id, is_deleted=False)

            # Update filename if provided and not empty
            if policy_filename and policy_filename.strip():
                policy.original_file_name = policy_filename
                policy.save(update_fields=["original_file_name"])

            instance.policy = policy
            instance.save()

        # Handle additional attachments if provided
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)

        return instance

    def update(self, instance, validated_data):
        # Handle policy and additional attachments
        policy_id = validated_data.pop("policy", None)
        policy_filename = validated_data.pop("policy_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        # Check if the new policy ID is different from the current one before archiving
        if (
            policy_id
            and instance.policy
            and str(instance.policy.id) != str(policy_id)
        ):
            old_policy = instance.policy
            old_policy.archive()
            instance.additional_attachments.add(old_policy)

        instance = super().update(instance, validated_data)

        # If a new policy file is provided, retrieve it and assign.
        if policy_id:
            new_policy = get_object_or_404(
                File, pk=policy_id, is_deleted=False
            )

            # Update filename if provided and not empty
            if policy_filename and policy_filename.strip():
                new_policy.original_file_name = policy_filename
                new_policy.save(update_fields=["original_file_name"])

            # Only set the policy if it's different or not set
            if not instance.policy or str(instance.policy.id) != str(
                policy_id
            ):
                instance.policy = new_policy
                instance.save()
        # Update filename of existing policy if no new policy is provided
        elif policy_filename and policy_filename.strip() and instance.policy:
            instance.policy.original_file_name = policy_filename
            instance.policy.save(update_fields=["original_file_name"])

        # Handle additional attachments operations
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)
        return instance

    def get_file(self, obj: Insurance):
        if not obj.policy or obj.policy.is_deleted:
            return None
        return FileSerializer(obj.policy).data

    def get_all_attachments(self, obj: Insurance):
        attachments = list(obj.additional_attachments.filter(is_deleted=False))
        return FileSerializer(attachments, many=True).data


class ListInsuranceV2Serializer(BaseInsuranceSerializer):
    pass


class InvitedUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "mobile",
            "profile_picture",
        ]


class MemberInviteSerializer(serializers.ModelSerializer):
    user = serializers.SerializerMethodField()
    invitee_emails = serializers.ListField(
        child=serializers.EmailField(), write_only=True, required=False
    )
    role = serializers.ChoiceField(choices=Member.ROLE_CHOICES)
    company = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = Member
        fields = [
            "id",
            "user",
            "status",
            "role",
            "invitation_expires_at",
            "invitee_emails",
            "company",
        ]
        read_only_fields = ["status", "invitation_expires_at"]

    def get_user(self, obj):
        if isinstance(obj, Member):
            return InvitedUserSerializer(obj.user).data
        return None

    def validate(self, data):
        request = self.context["request"]
        view = self.context["view"]
        company_id = view.kwargs.get("company_id")

        user = request.user
        user_company = Company.objects.get_user_company(user)

        # Check if the company_id matches user's own company
        if user_company and str(user_company.id) == company_id:
            data["company_id"] = company_id
            return data

        # If not user's own company, check membership and role
        member = Member.objects.filter(
            company_id=company_id, user=user, status=Member.STATUS_ACCEPTED
        ).first()

        if not member:
            raise PermissionDenied(
                "You can only invite members to your own company or companies where you are a member"
            )

        if member.role == Member.ROLE_GUEST:
            raise PermissionDenied(
                "Guest members cannot invite other people to the company"
            )

        data["company_id"] = company_id
        return data

    def validate_invitee_emails(self, value):
        if len(value) > 10:
            raise serializers.ValidationError(
                "Cannot invite more than 10 users at once."
            )

        current_user_email = self.context["request"].user.email
        if current_user_email in value:
            raise serializers.ValidationError(
                "Cannot invite yourself as a member."
            )

        users = []
        user_status = []  # Track if each user is newly created or existing
        for email in value:
            user, created = User.objects.get_or_create(email=email)
            users.append(user)
            user_status.append(created)  # True if new user, False if existing

        # Store user statuses in the context for use in create
        self.context["user_status"] = user_status

        return users

    def create(self, validated_data):
        invitee_users = validated_data.pop("invitee_emails")
        role = validated_data.pop("role")
        company_id = validated_data.pop("company_id")

        if not invitee_users:
            raise serializers.ValidationError(
                "No valid invitee emails provided."
            )

        invitee_user = invitee_users[0]  # Creating one invite at a time
        try:
            member, created = Member.objects.get_or_create(
                company_id=company_id,
                user=invitee_user,
                defaults={
                    **validated_data,
                    "role": role,
                    "created_by": self.context["request"].user,
                },
            )
        except Member.MultipleObjectsReturned:
            # Handle duplicate invites
            members = Member.objects.filter(
                company_id=company_id, user=invitee_user
            )
            member = members.first()
            members.exclude(pk=member.pk).delete()

        return member


class CompanyOwnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "first_name", "last_name", "profile_picture"]


class CompanyMembershipSerializer(serializers.ModelSerializer):
    user = serializers.SerializerMethodField()

    class Meta:
        model = Member
        fields = [
            "id",
            "role",
            "user",
            "status",
        ]

    def get_user(self, obj):
        if isinstance(obj, Member):
            return InvitedUserSerializer(obj.user).data
        return None


class UserCompanySerializer(serializers.ModelSerializer):
    owner = CompanyOwnerSerializer(source="user", read_only=True)
    membership_details = serializers.SerializerMethodField()
    company_type = serializers.SerializerMethodField()

    class Meta:
        model = Company
        fields = [
            "id",
            "logo",
            "name",
            "owner",
            "membership_details",
            "company_type",
        ]

    def get_membership_details(self, obj):
        request = self.context.get("request")
        if not request:
            return None

        membership = Member.objects.filter(
            company=obj, user=request.user, status=Member.STATUS_ACCEPTED
        ).first()

        return (
            CompanyMembershipSerializer(membership).data
            if membership
            else None
        )

    def get_company_type(self, obj):
        request = self.context.get("request")
        if not request:
            return None

        if obj.user == request.user:
            return "owned"

        membership = Member.objects.filter(
            company=obj, user=request.user, status=Member.STATUS_ACCEPTED
        ).first()

        return "member" if membership else None
