import hashlib
import logging

from crum import get_current_user
from django.conf import settings
from django.core.cache import cache
from general.cache_keys import REDIS_CACHE_KEY

logger = logging.getLogger(__name__)


class CompanyService:
    CACHE_VERSION = "v2"

    @property
    def current_user(self):
        user = get_current_user()
        if not user:
            return

        return str(user.id)

    def _generate_cache_key(
        self,
        company_id,
        query_params=None,
        key_type="list",
        item_id=None,
    ):
        """Generate a consistent cache key with collision resistance."""
        # Sort query params for consistent hashing
        if query_params:
            sorted_params = sorted(query_params.items())
            params_str = "&".join(f"{k}={v}" for k, v in sorted_params)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        else:
            params_hash = "default"

        # Include item_id in cache key if provided
        if item_id:
            return f"{key_type}_{self.CACHE_VERSION}_{self.current_user}_{company_id}_{item_id}_{params_hash}"
        else:
            return f"{key_type}_{self.CACHE_VERSION}_{self.current_user}_{company_id}_{params_hash}"

    # Insurance caching methods
    def cache_insurance_response(self, request, response_data, company_id):
        """Cache insurance list response with improved error handling."""

        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                company_id,
                request.query_params,
                key_type="insurance_list",
            )

            cache.set(
                cache_key, response_data, settings.INSURANCE_CACHE_TIMEOUT
            )
        except Exception as e:
            logger.warning(f"Failed to cache insurance response: {e}")

    def get_cached_insurance_response(self, request, company_id):
        """Retrieve cached insurance list response."""
        try:

            cache_key = self._generate_cache_key(
                company_id,
                request.query_params,
                key_type="insurance_list",
            )

            return cache.get(cache_key)
        except Exception as e:
            logger.warning(
                f"Failed to retrieve cached insurance response: {e}"
            )
            return None

    def get_cached_insurance_response_with_fallback(self, request, company_id):
        """
        Retrieve cached insurance response with comprehensive error handling and cache corruption cleanup.

        Returns:
            tuple: (cached_data, cache_status)
            - cached_data: The cached response data or None if not found/error
            - cache_status: 'hit', 'miss', or 'error'
        """
        try:

            cached_data = self.get_cached_insurance_response(
                request, company_id
            )

            if cached_data is not None:
                logger.debug(
                    f"Cache hit for insurance list - company: {company_id}"
                )
                return cached_data, "hit"
            else:
                logger.debug(
                    f"Cache miss for insurance list - company: {company_id}"
                )
                return None, "miss"
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                self.clear_insurance_cache(company_id)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted insurance cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached insurance, falling back to DB: {e}"
            )
            return None, "error"

    def cache_insurance_response_with_logging(
        self, request, response_data, company_id
    ):
        """Cache insurance response with detailed logging."""

        try:

            self.cache_insurance_response(request, response_data, company_id)

            logger.debug(f"Cached insurance list - company: {company_id}")
        except Exception as e:

            logger.warning(f"Failed to cache insurance response: {e}")

    def cache_insurance(self, company_id, insurance_data):
        """Cache insurance queryset data."""

        try:
            key = REDIS_CACHE_KEY.get_insurance_key(
                self.current_user, str(company_id)
            )
            cache.set(key, insurance_data, settings.INSURANCE_CACHE_TIMEOUT)
        except Exception as e:
            logger.warning(f"Failed to cache insurance: {e}")

    def get_cached_insurance(self, company_id):
        """Retrieve cached insurance queryset data."""
        try:
            key = REDIS_CACHE_KEY.get_insurance_key(
                self.current_user, str(company_id)
            )
            return cache.get(key)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached insurance: {e}")
            return None

    def clear_insurance_cache(self, company_id):
        """Clear insurance cache with improved efficiency and error handling."""
        try:
            # Clear queryset cache
            key = REDIS_CACHE_KEY.get_insurance_key(
                self.current_user, str(company_id)
            )
            cache.delete(key)

            # Clear response cache
            self.clear_insurance_response_cache(company_id)

            logger.debug(
                f"Cleared insurance cache - user: {self.current_user}, company: {company_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to clear insurance cache: {e}")

    def clear_insurance_response_cache(self, company_id):
        """Clear insurance response cache for a company."""
        try:
            if hasattr(cache, "delete_pattern"):
                # Use pattern deletion if available (Redis)
                pattern = f"insurance_list_{self.CACHE_VERSION}_{self.current_user}_{company_id}_*"
                cache.delete_pattern(pattern)
            else:
                # Fallback for other cache backends
                # Try to delete common cache keys
                base_pattern = f"insurance_list_{self.CACHE_VERSION}_{self.current_user}_{company_id}_"
                common_keys = [f"{base_pattern}default"]

                for key in common_keys:
                    try:
                        cache.delete(key)
                    except Exception as e:
                        logger.warning(
                            f"Failed to delete cache key {key}: {e}"
                        )

                logger.info(
                    f"Pattern deletion not available, some insurance caches may persist for user {self.current_user}, company {company_id}"
                )

        except Exception as e:
            logger.warning(f"Failed to clear insurance response cache: {e}")

    # License caching methods
    def cache_license_response(self, request, response_data, company_id):
        """Cache license list response with improved error handling."""
        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                company_id,
                request.query_params,
                key_type="license_list",
            )
            cache.set(cache_key, response_data, settings.LICENSE_CACHE_TIMEOUT)
        except Exception as e:
            logger.warning(f"Failed to cache license response: {e}")

    def get_cached_license_response(self, request, company_id):
        """Retrieve cached license list response."""
        try:
            cache_key = self._generate_cache_key(
                company_id,
                request.query_params,
                key_type="license_list",
            )
            return cache.get(cache_key)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached license response: {e}")
            return None

    def get_cached_license_response_with_fallback(self, request, company_id):
        """
        Retrieve cached license response with comprehensive error handling and cache corruption cleanup.

        Returns:
            tuple: (cached_data, cache_status)
            - cached_data: The cached response data or None if not found/error
            - cache_status: 'hit', 'miss', or 'error'
        """
        try:
            cached_data = self.get_cached_license_response(request, company_id)
            if cached_data is not None:
                logger.debug(
                    f"Cache hit for license list - company: {company_id}"
                )
                return cached_data, "hit"
            else:
                logger.debug(
                    f"Cache miss for license list - company: {company_id}"
                )
                return None, "miss"
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                self.clear_license_cache(company_id)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted license cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached licenses, falling back to DB: {e}"
            )
            return None, "error"

    def cache_license_response_with_logging(
        self, request, response_data, company_id
    ):
        """Cache license response with detailed logging."""
        try:
            self.cache_license_response(request, response_data, company_id)
            logger.debug(f"Cached license list - company: {company_id}")
        except Exception as e:
            logger.warning(f"Failed to cache license response: {e}")

    def cache_licenses(self, company_id, license_data):
        """Cache license queryset data."""
        try:
            key = REDIS_CACHE_KEY.get_license_key(
                self.current_user, str(company_id)
            )
            cache.set(key, license_data, settings.LICENSE_CACHE_TIMEOUT)
        except Exception as e:
            logger.warning(f"Failed to cache licenses: {e}")

    def get_cached_licenses(self, company_id):
        """Retrieve cached license queryset data."""
        try:
            key = REDIS_CACHE_KEY.get_license_key(
                self.current_user, str(company_id)
            )
            return cache.get(key)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached licenses: {e}")
            return None

    def clear_license_cache(self, company_id):
        """Clear license cache with improved efficiency and error handling."""
        try:
            # Clear queryset cache
            key = REDIS_CACHE_KEY.get_license_key(
                self.current_user, str(company_id)
            )
            cache.delete(key)

            # Clear response cache
            self.clear_license_response_cache(company_id)

            logger.debug(
                f"Cleared license cache - user: {self.current_user}, company: {company_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to clear license cache: {e}")

    def clear_license_response_cache(self, company_id):
        """Clear license response cache for a company."""
        try:
            if hasattr(cache, "delete_pattern"):
                # Use pattern deletion if available (Redis)
                pattern = f"license_list_{self.CACHE_VERSION}_{self.current_user}_{company_id}_*"
                cache.delete_pattern(pattern)
            else:
                # Fallback for other cache backends
                # Try to delete common cache keys
                base_pattern = f"license_list_{self.CACHE_VERSION}_{self.current_user}_{company_id}_"
                common_keys = [f"{base_pattern}default"]

                for key in common_keys:
                    try:
                        cache.delete(key)
                    except Exception as e:
                        logger.warning(
                            f"Failed to delete cache key {key}: {e}"
                        )

                logger.info(
                    f"Pattern deletion not available, some license caches may persist for user {self.current_user}, company {company_id}"
                )

        except Exception as e:
            logger.warning(f"Failed to clear license response cache: {e}")
