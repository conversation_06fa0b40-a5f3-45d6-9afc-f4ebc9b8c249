from datetime import timedelta

import factory
from accounts.factories import UserFactory
from django.utils import timezone

from .mixins import DocumentBaseMixin
from .models import BusinessID
from .models import Company
from .models import Insurance
from .models import Licenses
from .models import Member
from .models import Officer


class CompanyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Company

    user = factory.SubFactory("accounts.factories.UserFactory")
    name = factory.Faker("company")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("secondary_address")
    city = factory.Faker("city")
    state = factory.Faker("state_abbr")
    zip_code = factory.Faker("zipcode")
    country = factory.Faker("country")
    tax_id = factory.Faker("ssn")
    email = factory.Faker("email")
    website = factory.Faker("url")


class BusinessIDFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BusinessID

    company = factory.SubFactory(CompanyFactory)
    type_id = factory.Faker("name")
    number_id = factory.Faker("random_int")


class OfficerFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Officer

    company = factory.SubFactory(CompanyFactory)
    title = factory.Faker("job")
    firstname = factory.Faker("first_name")
    lastname = factory.Faker("last_name")
    mobile_number = factory.Faker("phone_number")
    extension = factory.Faker("random_int")
    email = factory.Faker("email")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("secondary_address")
    city = factory.Faker("city")
    state = factory.Faker("state_abbr")
    zip_code = factory.Faker("zipcode")
    country = factory.Faker("country")


class LicensesFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Licenses

    company = factory.SubFactory(CompanyFactory)
    license_type = factory.Iterator(
        [choice[0] for choice in Licenses.LicenseType.CHOICES]
    )
    name = factory.Faker("name")
    license_number = factory.Faker("ssn")
    valid_from = factory.Faker("date")
    valid_to = factory.Faker("date")
    reminder = factory.Faker("date")
    created_by = factory.SubFactory("accounts.factories.UserFactory")
    status = DocumentBaseMixin.Status.ACTIVE

    @factory.post_generation
    def additional_attachments(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for attachment in extracted:
                self.additional_attachments.add(attachment)


class InsuranceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Insurance

    company = factory.SubFactory(CompanyFactory)
    insurance_type = factory.Iterator(
        [choice[0] for choice in Insurance.InsuranceType.CHOICES]
    )
    carrier = factory.Faker("company")
    agent = factory.Faker("name")
    contact = factory.Faker("phone_number")
    extension = factory.Faker("random_int", min=1000, max=9999)
    email = factory.Faker("email")
    valid_from = factory.Faker("date")
    valid_to = factory.Faker("date")
    reminder = factory.Faker("date")
    created_by = factory.SubFactory("accounts.factories.UserFactory")
    policy = factory.SubFactory("storage.factories.FileFactory")
    status = DocumentBaseMixin.Status.ACTIVE

    @factory.post_generation
    def additional_attachments(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for attachment in extracted:
                self.additional_attachments.add(attachment)


class MemberFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Member

    user = factory.SubFactory(UserFactory)
    company = factory.SubFactory(CompanyFactory)
    created_by = factory.SubFactory(UserFactory)
    role = Member.ROLE_GUEST
    status = Member.STATUS_PENDING
    invitation_expires_at = factory.LazyFunction(
        lambda: timezone.now() + timedelta(days=7)
    )
