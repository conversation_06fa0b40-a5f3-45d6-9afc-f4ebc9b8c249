# create url for the CompanyViewSet
from company.views import CompanyRetrieveUpdateDeleteView
from company.views import CreateMemberInviteView
from company.views import InsuranceCreateListView
from company.views import InsuranceRetrieveUpdateDeleteView
from company.views import LicenseCreateListView
from company.views import LicenseRetrieveUpdateDeleteView
from company.views import ListMemberInvitationsReceivedView
from company.views import ListMemberInvitationsSentView
from company.views import MemberInviteStatusUpdateView
from company.views import OfficerCreateListView
from company.views import OfficerRetrieveUpdateDeleteView
from company.views import UserCompanyListView
from django.urls import path

app_name = "company"
urlpatterns = [
    path(
        "<pk>/",
        CompanyRetrieveUpdateDeleteView.as_view(),
        name="company",
    ),
    path(
        "<company_id>/officers/",
        OfficerCreateListView.as_view(),
        name="officers",
    ),
    path(
        "<company_id>/officers/<pk>/",
        OfficerRetrieveUpdateDeleteView.as_view(),
        name="officer",
    ),
    path(
        "<company_id>/licenses/",
        LicenseCreateListView.as_view(),
        name="licenses",
    ),
    path(
        "<company_id>/licenses/<pk>/",
        LicenseRetrieveUpdateDeleteView.as_view(),
        name="license",
    ),
    path(
        "<company_id>/insurance/",
        InsuranceCreateListView.as_view(),
        name="insurance",
    ),
    path(
        "<company_id>/insurance/<pk>/",
        InsuranceRetrieveUpdateDeleteView.as_view(),
        name="insurance",
    ),
    path(
        "members/invite/<company_id>/",
        CreateMemberInviteView.as_view(),
        name="create_member_invite",
    ),
    path(
        "members/invitations/sent/",
        ListMemberInvitationsSentView.as_view(),
        name="list_member_invitations_sent",
    ),
    path(
        "members/invitations/received/",
        ListMemberInvitationsReceivedView.as_view(),
        name="list_member_invitations_received",
    ),
    path(
        "members/invite/status/<pk>/",
        MemberInviteStatusUpdateView.as_view(),
        name="member_invite_status_update",
    ),
    path(
        "user/companies/",
        UserCompanyListView.as_view(),
        name="user-companies-list",
    ),
]
