from django.db import models
from storage.models import File


class DocumentBaseMixin(models.Model):
    class Status:
        ACTIVE = "active"
        ARCHIVED = "archived"

        CHOICES = (
            (ACTIVE, "Active"),
            (ARCHIVED, "Archived"),
        )

    status = models.CharField(
        max_length=20, choices=Status.CHOICES, default=Status.ACTIVE
    )

    additional_attachments = models.ManyToManyField(
        File, related_name="%(class)s_attachments", blank=True
    )

    class Meta:
        abstract = True

    def archive(self):
        """Archive the document by setting its status to archived"""
        self.status = self.Status.ARCHIVED
        self.save(update_fields=["status"])
