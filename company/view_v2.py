import logging

from api.mixins import VersionedAPIMixin
from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.serializers import LicensesV2Serializer
from company.serializers import ListInsuranceV2Serializer
from company.serializers import OfficerCreateSerializer
from company.serializers import OfficerListSerializer
from company.serializers import OfficerSerializer
from company.views import OfficerCreateListView
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .filters import InsuranceFilter
from .filters import LicensesFilter
from .models import ModulePermission
from .permissions import CompanyModulePermission

logger = logging.getLogger(__name__)


class OfficerCreateListViewV2(VersionedAPIMixin, OfficerCreateListView):
    """Version 2 of the OfficerCreateListView that uses separate serializers for create and list operations."""

    versioned_serializer_classes = {
        "v1": OfficerSerializer,  # Keep v1 compatibility
        "v2": OfficerCreateSerializer,  # Use new create serializer for v2
    }

    def get_serializer_class(self):
        if self.request.method == "GET":
            return OfficerListSerializer
        return OfficerCreateSerializer


class InsuranceCreateListViewV2(generics.ListAPIView):
    permission_classes = IsAuthenticated
    serializer_class = ListInsuranceV2Serializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = InsuranceFilter
    search_fields = ["carrier", "broker", "agent", "policy_number"]
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.INSURANCE

    def get_queryset(self):

        company_id = self.kwargs.get("company_id")
        company = get_object_or_404(Company, id=company_id)

        try:
            # Try to get cached insurance first
            from core.dependency_injection import service_locator

            cached_insurance = (
                service_locator.company_service.get_cached_insurance(
                    company_id
                )
            )
            if cached_insurance is not None:
                logger.debug(
                    f"Cache hit for insurance queryset - company: {company_id}"
                )
                return cached_insurance
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                from core.dependency_injection import service_locator

                service_locator.company_service.clear_insurance_cache(
                    company_id
                )
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted insurance cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached insurance, falling back to DB: {e}"
            )

        # Cache miss - get fresh data
        logger.debug(
            f"Cache miss for insurance queryset - company: {company_id}"
        )
        return self._get_and_cache_queryset(self.request, company)

    def _get_fresh_queryset(self, company):
        """Get fresh queryset without caching."""
        return Insurance.objects.get_filtered_company_insurance_for_owner(
            company, self.request.user
        )

    def _get_and_cache_queryset(self, request, company):
        """Get fresh queryset and cache it."""
        queryset = self._get_fresh_queryset(company)

        # Cache the results for future use
        try:
            from core.dependency_injection import service_locator

            service_locator.company_service.cache_insurance(
                company.id, queryset
            )
            logger.debug(f"Cached insurance queryset - company: {company.id}")
        except Exception as e:
            logger.warning(f"Failed to cache insurance queryset: {e}")

        return queryset

    def list(self, request, *args, **kwargs):
        """Override list method to implement response caching."""

        from core.dependency_injection import service_locator

        company_id = kwargs.get("company_id")

        # Check if caching is disabled
        if request.query_params.get("disable_cache"):

            return self._get_uncached_response(request, *args, **kwargs)

        # Try to get cached response with comprehensive error handling
        (
            cached_response,
            _,
        ) = service_locator.company_service.get_cached_insurance_response_with_fallback(
            request, company_id
        )

        if cached_response is not None:
            return Response(cached_response)

        # Cache miss - get fresh data
        logger.debug(f"Cache miss for insurance list - company: {company_id}")
        return self._get_and_cache_response(
            request, service_locator, *args, **kwargs
        )

    def _get_uncached_response(self, request, *args, **kwargs):
        """Get response without caching."""
        return super().list(request, *args, **kwargs)

    def _get_and_cache_response(
        self, request, service_locator, *args, **kwargs
    ):
        """Get response and cache it."""
        response = super().list(request, *args, **kwargs)
        company_id = kwargs.get("company_id")

        # Only cache successful responses
        if response.status_code == 200:
            service_locator.company_service.cache_insurance_response_with_logging(
                request, response.data, company_id
            )

        return response


class LicenseCreateListViewV2(generics.ListAPIView):
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    serializer_class = LicensesV2Serializer
    filterset_class = LicensesFilter
    search_fields = ["name", "license_number"]
    module_name = ModulePermission.LICENSES

    def get_queryset(self):
        company_id = self.kwargs.get("company_id")

        company = get_object_or_404(Company, id=company_id)

        try:
            # Try to get cached licenses first
            from core.dependency_injection import service_locator

            cached_licenses = (
                service_locator.company_service.get_cached_licenses(company_id)
            )
            if cached_licenses is not None:
                logger.debug(
                    f"Cache hit for license queryset - company: {company_id}"
                )
                return cached_licenses
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                from core.dependency_injection import service_locator

                service_locator.company_service.clear_license_cache(company_id)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted license cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached licenses, falling back to DB: {e}"
            )

        # Cache miss - get fresh data
        logger.debug(
            f"Cache miss for license queryset - company: {company_id}"
        )

        return self._get_and_cache_queryset(self.request, company)

    def _get_fresh_queryset(self, company):
        """Get fresh queryset without caching."""
        return Licenses.objects.get_filtered_company_licenses_for_owner(
            company, self.request.user
        )

    def _get_and_cache_queryset(self, request, company):
        """Get fresh queryset and cache it."""
        queryset = self._get_fresh_queryset(company)

        # Cache the results for future use
        try:
            from core.dependency_injection import service_locator

            service_locator.company_service.cache_licenses(
                company.id, queryset
            )
            logger.debug(f"Cached license queryset - company: {company.id}")
        except Exception as e:
            logger.warning(f"Failed to cache license queryset: {e}")

        return queryset

    def list(self, request, *args, **kwargs):
        """Override list method to implement response caching."""
        from core.dependency_injection import service_locator

        company_id = kwargs.get("company_id")

        # Check if caching is disabled
        if request.query_params.get("disable_cache"):
            return self._get_uncached_response(request, *args, **kwargs)

        # Try to get cached response with comprehensive error handling
        (
            cached_response,
            _,
        ) = service_locator.company_service.get_cached_license_response_with_fallback(
            request, company_id
        )

        if cached_response is not None:
            return Response(cached_response)

        # Cache miss - get fresh data
        logger.debug(f"Cache miss for license list - company: {company_id}")
        return self._get_and_cache_response(
            request, service_locator, *args, **kwargs
        )

    def _get_uncached_response(self, request, *args, **kwargs):
        """Get response without caching."""
        return super().list(request, *args, **kwargs)

    def _get_and_cache_response(
        self, request, service_locator, *args, **kwargs
    ):
        """Get response and cache it."""
        response = super().list(request, *args, **kwargs)
        company_id = kwargs.get("company_id")

        # Only cache successful responses
        if response.status_code == 200:
            service_locator.company_service.cache_license_response_with_logging(
                request, response.data, company_id
            )

        return response
