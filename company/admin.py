from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.models import Member
from django.contrib import admin
from general.admin import ImportExportModelAdminMixin


@admin.register(Company)
class CompanyAdmin(ImportExportModelAdminMixin):
    search_fields = ["name", "tax_id", "email", "user__email"]

    list_filter = [
        "user",
    ]


@admin.register(Licenses)
class LicensesAdmin(ImportExportModelAdminMixin):
    list_filter = ["created_by", "owner_contact"]


@admin.register(Insurance)
class InsuranceAdmin(ImportExportModelAdminMixin):
    list_filter = ["created_by", "company"]


@admin.register(Member)
class MemberAdmin(ImportExportModelAdminMixin):
    list_filter = ["company"]
