from datetime import datetime

from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.models import Officer
from company.serializers import CompanySerializer
from company.serializers import InsuranceSerializer
from company.serializers import LicensesSerializer
from company.serializers import OfficerSerializer
from core.constants import Features
from core.core_services.sendgrid_service import CoreService
from core.core_services.sendgrid_service import SendGridService
from django.conf import settings
from django.db import models
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from general.models import Setting
from notifications.mixins import NotificationCleanupMixin
from recent_app.mixins import CreateRecentActivityMixin
from rest_framework import filters
from rest_framework import serializers
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.exceptions import ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.generics import ListAPIView
from rest_framework.generics import ListCreateAPIView
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from storage.models import File

from .filters import InsuranceFilter
from .filters import LicensesFilter
from .filters import OfficerFilter
from .models import Member
from .models import ModulePermission
from .permissions import CompanyModulePermission
from .serializers import MemberInviteSerializer
from .serializers import UserCompanySerializer
from .tasks import notify_user_about_new_member_invite


class CompanyRetrieveUpdateDeleteView(RetrieveUpdateDestroyAPIView):
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.COMPANY

    def get_queryset(self):
        return Company.objects.get_user_companies(user=self.request.user)

    def get_object(self):
        company_id = self.kwargs.get("pk")
        return get_object_or_404(self.get_queryset(), id=company_id)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_deleted = True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class OfficerCreateListView(ListCreateAPIView):
    serializer_class = OfficerSerializer
    filter_backends = [
        filters.SearchFilter,
        filters.OrderingFilter,
        DjangoFilterBackend,
    ]
    filterset_class = OfficerFilter
    search_fields = ["firstname", "lastname", "job_position", "email"]
    ordering_fields = "__all__"
    ordering = ["lastname", "firstname"]

    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.OFFICERS

    def get_queryset(self):
        company_id = self.kwargs.get("company_id")
        company: Company = get_object_or_404(Company, id=company_id)
        return Officer.objects.get_company_officers(company)


class OfficerRetrieveUpdateDeleteView(RetrieveUpdateDestroyAPIView):
    serializer_class = OfficerSerializer
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.OFFICERS

    def get_queryset(self, *args, **kwargs):
        # get company id from url params
        company_id = self.kwargs.get("company_id")
        # get company object
        company: Company = Company.objects.filter(id=company_id).first()
        if not company:
            return Officer.objects.none()

        return Officer.objects.get_company_officers(company)

    def get_object(self):
        return get_object_or_404(self.get_queryset(), id=self.kwargs.get("pk"))

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_deleted = True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class LicenseCreateListView(ListCreateAPIView):
    serializer_class = LicensesSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = LicensesFilter
    search_fields = ["name", "license_number"]
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.LICENSES

    def get_queryset(self):
        company_id = self.kwargs.get("company_id")

        company: Company = get_object_or_404(Company, id=company_id)
        return Licenses.objects.get_filtered_company_licenses(company)


class LicenseRetrieveUpdateDeleteView(
    NotificationCleanupMixin,
    CreateRecentActivityMixin,
    RetrieveUpdateDestroyAPIView,
):
    serializer_class = LicensesSerializer
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.LICENSES

    def get_queryset(self, *args, **kwargs):
        # get company id from url params
        company_id = self.kwargs.get("company_id")
        # get company object
        company: Company = Company.objects.filter(id=company_id).first()
        if not company:
            return Licenses.objects.none()
        return Licenses.objects.get_filtered_company_licenses(company)

    def get_object(self):
        return get_object_or_404(self.get_queryset(), id=self.kwargs.get("pk"))

    def perform_update(self, serializer):
        instance = serializer.save()
        # Get updated fields from serializer
        updated_fields = serializer.validated_data.keys()
        self.cleanup_notifications(instance, updated_fields)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_deleted = True

        # Mark main attachment as deleted if it exists
        if instance.attachment:
            instance.attachment.is_deleted = True
            instance.attachment.save()

        # Mark additional attachments as deleted
        instance.additional_attachments.update(is_deleted=True)

        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_category(self):
        return Features.LICENSES


class InsuranceCreateListView(ListCreateAPIView):
    serializer_class = InsuranceSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = InsuranceFilter
    search_fields = ["carrier", "broker", "agent", "policy_number"]
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.INSURANCE

    def get_queryset(self):

        company_id = self.kwargs.get("company_id")
        company: Company = get_object_or_404(Company, id=company_id)
        return Insurance.objects.get_filtered_company_insurance(company)


class InsuranceRetrieveUpdateDeleteView(
    NotificationCleanupMixin,
    CreateRecentActivityMixin,
    RetrieveUpdateDestroyAPIView,
):
    serializer_class = InsuranceSerializer
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.INSURANCE

    def get_queryset(self, *args, **kwargs):
        # get company id from url params
        company_id = self.kwargs.get("company_id")
        # get company object
        company: Company = Company.objects.filter(id=company_id).first()
        if not company:
            return Insurance.objects.none()
        return Insurance.objects.get_filtered_company_insurance(company)

    def get_object(self):
        return get_object_or_404(self.get_queryset(), id=self.kwargs.get("pk"))

    def perform_update(self, serializer):
        instance = serializer.save()
        # Get updated fields from serializer
        updated_fields = serializer.validated_data.keys()
        self.cleanup_notifications(instance, updated_fields)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_deleted = True

        # Mark policy as deleted if it exists
        if instance.policy:
            try:
                instance.policy.is_deleted = True
                instance.policy.save()
            except File.DoesNotExist:
                instance.policy = None

        # Mark additional attachments as deleted
        instance.additional_attachments.update(is_deleted=True)

        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_category(self):
        return Features.INSURANCES


class CreateMemberInviteView(CreateAPIView):
    serializer_class = MemberInviteSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.core_service = CoreService(sendgrid_service=SendGridService())

    def send_invite_email(
        self, email, company, sender, member_id, template_path
    ):
        subject = f"{sender.get_full_name()} invited you to join their company on the platform"
        context = {
            "company_name": company.name,
            "inviter_name": sender.get_full_name(),
            "redirect_url": f"{settings.FRONTEND_URL}accept-member-invite?inviteId={member_id}",
            "current_year": datetime.now().year,
        }

        self.core_service.send_email(
            subject=subject,
            template_path=template_path,
            template_context=context,
            to_emails=[email],
        )

    def perform_create(self, serializer):

        # Use get_user_company to get the company for the user
        company = Company.objects.get_user_company(self.request.user)

        invitee_emails = self.request.data.get("invitee_emails", [])
        members = []

        # Extract user statuses from the context
        user_statuses = serializer.context.get("user_status", [])

        for i, email in enumerate(invitee_emails):
            serializer = self.get_serializer(
                data={**self.request.data, "invitee_emails": [email]}
            )
            serializer.is_valid(raise_exception=True)

            # Save the member and get the invitee user creation status
            member = serializer.save()

            # Determine if the user is new or existing
            is_new_user = user_statuses[i] if i < len(user_statuses) else False

            # Select the appropriate email template
            template_path = (
                "member_invite_new_user.html"
                if is_new_user
                else "member_invite_existing_user.html"
            )

            # Send the invite email
            self.send_invite_email(
                email, company, self.request.user, member.id, template_path
            )
            members.append(member)

            # notification task
            notify_user_about_new_member_invite.delay(member.id)

        return members


class ListMemberInvitationsSentView(ListAPIView):
    serializer_class = MemberInviteSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        settings_obj = Setting.objects.filter(user=self.request.user).first()
        return Member.objects.filter(
            company_id=settings_obj.company, created_by=self.request.user
        )


class ListMemberInvitationsReceivedView(ListAPIView):
    serializer_class = MemberInviteSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Member.objects.filter(user=self.request.user)


class MemberInviteStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ["status"]


class MemberInviteStatusUpdateView(UpdateAPIView):
    serializer_class = MemberInviteStatusSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        member = get_object_or_404(Member, id=self.kwargs["pk"])

        if member.user != self.request.user:
            raise PermissionDenied(
                "You are not allowed to update this invitation status."
            )

        # Check if the invite has expired
        if (
            timezone.now() > member.invitation_expires_at
            and member.status == Member.STATUS_PENDING
        ):
            member.status = Member.STATUS_EXPIRED
            member.save()
            raise ValidationError(
                "This invitation has expired and cannot be accepted or rejected."
            )

        return member

    def get_queryset(self):
        return Member.objects.none()


class UserCompanyListView(ListAPIView):
    serializer_class = UserCompanySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        return Company.objects.filter(
            models.Q(user=user)
            | models.Q(  # Companies owned by user  # Companies where user is an accepted member
                members__user=user, members__status=Member.STATUS_ACCEPTED
            )
        ).distinct()

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["request"] = self.request
        return context
