import json

from accounts.models import User
from celery import shared_task
from company.models import Member
from core.constants import Features
from django.conf import settings
from django.utils import timezone
from django_celery_beat.models import ClockedSchedule
from django_celery_beat.models import PeriodicTask
from firebase_admin import messaging
from general.models import Setting
from notifications.models import Notification
from utils.firebase.messaging import FirebaseMessageService
from utils.utils import create_or_update_notification

messaging_service = FirebaseMessageService()


def schedule_notification(task_name, reminder_time, item_id, task_type):
    """Utility to schedule periodic notifications."""
    schedule, _ = ClockedSchedule.objects.get_or_create(
        clocked_time=reminder_time
    )
    task_params = {
        "clocked": schedule,
        "name": task_name,
        "task": task_type,
        "args": json.dumps([str(item_id)]),
        "one_off": True,
        "start_time": reminder_time,
    }
    PeriodicTask.objects.update_or_create(name=task_name, defaults=task_params)


def send_notification(user, title, msg, category, item_id, feature):
    """Send push notification to user."""
    if user is None:
        return
    # Create or update the notification using the utility function
    create_or_update_notification(user, category, item_id)

    topic = str(user.id)
    data_payload = {
        "id": str(item_id),
        "category": feature,
    }
    message = messaging.Message(
        topic=topic,
        notification=messaging.Notification(title=title, body=msg),
        data=data_payload,
    )
    messaging_service.send_message_to_topic(message)


@shared_task()
def notify_user_about_new_member_invite(member_id):
    """Send notification for new member invite."""
    if settings.TEST_DEBUG:
        return

    member = Member.objects.get(pk=member_id)
    user = member.user
    if not user:
        return

    company = member.company
    msg = f"{member.created_by.get_full_name()} invited you to join {company.name}"
    send_notification(
        user,
        "New Company Invite",
        msg,
        "pending_invites",
        member.id,
        Features.PENDING_INVITES,
    )


@shared_task()
def schedule_member_invite_expiration_notifications():
    """Schedule notifications for member invites that expire today."""
    invites = Member.objects.get_invites_that_expire_today()
    for invite in invites:
        task_name = f"member_invite_expiration_{invite.id}"
        schedule_notification(
            task_name,
            timezone.now(),
            invite.id,
            "notifications.tasks.notify_user_about_member_invite_expiration",
        )


@shared_task()
def schedule_member_invite_reminder_notifications():
    """Schedule reminder notifications for member invites."""
    invites = Member.objects.get_invites_that_need_to_remind_today()
    for invite in invites:
        task_name = f"member_invite_reminder_{invite.id}"
        schedule_notification(
            task_name,
            timezone.now(),
            invite.id,
            "notifications.tasks.notify_user_about_member_invite_reminder",
        )


@shared_task()
def notify_user_about_member_invite_expiration(member_id):
    """Send notification when member invite expires."""
    member = Member.objects.get(pk=member_id)
    member.status = Member.STATUS_EXPIRED
    member.save()

    user = member.user
    msg = f"Your invitation to join {member.company.name} has expired"
    messaging_service.send_message_to_topic(
        messaging.Message(
            topic=str(user.id),
            notification=messaging.Notification(
                title="Company Invite Expiration", body=msg
            ),
            data={
                "id": str(member.id),
                "category": Features.COMPANY_INVITE_EXPIRATIONS,
            },
        )
    )


@shared_task()
def notify_user_about_member_invite_reminder(member_id):
    """Send reminder notification for member invite."""
    member = Member.objects.get(pk=member_id)
    user = member.user
    expire_at = (member.created_at + timezone.timedelta(days=7)).strftime(
        "%d %B, %Y at %I:%M %p"
    )
    msg = (
        f"Your invitation to join {member.company.name} expires on {expire_at}"
    )
    messaging_service.send_message_to_topic(
        messaging.Message(
            topic=str(user.id),
            notification=messaging.Notification(
                title="Company Invite Expiration Reminder", body=msg
            ),
            data={
                "id": str(member.id),
                "category": Features.COMPANY_INVITE_EXPIRATIONS,
            },
        )
    )


@shared_task()
def delete_expired_member_invites():
    """Delete expired member invites and their notifications."""
    invites_to_delete = Member.objects.get_invites_to_delete()
    for invite in invites_to_delete:
        Notification.objects.filter(
            item_id=invite.id, category=Notification.Category.PENDING_INVITES
        ).delete()
        invite.delete()


@shared_task()
def update_user_settings_with_company():
    """Update settings for all users with their associated company."""
    users = User.objects.all()
    for user in users:
        company = user.company_set.first()

        # Skip if user has no associated company
        if not company:
            continue

        # Retrieve or create the user's setting
        setting, created = Setting.objects.get_or_create(
            user=user, defaults={"company": company.id}
        )

        # If the setting already exists and the company is different, update it
        if not created and setting.company != company.id:
            setting.company = company.id
            setting.save()
