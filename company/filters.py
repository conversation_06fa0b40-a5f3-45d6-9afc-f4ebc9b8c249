import django_filters

from .models import Insurance
from .models import Licenses
from .models import Officer


class OfficerFilter(django_filters.FilterSet):
    firstname = django_filters.CharFilter(lookup_expr="icontains")
    lastname = django_filters.CharFilter(lookup_expr="icontains")
    job_position = django_filters.CharFilter(lookup_expr="icontains")
    mobile_number = django_filters.CharFilter(lookup_expr="icontains")
    extension = django_filters.CharFilter(lookup_expr="icontains")
    email = django_filters.CharFilter(lookup_expr="icontains")

    class Meta:
        model = Officer
        fields = [
            "firstname",
            "lastname",
            "job_position",
            "mobile_number",
            "extension",
            "email",
        ]


class LicensesFilter(django_filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    license_number = django_filters.CharFilter(lookup_expr="icontains")
    license_type = django_filters.ChoiceFilter(
        choices=Licenses.LicenseType.CHOICES
    )
    status = django_filters.ChoiceFilter(choices=Licenses.Status.CHOICES)

    class Meta:
        model = Licenses
        fields = ["name", "license_number", "license_type", "status"]


class InsuranceFilter(django_filters.FilterSet):
    carrier = django_filters.CharFilter(lookup_expr="icontains")
    broker = django_filters.CharFilter(lookup_expr="icontains")
    agent = django_filters.CharFilter(lookup_expr="icontains")
    contact = django_filters.CharFilter(lookup_expr="icontains")
    extension = django_filters.CharFilter(lookup_expr="icontains")
    email = django_filters.CharFilter(lookup_expr="icontains")
    policy_number = django_filters.CharFilter(lookup_expr="icontains")
    insurance_type = django_filters.ChoiceFilter(
        choices=Insurance.InsuranceType.CHOICES
    )
    status = django_filters.ChoiceFilter(choices=Insurance.Status.CHOICES)

    class Meta:
        model = Insurance
        fields = [
            "carrier",
            "broker",
            "agent",
            "contact",
            "extension",
            "email",
            "policy_number",
            "insurance_type",
            "status",
        ]
