# Performance Tests - V1 vs V2 Comparison

This directory contains comprehensive performance tests to measure the improvements between V1 and V2 endpoints for Insurance and License operations.

## Overview

The performance tests compare:

- **V1 Endpoints**: Traditional endpoints without caching
- **V2 Endpoints**: New endpoints with comprehensive caching implementation

## Test Files

### `test_performance_v1_vs_v2.py`

Main performance test suite containing:

1. **PerformanceTestV1VsV2**

   - `test_insurance_performance_comparison()`: Compares insurance endpoint performance
   - `test_license_performance_comparison()`: Compares license endpoint performance
   - `test_cache_bypass_performance()`: Tests V2 performance with cache disabled

2. **CacheEffectivenessTest**
   - `test_cache_invalidation()`: Verifies cache invalidation works correctly
   - `test_cache_with_query_parameters()`: Tests cache behavior with different query params

## Running the Tests

### Method 1: Django Management Command (Recommended)

```bash
# Run all performance tests
python manage.py benchmark_v1_vs_v2

# Run only insurance tests
python manage.py benchmark_v1_vs_v2 --test-type insurance

# Run only license tests
python manage.py benchmark_v1_vs_v2 --test-type license

# Customize iterations and test data size
python manage.py benchmark_v1_vs_v2 --iterations 20 --records 100

# Clear cache before running
python manage.py benchmark_v1_vs_v2 --clear-cache
```

### Method 2: Python Script

```bash
python scripts/run_performance_tests.py
```

### Method 3: Django Test Runner

```bash
# Run all performance tests
python manage.py test company.tests.test_performance_v1_vs_v2

# Run specific test
python manage.py test company.tests.test_performance_v1_vs_v2.PerformanceTestV1VsV2.test_insurance_performance_comparison
```

## What the Tests Measure

### Response Time Metrics

- **Average Response Time**: Mean response time across multiple requests
- **Median Response Time**: Middle value of response times
- **Min/Max Response Time**: Fastest and slowest response times
- **Standard Deviation**: Consistency of response times

### Cache Performance Metrics

- **Cache Miss Performance**: First request performance (no cache)
- **Cache Hit Performance**: Subsequent request performance (from cache)
- **Overall Performance**: Combined performance including cache misses and hits
- **Performance Improvement**: Percentage improvement of V2 over V1

### Cache Behavior Tests

- **Cache Invalidation**: Ensures cache is properly cleared when data changes
- **Query Parameter Handling**: Verifies different query params create separate cache entries
- **Cache Bypass**: Tests performance when cache is explicitly disabled

## Expected Results

### Typical Performance Improvements

- **Cache Hits**: 70-90% faster than V1
- **Overall V2**: 40-70% faster than V1 (including cache misses)
- **Cache Miss**: Similar to V1 (slight overhead for caching logic)

### Sample Output

```
INSURANCE PERFORMANCE RESULTS:
----------------------------------------
V1 Average Response Time: 45.23ms
V1 Median Response Time:  43.18ms
V1 Min/Max:              38.45ms / 52.67ms

V2 Overall Average:      28.91ms
V2 Cache Miss (1st req): 47.12ms
V2 Cache Hit Average:    8.34ms
V2 Cache Hit Min/Max:    6.78ms / 11.23ms

PERFORMANCE IMPROVEMENTS:
Overall V2 vs V1:        36.1% faster
V2 Cache Hits vs V1:     81.6% faster
✅ EXCELLENT: Cache provides significant performance boost!
```

## Interpreting Results

### Performance Categories

- **EXCELLENT** (>50% improvement): Cache provides significant performance boost
- **GOOD** (25-50% improvement): Cache provides good performance improvement
- **MODERATE** (<25% improvement): Cache provides some improvement

### Key Metrics to Watch

1. **Cache Hit Improvement**: Should be >50% for optimal caching
2. **Overall Improvement**: Should be >25% considering cache misses
3. **Cache Miss Overhead**: Should be minimal (<10% slower than V1)

## Troubleshooting

### Common Issues

1. **Low Performance Improvement**

   - Check if cache is properly configured
   - Verify cache backend is running (Redis)
   - Ensure test data is sufficient for meaningful comparison

2. **Cache Not Working**

   - Check cache settings in Django settings
   - Verify Redis connection
   - Check for cache key conflicts

3. **Inconsistent Results**
   - Run tests multiple times
   - Increase iteration count
   - Check for background processes affecting performance

### Debug Commands

```bash
# Check cache configuration
python manage.py shell -c "from django.core.cache import cache; print(cache)"

# Clear cache manually
python manage.py shell -c "from django.core.cache import cache; cache.clear()"

# Check cache keys
python manage.py shell -c "from django.core.cache import cache; print(cache._cache.get_stats())"
```

## Production Considerations

### Monitoring in Production

1. Set up cache hit rate monitoring
2. Monitor response times for V2 endpoints
3. Set up alerts for cache failures
4. Track cache memory usage

### Optimization Tips

1. Implement cache warming for critical data
2. Set up proper cache invalidation triggers
3. Monitor and tune cache timeout values
4. Consider cache partitioning for large datasets

### Load Testing

For production load testing, use tools like:

- Apache Bench (ab)
- wrk
- Artillery
- JMeter

Example load test:

```bash
# Test V1 endpoint
ab -n 1000 -c 50 http://localhost:8000/api/v1/company/{company_id}/insurance/

# Test V2 endpoint
ab -n 1000 -c 50 http://localhost:8000/api/v2/company/{company_id}/insurance/
```

## Contributing

When adding new performance tests:

1. Follow the existing test structure
2. Include both cache hit and miss scenarios
3. Add proper assertions for performance improvements
4. Update this README with new test descriptions
