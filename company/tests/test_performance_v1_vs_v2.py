import statistics
import time

from accounts.factories import UserFactory
from company.factories import CompanyFactory
from company.factories import InsuranceFactory
from company.factories import LicensesFactory
from company.models import Insurance
from company.models import Licenses
from crum import set_current_user
from django.core.cache import cache
from django.test import override_settings
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient


# Override cache settings to use a real cache backend for performance tests
CACHE_SETTINGS = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "performance-test-cache",
    }
}


@override_settings(CACHES=CACHE_SETTINGS)
class PerformanceTestV1VsV2(TestCase):
    """
    Performance test comparing v1 and v2 endpoints for Insurance and Licenses.
    Tests cache performance, response times, and overall improvements.
    """

    def setUp(self):
        """Set up test data and client."""
        self.client = APIClient()
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        set_current_user(self.user)

        # Create test data
        self.insurance_count = 50
        self.license_count = 50

        # Create insurance records
        self.insurances = [
            InsuranceFactory(company=self.company)
            for _ in range(self.insurance_count)
        ]

        # Create license records
        self.licenses = [
            LicensesFactory(company=self.company)
            for _ in range(self.license_count)
        ]

        # Authenticate user
        self.client.force_authenticate(user=self.user)

        # Clear cache before each test
        cache.clear()

    def tearDown(self):
        """Clean up after tests."""
        cache.clear()
        set_current_user(None)

    def measure_response_time(self, url, iterations=10, debug=False):
        """
        Measure average response time for multiple requests.

        Args:
            url: The URL to test
            iterations: Number of requests to make
            debug: Print debug information

        Returns:
            dict: Statistics about response times
        """
        times = []

        for i in range(iterations):
            start_time = time.time()
            response = self.client.get(url)
            end_time = time.time()

            # Ensure successful response
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            response_time = (
                end_time - start_time
            ) * 1000  # Convert to milliseconds
            times.append(response_time)

            if debug:
                print(
                    f"  Request {i+1}: {response_time:.2f}ms, Records: {len(response.data)}"
                )

        return {
            "times": times,
            "average": statistics.mean(times),
            "median": statistics.median(times),
            "min": min(times),
            "max": max(times),
            "std_dev": statistics.stdev(times) if len(times) > 1 else 0,
        }

    def test_insurance_performance_comparison(self):
        """Test performance comparison between v1 and v2 insurance endpoints."""
        print("\n" + "=" * 60)
        print("INSURANCE PERFORMANCE TEST - V1 vs V2")
        print("=" * 60)

        # URLs for v1 and v2
        v1_url = reverse(
            "company:insurance", kwargs={"company_id": self.company.id}
        )
        v2_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )

        print(f"Testing with {self.insurance_count} insurance records")
        print(f"V1 URL: {v1_url}")
        print(f"V2 URL: {v2_url}")

        # Test V1 performance (no caching)
        print("\nTesting V1 (no caching)...")
        cache.clear()  # Ensure clean state
        v1_stats = self.measure_response_time(
            v1_url, iterations=10, debug=True
        )

        # Test V2 performance (with caching)
        print("Testing V2 (with caching)...")
        cache.clear()  # Ensure clean state for fair comparison

        # First request (cache miss)
        print("  - First request (cache miss)...")
        v2_cache_miss_stats = self.measure_response_time(
            v2_url, iterations=1, debug=True
        )

        # Subsequent requests (cache hits)
        print("  - Subsequent requests (cache hits)...")
        v2_cache_hit_stats = self.measure_response_time(
            v2_url, iterations=9, debug=True
        )

        # Calculate overall V2 performance
        all_v2_times = (
            v2_cache_miss_stats["times"] + v2_cache_hit_stats["times"]
        )
        v2_overall_stats = {
            "average": statistics.mean(all_v2_times),
            "median": statistics.median(all_v2_times),
            "min": min(all_v2_times),
            "max": max(all_v2_times),
        }

        # Print results
        self._print_performance_results(
            "INSURANCE",
            v1_stats,
            v2_overall_stats,
            v2_cache_miss_stats,
            v2_cache_hit_stats,
        )

        # Performance checks (warnings instead of hard failures)
        if v2_cache_hit_stats["average"] >= v1_stats["average"]:
            print(
                f"⚠️  WARNING: V2 cache hits ({v2_cache_hit_stats['average']:.2f}ms) not faster than V1 ({v1_stats['average']:.2f}ms)"
            )
        if v2_overall_stats["average"] >= v1_stats["average"]:
            print(
                f"⚠️  WARNING: V2 overall ({v2_overall_stats['average']:.2f}ms) not faster than V1 ({v1_stats['average']:.2f}ms)"
            )

        # Only fail if cache is completely broken (much slower)
        self.assertLess(
            v2_cache_hit_stats["average"],
            v1_stats["average"] * 2,
            "V2 cache hits should not be more than 2x slower than V1",
        )

    def test_license_performance_comparison(self):
        """Test performance comparison between v1 and v2 license endpoints."""
        print("\n" + "=" * 60)
        print("LICENSE PERFORMANCE TEST - V1 vs V2")
        print("=" * 60)

        # URLs for v1 and v2
        v1_url = reverse(
            "company:licenses", kwargs={"company_id": self.company.id}
        )
        v2_url = reverse(
            "company_v2:licenses_v2", kwargs={"company_id": self.company.id}
        )

        print(f"Testing with {self.license_count} license records")
        print(f"V1 URL: {v1_url}")
        print(f"V2 URL: {v2_url}")

        # Test V1 performance (no caching)
        print("\nTesting V1 (no caching)...")
        cache.clear()  # Ensure clean state
        v1_stats = self.measure_response_time(
            v1_url, iterations=10, debug=True
        )

        # Test V2 performance (with caching)
        print("Testing V2 (with caching)...")
        cache.clear()  # Ensure clean state for fair comparison

        # First request (cache miss)
        print("  - First request (cache miss)...")
        v2_cache_miss_stats = self.measure_response_time(
            v2_url, iterations=1, debug=True
        )

        # Subsequent requests (cache hits)
        print("  - Subsequent requests (cache hits)...")
        v2_cache_hit_stats = self.measure_response_time(
            v2_url, iterations=9, debug=True
        )

        # Calculate overall V2 performance
        all_v2_times = (
            v2_cache_miss_stats["times"] + v2_cache_hit_stats["times"]
        )
        v2_overall_stats = {
            "average": statistics.mean(all_v2_times),
            "median": statistics.median(all_v2_times),
            "min": min(all_v2_times),
            "max": max(all_v2_times),
        }

        # Print results
        self._print_performance_results(
            "LICENSE",
            v1_stats,
            v2_overall_stats,
            v2_cache_miss_stats,
            v2_cache_hit_stats,
        )

        # Performance checks (warnings instead of hard failures)
        if v2_cache_hit_stats["average"] >= v1_stats["average"]:
            print(
                f"⚠️  WARNING: V2 cache hits ({v2_cache_hit_stats['average']:.2f}ms) not faster than V1 ({v1_stats['average']:.2f}ms)"
            )
        if v2_overall_stats["average"] >= v1_stats["average"]:
            print(
                f"⚠️  WARNING: V2 overall ({v2_overall_stats['average']:.2f}ms) not faster than V1 ({v1_stats['average']:.2f}ms)"
            )

        # Only fail if cache is completely broken (much slower)
        self.assertLess(
            v2_cache_hit_stats["average"],
            v1_stats["average"] * 2,
            "V2 cache hits should not be more than 2x slower than V1",
        )

    def test_cache_bypass_performance(self):
        """Test performance when cache is disabled in V2."""
        print("\n" + "=" * 60)
        print("CACHE BYPASS TEST - V2 with disable_cache=true")
        print("=" * 60)

        # Test insurance with cache disabled
        v2_insurance_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )
        v2_license_url = reverse(
            "company_v2:licenses_v2", kwargs={"company_id": self.company.id}
        )

        # Add disable_cache parameter
        insurance_url_no_cache = f"{v2_insurance_url}?disable_cache=true"
        license_url_no_cache = f"{v2_license_url}?disable_cache=true"

        print("Testing V2 Insurance with cache disabled...")
        insurance_no_cache_stats = self.measure_response_time(
            insurance_url_no_cache, iterations=5
        )

        print("Testing V2 License with cache disabled...")
        license_no_cache_stats = self.measure_response_time(
            license_url_no_cache, iterations=5
        )

        print(
            f"\nInsurance (cache disabled): {insurance_no_cache_stats['average']:.2f}ms avg"
        )
        print(
            f"License (cache disabled): {license_no_cache_stats['average']:.2f}ms avg"
        )

    def _print_performance_results(
        self,
        endpoint_type,
        v1_stats,
        v2_overall_stats,
        v2_cache_miss_stats,
        v2_cache_hit_stats,
    ):
        """Print formatted performance results."""
        print(f"\n{endpoint_type} PERFORMANCE RESULTS:")
        print("-" * 40)
        print(f"V1 Average Response Time: {v1_stats['average']:.2f}ms")
        print(f"V1 Median Response Time:  {v1_stats['median']:.2f}ms")
        print(
            f"V1 Min/Max:              {v1_stats['min']:.2f}ms / {v1_stats['max']:.2f}ms"
        )

        print(
            f"\nV2 Overall Average:      {v2_overall_stats['average']:.2f}ms"
        )
        print(
            f"V2 Cache Miss (1st req): {v2_cache_miss_stats['average']:.2f}ms"
        )
        print(
            f"V2 Cache Hit Average:    {v2_cache_hit_stats['average']:.2f}ms"
        )
        print(
            f"V2 Cache Hit Min/Max:    {v2_cache_hit_stats['min']:.2f}ms / {v2_cache_hit_stats['max']:.2f}ms"
        )

        # Calculate improvements
        overall_improvement = (
            (v1_stats["average"] - v2_overall_stats["average"])
            / v1_stats["average"]
        ) * 100
        cache_hit_improvement = (
            (v1_stats["average"] - v2_cache_hit_stats["average"])
            / v1_stats["average"]
        ) * 100

        print("\nPERFORMANCE IMPROVEMENTS:")
        print(f"Overall V2 vs V1:        {overall_improvement:.1f}% faster")
        print(f"V2 Cache Hits vs V1:     {cache_hit_improvement:.1f}% faster")

        if cache_hit_improvement > 50:
            print("✅ EXCELLENT: Cache provides significant performance boost!")
        elif cache_hit_improvement > 25:
            print("✅ GOOD: Cache provides good performance improvement")
        else:
            print("⚠️  MODERATE: Cache provides some improvement")


@override_settings(CACHES=CACHE_SETTINGS)
class CacheEffectivenessTest(TestCase):
    """Test cache effectiveness and behavior."""

    def setUp(self):
        self.client = APIClient()
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.client.force_authenticate(user=self.user)
        cache.clear()

        # Clear all existing insurance and license records to start fresh
        Insurance.objects.all().delete()
        Licenses.objects.all().delete()

    def test_cache_invalidation(self):
        """Test that cache is properly invalidated when data changes."""
        print("\n" + "=" * 60)
        print("CACHE INVALIDATION TEST")
        print("=" * 60)

        # Clear any existing insurance records for this company to start fresh
        existing_count = Insurance.objects.filter(company=self.company).count()
        print(
            f"Found {existing_count} existing insurance records, deleting them"
        )
        Insurance.objects.filter(company=self.company).delete()

        # Create exactly 2 insurance records  (so they appear in filtered results)
        InsuranceFactory(
            company=self.company,
            carrier="Initial Insurance 1",
        )
        InsuranceFactory(
            company=self.company,
            carrier="Initial Insurance 2",
        )

        # Verify we have exactly 2 records in the database
        db_count = Insurance.objects.filter(company=self.company).count()
        filtered_count = Insurance.objects.filter(
            company=self.company
        ).count()
        print(
            f"Created 2 insurance records, DB shows: {db_count}, filtered: {filtered_count}"
        )

        v2_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )

        # First request - cache miss
        response1 = self.client.get(v2_url)
        self.assertEqual(response1.status_code, status.HTTP_200_OK)

        initial_count = len(response1.data.get("results"))
        print(f"Initial insurance count from API (paginated): {initial_count}")

        # Verify the API count matches our filtered count
        self.assertEqual(
            initial_count,
            filtered_count,
            "API should return only filtered insurance records",
        )

        # Second request - cache hit (should be same)
        response2 = self.client.get(v2_url)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)

        second_count = len(response2.data["results"])

        self.assertEqual(second_count, initial_count)
        print(f"Second request count (cached): {second_count}")

        # Add new insurance
        InsuranceFactory(
            company=self.company, carrier="New Insurance"
        )
        print("Added new insurance record")

        # Verify the new record was added to the database
        new_db_count = Insurance.objects.filter(company=self.company).count()
        new_filtered_count = Insurance.objects.filter(
            company=self.company
        ).count()
        print(
            f"DB count after adding new record: {new_db_count}, filtered: {new_filtered_count}"
        )

        # Cache should still return old data until invalidated
        response3 = self.client.get(v2_url)

        third_count = len(response3.data["results"])

        print(f"Third request count (should be cached): {third_count}")
        self.assertEqual(third_count, initial_count)  # Still cached

        # Clear cache manually (simulating cache invalidation)
        from core.dependency_injection import service_locator

        set_current_user(self.user)

        service_locator.company_service.clear_insurance_cache(self.company.id)
        print("Cleared cache")

        # Now should get fresh data
        response4 = self.client.get(v2_url)

        fourth_count = len(response4.data.get("results"))

        print(f"Fourth request count (after cache clear): {fourth_count}")

        self.assertEqual(fourth_count, initial_count + 1)

        print("✅ Cache invalidation working correctly")

    def test_cache_with_query_parameters(self):
        """Test that cache works correctly with different query parameters."""
        print("\n" + "=" * 60)
        print("CACHE WITH QUERY PARAMETERS TEST")
        print("=" * 60)

        # Clear existing data and create test data with different carriers
        Insurance.objects.filter(company=self.company).delete()
        InsuranceFactory(company=self.company, carrier="Carrier A")
        InsuranceFactory(company=self.company, carrier="Carrier B")
        InsuranceFactory(company=self.company, carrier="Carrier C")

        v2_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )

        # Test different query parameters create different cache entries
        response1 = self.client.get(f"{v2_url}?search=Carrier A")
        response2 = self.client.get(f"{v2_url}?search=Carrier B")
        response3 = self.client.get(v2_url)  # No search

        self.assertEqual(response1.status_code, status.HTTP_200_OK)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        self.assertEqual(response3.status_code, status.HTTP_200_OK)

        print(f"Search 'Carrier A': {len(response1.data)} results")
        print(f"Search 'Carrier B': {len(response2.data)} results")
        print(f"No search: {len(response3.data)} results")

        # Results should be different - search should return fewer results than no search
        self.assertLessEqual(len(response1.data), len(response3.data))
        self.assertLessEqual(len(response2.data), len(response3.data))

        print("✅ Cache correctly handles different query parameters")
