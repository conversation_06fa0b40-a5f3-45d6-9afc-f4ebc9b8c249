from datetime import datetime
from datetime import timedelta

from accounts.factories import UserFactory
from company.factories import CompanyFactory
from company.factories import InsuranceFactory
from company.factories import LicensesFactory
from company.factories import MemberFactory
from company.factories import OfficerFactory
from company.mixins import DocumentBaseMixin
from company.models import Insurance
from company.models import Licenses
from company.models import Member
from django.urls import reverse
from django.utils import timezone
from general.models import Setting
from rest_framework import status
from rest_framework.response import Response
from storage.factories import FileFactory
from storage.models import File
from testing.base import BaseAPITest


class CompanyTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.other_company = CompanyFactory()

        self.url = reverse(
            "company:company",
            kwargs={
                "pk": self.company.id,
            },
        )
        super().setUp()

    def test_unauthenticated_user(self):
        #  Given an anaonymous user
        #  When I try to get the company
        res: Response = self.client.get(self.url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an anaonymous user
        #  When I try to update the company
        res: Response = self.client.patch(self.url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an anaonymous user
        #  When I try to delete the company
        res: Response = self.client.delete(self.url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_user(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        #  When I try to get the company
        res: Response = self.client.get(self.url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        #  And When I try to update the company
        payload = {
            # Provide updated data for the company, including business_ids
            "name": "Updated Company Name",
            "business_ids": [
                {"type_id": "Type1", "number_id": "12345"},
                {"type_id": "Type2", "number_id": "67890"},
            ],
        }
        res: Response = self.client.patch(
            self.url, data=payload, format="json"
        )

        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        #  When I try to delete the company
        res: Response = self.client.delete(self.url)
        #  Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # And When I try to get the company
        res: Response = self.client.get(self.url)
        # Then I should get a 404
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)

    def test_add_logo_company(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        #  When I try to add a logo to the company
        payload = {
            "logo": self.generate_base64_photo_file(),
        }
        res: Response = self.client.patch(self.url, data=payload)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And company logo should be updated
        self.company.refresh_from_db()
        self.assertIsNotNone(self.company.logo)


class OfficerTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.other_company = CompanyFactory(user=UserFactory())

        self.url = reverse(
            "company:officers",
            kwargs={
                "company_id": self.company.id,
            },
        )
        super().setUp()

    def test_unauthenticated_user(self):
        #  Given an unauthenticated user
        self.client.force_authenticate(user=None)
        #  When I try to get the officers
        res: Response = self.client.get(self.url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  And When I try to create an officer
        res: Response = self.client.post(self.url, data={})
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to update an officer
        res: Response = self.client.patch(self.url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to delete an officer
        res: Response = self.client.delete(self.url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_office(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        #  When I try to create an officer

        payload = {
            "firstname": "John",
            "lastname": "Doe",
            "title": "Mr",
            "job_position": "CEO",
            "mobile_number": "+254712345678",
            "extension": "12345",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St",
            "address_line_2": "Apt 101",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
        }
        res: Response = self.client.post(self.url, data=payload)
        #  Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # And number of officers should be 1
        self.assertEqual(self.company.officers.count(), 1)

        # When I try to create an officer with company that does not belong to me
        payload = {
            "firstname": "John",
            "lastname": "Doe",
            "title": "Mr",
            "job_position": "CEO",
            "mobile_number": "+254712345678",
            "extension": "12345",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St",
            "address_line_2": "Apt 101",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
        }

        url = reverse(
            "company:officers",
            kwargs={
                "company_id": self.other_company.id,
            },
        )

        res: Response = self.client.post(url, data=payload)
        # Then I should get a 400
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)

    def test_retreive_update_delete_company_officer(self):
        #  Given an authenticated user
        OfficerFactory(company=self.company)
        self.client.force_authenticate(user=self.user)
        #  When I try to  get the officers

        res: Response = self.client.get(self.url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And list of officers should be 1
        self.assertEqual(len(res.data["results"]), 1)

        # And When I try to update an officer
        officer = self.company.officers.first()

        payload = {
            "firstname": "New",
            "lastname": "Name",
            "title": "New Mr",
            "job_position": "New CEO",
            "mobile_number": "+1234",
            "extension": "12345",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St",
            "address_line_2": "Apt 101",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
        }

        url = reverse(
            "company:officer",
            kwargs={
                "company_id": self.company.id,
                "pk": officer.id,
            },
        )

        res: Response = self.client.patch(url, data=payload)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And officer should be updated
        officer.refresh_from_db()

        self.assertEqual(officer.firstname, payload["firstname"])
        self.assertEqual(officer.lastname, payload["lastname"])
        self.assertEqual(officer.title, payload["title"])
        self.assertEqual(officer.job_position, payload["job_position"])
        self.assertEqual(officer.mobile_number, payload["mobile_number"])
        self.assertEqual(officer.extension, payload["extension"])
        self.assertEqual(officer.email, payload["email"])
        self.assertEqual(officer.address_line_1, payload["address_line_1"])
        self.assertEqual(officer.address_line_2, payload["address_line_2"])
        self.assertEqual(officer.city, payload["city"])
        self.assertEqual(officer.state, payload["state"])
        self.assertEqual(officer.zip_code, payload["zip_code"])
        self.assertEqual(officer.country, payload["country"])
        # And When I try to delete an officer
        res: Response = self.client.delete(url)
        # Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # And number of officers should be 0
        self.assertEqual(self.company.officers.count(), 0)


class LicensesTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.other_company = CompanyFactory(user=UserFactory())

        self.url = reverse(
            "company:licenses", kwargs={"company_id": self.company.id}
        )
        super().setUp()

    def test_unauthenticated_user(self):
        # Given an unauthenticated user
        self.client.force_authenticate(user=None)

        # When I try to get the licenses
        res: Response = self.client.get(self.url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to create a license
        res: Response = self.client.post(self.url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to update a license
        res: Response = self.client.patch(self.url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to delete a license
        res: Response = self.client.delete(self.url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_license(self):
        # Given an authenticated user
        self.client.force_authenticate(user=self.user)

        # When I try to create a license
        file = FileFactory.create()
        payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "License 1 description",
            "license_number": "1234",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "attachment": file.id,
        }

        res: Response = self.client.post(self.url, data=payload)
        # Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # And number of licenses should be 1
        self.assertEqual(self.company.licenses.count(), 1)

        # When I try to create a license with a company that does not belong to me
        url = reverse(
            "company:licenses", kwargs={"company_id": self.other_company.id}
        )
        res: Response = self.client.post(url, data=payload)

        # Then I should get a 404
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)

    def test_retreive_update_delete_company_license(self):
        # Given an authenticated user
        LicensesFactory(company=self.company)
        self.client.force_authenticate(user=self.user)

        # When I try to get the licenses
        res: Response = self.client.get(self.url)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And list of licenses should be 1
        self.assertEqual(len(res.data["results"]), 1)

        # And When I try to update a license
        license = self.company.licenses.first()

        file = FileFactory.create()
        payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "new license name",
            "license_number": "new license number",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "attachment": file.id,
        }

        url = reverse(
            "company:license",
            kwargs={"company_id": self.company.id, "pk": license.id},
        )

        res: Response = self.client.patch(url, data=payload)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And license should be updated
        license.refresh_from_db()

        def normalize_datetime(dt_str):
            return datetime.fromisoformat(
                dt_str.replace("Z", "+00:00")
            ).isoformat()

        self.assertEqual(license.license_type, payload["license_type"])
        self.assertEqual(license.name, payload["name"])
        self.assertEqual(license.license_number, payload["license_number"])
        self.assertEqual(str(license.valid_from), payload["valid_from"])
        self.assertEqual(
            normalize_datetime(str(license.valid_to)),
            normalize_datetime(payload["valid_to"]),
        )
        self.assertEqual(str(license.reminder), payload["reminder"])
        self.assertEqual(license.attachment.id, payload["attachment"])

        # And When I try to delete a license
        res: Response = self.client.delete(url)
        # Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # And number of licenses should be 0
        self.assertEqual(self.company.licenses.count(), 0)

    def test_create_license_with_additional_attachments(self):
        self.client.force_authenticate(user=self.user)

        # Create main attachment and additional attachments
        main_file = FileFactory.create()
        additional_files = [FileFactory.create() for _ in range(3)]

        payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "License 1 description",
            "license_number": "1234",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "attachment": main_file.id,
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(file.id) for file in additional_files],
                }
            ],
        }

        res: Response = self.client.post(self.url, data=payload, format="json")
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # Verify the additional attachments were saved
        license = self.company.licenses.first()
        self.assertEqual(license.additional_attachments.count(), 3)

        # Sort both lists before comparing
        expected_ids = sorted([file.id for file in additional_files])
        actual_ids = sorted(
            list(license.additional_attachments.values_list("id", flat=True))
        )
        self.assertListEqual(actual_ids, expected_ids)
        self.assertEqual(len(res.data["all_attachments"]), 3)

    def test_update_license_additional_attachments(self):
        # Create license with some initial attachments
        initial_attachments = [FileFactory.create() for _ in range(2)]
        license = LicensesFactory(
            company=self.company, additional_attachments=initial_attachments
        )
        self.client.force_authenticate(user=self.user)

        # Create new attachments to add
        new_attachments = [FileFactory.create() for _ in range(3)]

        url = reverse(
            "company:license",
            kwargs={"company_id": self.company.id, "pk": license.id},
        )

        payload = {
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(file.id) for file in new_attachments],
                },
                {
                    "operation": "remove",
                    "files": [str(initial_attachments[0].id)],
                },
            ]
        }

        res: Response = self.client.patch(url, data=payload, format="json")
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # Verify attachments were updated
        license.refresh_from_db()
        expected_count = (
            len(initial_attachments) - 1 + len(new_attachments)
        )  # One removed, three added
        self.assertEqual(
            license.additional_attachments.count(), expected_count
        )

        # Verify specific files
        current_attachment_ids = {
            str(id)
            for id in license.additional_attachments.values_list(
                "id", flat=True
            )
        }

        # Should not contain the removed attachment
        self.assertNotIn(
            str(initial_attachments[0].id), current_attachment_ids
        )
        # Should contain the remaining initial attachment
        self.assertIn(str(initial_attachments[1].id), current_attachment_ids)
        # Should contain all new attachments
        for new_file in new_attachments:
            self.assertIn(str(new_file.id), current_attachment_ids)

    def test_delete_license_with_attachments(self):
        # Create license with attachments
        attachments = [FileFactory.create() for _ in range(3)]
        license = LicensesFactory(
            company=self.company, additional_attachments=attachments
        )
        self.client.force_authenticate(user=self.user)

        url = reverse(
            "company:license",
            kwargs={"company_id": self.company.id, "pk": license.id},
        )

        res: Response = self.client.delete(url)
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # Verify all attachments are marked as deleted
        for attachment in attachments:
            attachment.refresh_from_db()
            self.assertTrue(attachment.is_deleted)


class InsuranceTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.other_company = CompanyFactory(user=UserFactory())

    def test_unauthenticated_user(self):

        #  Given an unauthenticated user
        self.client.force_authenticate(user=None)
        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
            },
        )

        #  When I try to list the insurances
        res = self.client.get(url)
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  And When I try to create an insurance
        res = self.client.post(url, data={})
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  And When I try to update an insurance
        res = self.client.patch(url, data={})
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  And When I try to delete an insurance
        res = self.client.delete(url)
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_insurance(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
            },
        )

        #  When I try to create an insurance

        payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "Insurance 1 description",
            "broker": "new insurance broker",
            "agent": "1234",
            "contact": "some contact",
            "extension": "12345",
            "email": "<EMAIL>",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "policy_number": "P1234",
            "policy": FileFactory().id,
        }

        res: Response = self.client.post(url, data=payload)
        #  Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # And number of insurances should be 1
        self.assertEqual(self.company.insurance_set.count(), 1)

        # When I try to create an insurance with company that does not belong to me
        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.other_company.id,
            },
        )
        res: Response = self.client.post(url, data=payload)

        # Then I should get a 404
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)

    def test_retreive_update_delete_company_insurance(self):
        #  Given an authenticated user
        InsuranceFactory(company=self.company)
        self.client.force_authenticate(user=self.user)
        #  When I try to  get the insurances

        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
            },
        )

        res: Response = self.client.get(url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And list of insurances should be 1
        self.assertEqual(len(res.data["results"]), 1)

        # And When I try to update an insurance
        file = FileFactory.create()
        insurance = self.company.insurance_set.first()

        payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "new insurance name",
            "broker": "new insurance broker",
            "agent": "new insurance number",
            "contact": "new contact",
            "extension": "12345",
            "email": "<EMAIL>",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "policy_number": "P1234",
            "policy": file.id,
        }

        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
                "pk": insurance.id,
            },
        )

        res: Response = self.client.patch(url, data=payload)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And insurance should be updated
        insurance.refresh_from_db()

        def normalize_datetime(dt_str):
            return datetime.fromisoformat(
                dt_str.replace("Z", "+00:00")
            ).isoformat()

        self.assertEqual(insurance.insurance_type, payload["insurance_type"])
        self.assertEqual(insurance.carrier, payload["carrier"])
        self.assertEqual(insurance.broker, payload["broker"])
        self.assertEqual(insurance.agent, payload["agent"])
        self.assertEqual(insurance.contact, payload["contact"])
        self.assertEqual(insurance.extension, payload["extension"])
        self.assertEqual(insurance.email, payload["email"])
        self.assertEqual(str(insurance.reminder), payload["reminder"])
        self.assertEqual(str(insurance.valid_from), payload["valid_from"])
        self.assertEqual(
            normalize_datetime(str(insurance.valid_to)),
            normalize_datetime(payload["valid_to"]),
        )
        self.assertEqual(
            str(insurance.policy_number), payload["policy_number"]
        )
        self.assertEqual(insurance.policy.id, payload["policy"])

        # And When I try to delete an insurance
        res: Response = self.client.delete(url)
        insurance.refresh_from_db()

        # Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)
        # then policy file should be deleted
        policy_file: File = insurance.policy
        self.assertTrue(policy_file.is_deleted)

        # And number of insurances should be 0
        self.assertEqual(self.company.insurance_set.count(), 0)

    def test_list_company_insurance(self):
        #  Given an authenticated user
        InsuranceFactory(company=self.company)
        self.client.force_authenticate(user=self.user)
        #  When I try to  get the insurances

        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
            },
        )

        res: Response = self.client.get(url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And list of insurances should be 1
        self.assertEqual(len(res.data["results"]), 1)

    def test_delete_company_insurance(self):
        #  Given an authenticated user
        InsuranceFactory(company=self.company)
        self.client.force_authenticate(user=self.user)

        insurance = self.company.insurance_set.first()
        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
                "pk": insurance.id,
            },
        )

        #  When I try to delete an insurance
        res: Response = self.client.delete(url)
        insurance.refresh_from_db()

        # Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)
        # then policy file should be deleted
        policy_file: File = insurance.policy
        self.assertTrue(policy_file.is_deleted)

        # And number of insurances should be 0
        self.assertEqual(self.company.insurance_set.count(), 0)

    def test_create_insurance_with_additional_attachments(self):
        self.client.force_authenticate(user=self.user)

        main_policy = FileFactory.create()
        additional_files = [FileFactory.create() for _ in range(3)]

        url = reverse(
            "company:insurance",
            kwargs={"company_id": self.company.id},
        )

        payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "Insurance 1 description",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "policy": main_policy.id,
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(file.id) for file in additional_files],
                }
            ],
        }

        res: Response = self.client.post(url, data=payload, format="json")
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        insurance = self.company.insurance_set.first()
        self.assertEqual(insurance.additional_attachments.count(), 3)
        self.assertEqual(len(res.data["all_attachments"]), 3)

    def test_delete_insurance_with_attachments(self):
        attachments = [FileFactory.create() for _ in range(3)]
        insurance = InsuranceFactory(
            company=self.company, additional_attachments=attachments
        )
        self.client.force_authenticate(user=self.user)

        url = reverse(
            "company:insurance",
            kwargs={
                "company_id": self.company.id,
                "pk": insurance.id,
            },
        )

        res: Response = self.client.delete(url)
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # Verify all attachments are marked as deleted
        for attachment in attachments:
            attachment.refresh_from_db()
            self.assertTrue(attachment.is_deleted)

    def test_archive_document(self):
        insurance = InsuranceFactory(company=self.company)
        self.client.force_authenticate(user=self.user)

        self.assertEqual(insurance.status, DocumentBaseMixin.Status.ACTIVE)

        insurance.archive()
        insurance.refresh_from_db()

        self.assertEqual(insurance.status, DocumentBaseMixin.Status.ARCHIVED)


class MemberTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.guest_user = UserFactory()

        self.guest_setting = Setting.objects.create(
            user=self.guest_user,
            favorite={"key": "value"},
            module={"name": "test"},
            timezone="UTC",
            company=self.company.id,
        )

        # Create guest member
        self.guest_member = MemberFactory(
            user=self.guest_user,
            company=self.company,
            created_by=self.user,
            role=Member.ROLE_GUEST,
            status=Member.STATUS_ACCEPTED,
        )

        self.invite_url = reverse(
            "company:create_member_invite",
            kwargs={"company_id": self.company.id},
        )
        super().setUp()

    def test_guest_cannot_invite_members(self):
        # Given a guest member
        self.client.force_authenticate(user=self.guest_user)

        # When trying to invite new users
        payload = {
            "invitee_emails": ["<EMAIL>"],
            "role": Member.ROLE_GUEST,
        }

        res: Response = self.client.post(self.invite_url, data=payload)

        # Then should be forbidden
        self.assertEqual(res.status_code, status.HTTP_403_FORBIDDEN)

    def test_guest_can_view_company_members(self):
        # Given a guest member
        self.client.force_authenticate(user=self.guest_user)

        # When requesting company members list
        url = reverse("company:user-companies-list")
        res: Response = self.client.get(url)

        # Then should succeed
        self.assertEqual(res.status_code, status.HTTP_200_OK)

    def test_guest_cannot_modify_other_members(self):
        # Given a guest member and another member
        self.client.force_authenticate(user=self.guest_user)
        other_member = MemberFactory(
            company=self.company,
            role=Member.ROLE_GUEST,
            status=Member.STATUS_PENDING,
        )

        # When trying to modify another member's status
        url = reverse(
            "company:member_invite_status_update",
            kwargs={"pk": other_member.id},
        )
        payload = {"status": Member.STATUS_ACCEPTED}

        res: Response = self.client.patch(url, data=payload)

        # Then should be forbidden
        self.assertEqual(res.status_code, status.HTTP_403_FORBIDDEN)

    def test_guest_can_view_received_invitations(self):
        # Given a guest with pending invitations
        self.client.force_authenticate(user=self.guest_user)

        # When viewing received invitations
        url = reverse("company:list_member_invitations_received")
        res: Response = self.client.get(url)

        # Then should succeed and only show pending invitations
        self.assertEqual(res.status_code, status.HTTP_200_OK)

    def test_guest_cannot_view_sent_invitations(self):
        # Given a guest member
        self.client.force_authenticate(user=self.guest_user)

        # When trying to view sent invitations
        url = reverse("company:list_member_invitations_sent")
        res: Response = self.client.get(url)

        # Then should return empty list (since guests can't send invites)
        self.assertEqual(res.status_code, status.HTTP_200_OK)
        self.assertEqual(res.data["results"], [])

    def test_expired_guest_invitation(self):
        # Given an expired invitation for a guest role
        expired_invite = MemberFactory(
            user=UserFactory(),
            company=self.company,
            role=Member.ROLE_GUEST,
            status=Member.STATUS_PENDING,
            invitation_expires_at=timezone.now() - timedelta(days=1),
        )

        # When trying to accept
        self.client.force_authenticate(user=expired_invite.user)
        url = reverse(
            "company:member_invite_status_update",
            kwargs={"pk": expired_invite.id},
        )
        payload = {"status": Member.STATUS_ACCEPTED}

        res: Response = self.client.patch(url, data=payload)

        # Then should fail
        self.assertEqual(res.status_code, status.HTTP_400_BAD_REQUEST)
        expired_invite.refresh_from_db()
        self.assertEqual(expired_invite.status, Member.STATUS_EXPIRED)
