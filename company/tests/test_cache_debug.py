import time

from accounts.factories import UserFactory
from company.factories import CompanyFactory
from company.factories import InsuranceFactory
from django.core.cache import cache
from django.test import override_settings
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient


# Use a real cache backend for debugging
CACHE_SETTINGS = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "debug-test-cache",
    }
}


@override_settings(CACHES=CACHE_SETTINGS)
class CacheDebugTest(TestCase):
    """Debug test to understand cache behavior."""

    def setUp(self):
        self.client = APIClient()
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.client.force_authenticate(user=self.user)
        cache.clear()

    def test_cache_keys_and_storage(self):
        """Test that cache keys are being generated and stored correctly."""
        print("\n" + "=" * 60)
        print("CACHE DEBUG TEST")
        print("=" * 60)

        # Create test data

        # Test cache key generation
        from core.dependency_injection import service_locator
        from django.http import QueryDict

        # Simulate a request
        query_params = QueryDict()
        cache_key = service_locator.company_service._generate_cache_key(
            self.company.id,
            query_params,
            key_type="insurance_list",
        )
        print(f"Generated cache key: {cache_key}")

        # Test manual cache storage
        test_data = [{"id": 1, "carrier": "Test"}]
        cache.set(cache_key, test_data, 300)

        # Test retrieval
        cached_data = cache.get(cache_key)
        print(f"Cached data retrieved: {cached_data}")

        self.assertEqual(cached_data, test_data)

        # Test V2 endpoint
        v2_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )
        print(f"Testing V2 URL: {v2_url}")

        # Clear cache and make first request
        cache.clear()
        start_time = time.time()
        response1 = self.client.get(v2_url)
        end_time = time.time()
        first_request_time = (end_time - start_time) * 1000

        print(
            f"First request: {first_request_time:.2f}ms, Status: {response1.status_code}, Records: {len(response1.data)}"
        )

        # Make second request (should be cached)
        start_time = time.time()
        response2 = self.client.get(v2_url)
        end_time = time.time()
        second_request_time = (end_time - start_time) * 1000

        print(
            f"Second request: {second_request_time:.2f}ms, Status: {response2.status_code}, Records: {len(response2.data)}"
        )

        # Check if cache is working
        if second_request_time < first_request_time * 0.8:
            print("✅ Cache appears to be working (second request faster)")
        else:
            print(
                "⚠️  Cache may not be working (second request not significantly faster)"
            )

        # Check cache directly
        cached_response = (
            service_locator.company_service.get_cached_insurance_response(
                response2.wsgi_request, self.company.id
            )
        )
        print(f"Direct cache check: {cached_response is not None}")

    def test_cache_with_different_params(self):
        """Test cache behavior with different query parameters."""
        print("\n" + "=" * 40)
        print("CACHE WITH QUERY PARAMS TEST")
        print("=" * 40)

        # Create test data
        InsuranceFactory(company=self.company, carrier="Carrier A")
        InsuranceFactory(company=self.company, carrier="Carrier B")

        v2_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )

        # Test different URLs
        urls = [
            v2_url,
            f"{v2_url}?search=Carrier A",
            f"{v2_url}?search=Carrier B",
            f"{v2_url}?ordering=carrier",
        ]

        for url in urls:
            response = self.client.get(url)
            print(f"URL: {url}")
            print(
                f"  Status: {response.status_code}, Records: {len(response.data)}"
            )

            # Check if this creates a different cache entry
            from core.dependency_injection import service_locator
            from urllib.parse import urlparse, parse_qs

            parsed = urlparse(url)
            parse_qs(parsed.query)
            # Convert to Django QueryDict format
            from django.http import QueryDict

            qd = QueryDict(parsed.query)

            cache_key = service_locator.company_service._generate_cache_key(
                self.company.id, qd, key_type="insurance_list"
            )
            print(f"  Cache key: {cache_key}")

    def test_queryset_vs_response_caching(self):
        """Test both queryset and response level caching."""
        print("\n" + "=" * 40)
        print("QUERYSET VS RESPONSE CACHING TEST")
        print("=" * 40)

        # Create test data
        InsuranceFactory(company=self.company)

        from core.dependency_injection import service_locator

        # Test queryset caching
        print("Testing queryset caching...")
        f"insurance_key_{self.company.id}"

        # Check if queryset is cached
        cached_queryset = service_locator.company_service.get_cached_insurance(
            self.company.id
        )
        print(f"Cached queryset: {cached_queryset is not None}")

        # Test response caching
        print("Testing response caching...")
        v2_url = reverse(
            "company_v2:insurance_v2", kwargs={"company_id": self.company.id}
        )

        # Make request
        response = self.client.get(v2_url)
        print(
            f"Response: {response.status_code}, Records: {len(response.data)}"
        )

        # Check if response is cached
        cached_response = (
            service_locator.company_service.get_cached_insurance_response(
                response.wsgi_request, self.company.id
            )
        )
        print(f"Cached response: {cached_response is not None}")

        if cached_response:
            print(f"Cached response length: {len(cached_response)}")

    def test_cache_backend(self):
        """Test the cache backend directly."""
        print("\n" + "=" * 40)
        print("CACHE BACKEND TEST")
        print("=" * 40)

        # Test basic cache operations
        test_key = "test_key_123"
        test_value = {"test": "data", "count": 42}

        # Set
        cache.set(test_key, test_value, 300)
        print(f"Set cache key: {test_key}")

        # Get
        retrieved = cache.get(test_key)
        print(f"Retrieved: {retrieved}")

        # Check if it matches
        self.assertEqual(retrieved, test_value)
        print("✅ Basic cache operations working")

        # Test cache info
        try:
            # This might not work with all cache backends
            cache_info = cache._cache.get_stats()
            print(f"Cache stats: {cache_info}")
        except:  # noqa
            print("Cache stats not available")

        # Test cache keys (Redis specific)
        try:
            import redis
            from django.conf import settings

            # Try to connect to Redis directly
            r = redis.from_url(settings.CACHES["default"]["LOCATION"])
            keys = r.keys("*")
            print(
                f"Redis keys: {[k.decode() for k in keys[:10]]}"
            )  # Show first 10 keys
        except Exception as e:
            print(f"Could not connect to Redis directly: {e}")
