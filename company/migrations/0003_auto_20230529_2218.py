# Generated by Django 3.2.17 on 2023-05-29 22:18
import uuid

import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("storage", "0001_initial"),
        ("company", "0002_auto_20230417_2324"),
    ]

    operations = [
        migrations.RenameField(
            model_name="company",
            old_name="city",
            new_name="tax_id",
        ),
        migrations.RemoveField(
            model_name="company",
            name="phone",
        ),
        migrations.RemoveField(
            model_name="company",
            name="state",
        ),
        migrations.RemoveField(
            model_name="company",
            name="zip",
        ),
        migrations.CreateModel(
            name="Officer",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=255)),
                ("fullname", models.CharField(max_length=255)),
                ("mobile_number", models.CharField(max_length=255)),
                ("email", models.EmailField(max_length=255)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="company.company",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Licenses",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("license_type", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("license_number", models.CharField(max_length=255)),
                ("valid_from", models.DateField()),
                ("valid_to", models.DateField()),
                ("reminder", models.DateField()),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="company.company",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Insurance",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("insurance_type", models.CharField(max_length=255)),
                ("carrier", models.CharField(max_length=255)),
                ("agent", models.CharField(max_length=255)),
                ("contact", models.CharField(max_length=255)),
                ("email", models.EmailField(max_length=255)),
                ("valid_from", models.DateField()),
                ("valid_to", models.DateField()),
                ("reminder", models.DateField()),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="company.company",
                    ),
                ),
                (
                    "policy",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="company_insurance",
                        to="storage.file",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
