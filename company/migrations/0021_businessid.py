# Generated by Django 3.2.17 on 2024-02-22 10:49

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0020_officer_job_position'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessID',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('type_id', models.Char<PERSON>ield(default='', max_length=255)),
                ('number_id', models.CharField(default='', max_length=255)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_ids', to='company.company')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
