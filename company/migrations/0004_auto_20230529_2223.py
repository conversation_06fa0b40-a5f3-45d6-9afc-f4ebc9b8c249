# Generated by Django 3.2.17 on 2023-05-29 22:23
import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("storage", "0001_initial"),
        ("company", "0003_auto_20230529_2218"),
    ]

    operations = [
        migrations.AlterField(
            model_name="insurance",
            name="policy",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="insurance",
                to="storage.file",
            ),
        ),
        migrations.AlterField(
            model_name="licenses",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="licenses",
                to="company.company",
            ),
        ),
        migrations.AlterField(
            model_name="officer",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="officers",
                to="company.company",
            ),
        ),
    ]
