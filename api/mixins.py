from typing import Sequence
from typing import Type

from company.models import ModulePermission
from company.permissions import CompanyModulePermission
from rest_framework.authentication import BaseAuthentication
from rest_framework.authentication import SessionAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication


class ApiAuthMixin:
    authentication_classes: Sequence[Type[BaseAuthentication]] = [
        JWTAuthentication,
        SessionAuthentication,
    ]
    module_name = ModulePermission.STORAGE

    permission_classes = [IsAuthenticated, CompanyModulePermission]


class VersionedAPIMixin:
    """
    Mixin for versioned API views.
    This mixin provides version-specific functionality based on the API version.
    """

    def get_serializer_class(self):
        """
        Return the serializer class based on the API version.
        Override this method in subclasses to provide version-specific serializers.
        """
        if hasattr(self, "versioned_serializer_classes"):
            version = self.request.version
            if version in self.versioned_serializer_classes:
                return self.versioned_serializer_classes[version]
        return super().get_serializer_class()

    def get_queryset(self):
        """
        Return the queryset based on the API version.
        Override this method in subclasses to provide version-specific querysets.
        """
        if hasattr(self, "versioned_querysets"):
            version = self.request.version
            if version in self.versioned_querysets:
                return self.versioned_querysets[version]()
        return super().get_queryset()
