from django.urls import include
from django.urls import path
from project.views import CompanyProjectDocumentView
from project.views import ListSubcontractorsByProjectView

urlpatterns = [
    path("resources/", include("resources.urls")),
    path("company/", include("company.urls")),
    path("accounts/", include("accounts.urls")),
    path("auth/", include("authentication.urls")),
    path("storage/", include("storage.urls")),
    path("project/", include("project.urls")),
    path("contacts/", include("contacts.urls")),
    path("integrations/", include("integrations.urls")),
    path("communication/", include("communication.urls")),
    path("search/", include("search_engine.urls")),
    path("general/", include("general.urls")),
    path("notification/", include("notifications.urls")),
    path(
        "projects/all-documents/",
        CompanyProjectDocumentView.as_view(),
        name="project-all-documents",
    ),
    path(
        "projects/subcontractors/",
        ListSubcontractorsByProjectView.as_view(),
        name="subcontractors-by-project",
    ),
    path("calendar/", include("calendar_app.urls")),
    path("recent-activity/", include("recent_app.urls")),
    path("literals/", include("literals.urls")),
    path("backoffice/", include("backoffice.urls")),
    path("chat/", include("chat.urls")),
]
