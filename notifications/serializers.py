from communication.serializers import EmailMessageSerializer
from communication.services.gmail_service import GmailService
from company.models import Insurance
from company.models import Licenses
from company.models import Member
from company.serializers import InsuranceSerializer
from company.serializers import LicensesSerializer
from company.serializers import MemberInviteSerializer
from core.serializers import TimezoneConverterMixin
from integrations.models import GoogleIntegration
from project.models import ProjectInvite
from project.serializers import ProjectInviteSerializer
from rest_framework import serializers
from storage.models import File
from storage.serializers import FileSerializer
from utils.utils import chat_client

from .models import Notification
from .models import NotificationPreference


class NotificationPreferenceSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = NotificationPreference
        read_only_fields = ["id", "user"]
        exclude = ("is_deleted",)


class NotificationListCreateSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    name = serializers.SerializerMethodField()
    expirationDate = serializers.SerializerMethodField()
    reminderDate = serializers.SerializerMethodField()
    original_object = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        read_only_fields = ["id", "user"]
        exclude = ("is_deleted",)

    def get_name(self, notification):
        category = notification.category
        item_id = notification.item_id

        if (
            category == Notification.Category.LICENSE_REMINDERS
            or category == Notification.Category.LICENSE_EXPIRATIONS
        ):
            license = Licenses.objects.filter(id=item_id).first()
            return license.name if license else ""

        if (
            category == Notification.Category.INSURANCE_REMINDERS
            or category == Notification.Category.INSURANCE_EXPIRATIONS
        ):
            insurance = Insurance.objects.filter(id=item_id).first()
            return insurance.carrier if insurance else ""

        if (
            category == Notification.Category.DOCUMENT_REMINDERS
            or category == Notification.Category.DOCUMENT_EXPIRATIONS
        ):
            document = File.objects.filter(id=item_id).first()
            return document.original_file_name if document else ""

        return ""

    def get_expirationDate(self, notification):
        category = notification.category
        item_id = notification.item_id

        if (
            category == Notification.Category.LICENSE_REMINDERS
            or category == Notification.Category.LICENSE_EXPIRATIONS
        ):
            license = Licenses.objects.filter(id=item_id).first()
            return license.valid_to if license else None

        if (
            category == Notification.Category.INSURANCE_REMINDERS
            or category == Notification.Category.INSURANCE_EXPIRATIONS
        ):
            insurance = Insurance.objects.filter(id=item_id).first()
            return insurance.valid_to if insurance else None

        if (
            category == Notification.Category.DOCUMENT_REMINDERS
            or category == Notification.Category.DOCUMENT_EXPIRATIONS
        ):
            document = File.objects.filter(id=item_id).first()
            return document.expire_at if document else None

        return None

    def get_reminderDate(self, notification):
        category = notification.category
        item_id = notification.item_id

        if (
            category == Notification.Category.LICENSE_REMINDERS
            or category == Notification.Category.LICENSE_EXPIRATIONS
        ):
            license = Licenses.objects.filter(id=item_id).first()
            return license.reminder if license else None

        if (
            category == Notification.Category.INSURANCE_REMINDERS
            or category == Notification.Category.INSURANCE_EXPIRATIONS
        ):
            insurance = Insurance.objects.filter(id=item_id).first()
            return insurance.reminder if insurance else None

        if (
            category == Notification.Category.DOCUMENT_REMINDERS
            or category == Notification.Category.DOCUMENT_EXPIRATIONS
        ):
            document = File.objects.filter(id=item_id).first()
            return document.reminder if document else None

        return None

    def get_original_object(self, notification):
        category = notification.category
        item_id = notification.item_id

        if category == Notification.Category.EMAILS:
            return {"message_id": item_id}

        if category in [
            Notification.Category.LICENSE_REMINDERS,
            Notification.Category.LICENSE_EXPIRATIONS,
        ]:
            license = Licenses.objects.filter(id=item_id).first()
            return LicensesSerializer(license).data if license else None

        if category in [
            Notification.Category.INSURANCE_REMINDERS,
            Notification.Category.INSURANCE_EXPIRATIONS,
        ]:
            insurance = Insurance.objects.filter(id=item_id).first()
            return InsuranceSerializer(insurance).data if insurance else None

        if category in [
            Notification.Category.DOCUMENT_REMINDERS,
            Notification.Category.DOCUMENT_EXPIRATIONS,
        ]:
            document = File.objects.filter(id=item_id).first()
            return FileSerializer(document).data if document else None

        if category == Notification.Category.PENDING_INVITES:
            # Try to get project invite first
            invite = ProjectInvite.objects.filter(id=item_id).first()
            if invite:
                return ProjectInviteSerializer(invite).data

            # If project invite not found, try member invite
            member_invite = Member.objects.filter(id=item_id).first()
            if member_invite:
                return MemberInviteSerializer(member_invite).data

        if category == Notification.Category.CHAT_MESSAGES:
            try:
                room = chat_client.get_room(
                    room_id=notification.item_id, fetch_only=True
                )
                if not room:
                    return None

                participant_id = next(
                    (
                        p.get("id")
                        for p in room.get("participants", [])
                        if p["email"] == notification.user.email
                    ),
                    None,
                )
                if not participant_id:
                    return None

                unread_data = chat_client.get_unread_messages(
                    participant_id=participant_id, room_id=notification.item_id
                )

                return {
                    "room": room,
                    "unread_messages": unread_data,
                }
            except Exception as e:
                # Log the error if you have logging configured
                import logging

                logger = logging.getLogger(__name__)
                logger.error(
                    f"Error fetching chat room {notification.item_id}: {str(e)}"
                )
                return None

        return None


class NotificationRetrieveUpdateDeleteSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    original_object = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        read_only_fields = ["id", "user"]
        exclude = ("is_deleted",)

    def get_original_object(self, notification):
        category = notification.category
        item_id = notification.item_id

        if category == Notification.Category.EMAILS:
            return {"message_id": item_id}

        if category in [
            Notification.Category.LICENSE_REMINDERS,
            Notification.Category.LICENSE_EXPIRATIONS,
        ]:
            license = Licenses.objects.filter(id=item_id).first()
            return LicensesSerializer(license).data if license else None

        if category in [
            Notification.Category.INSURANCE_REMINDERS,
            Notification.Category.INSURANCE_EXPIRATIONS,
        ]:
            insurance = Insurance.objects.filter(id=item_id).first()
            return InsuranceSerializer(insurance).data if insurance else None

        if category in [
            Notification.Category.DOCUMENT_REMINDERS,
            Notification.Category.DOCUMENT_EXPIRATIONS,
        ]:
            document = File.objects.filter(id=item_id).first()
            return FileSerializer(document).data if document else None

        if category == Notification.Category.PENDING_INVITES:
            invite = ProjectInvite.objects.filter(id=item_id).first()
            return ProjectInviteSerializer(invite).data if invite else None

        if category == Notification.Category.CHAT_MESSAGES:
            try:
                room = chat_client.get_room(room_id=notification.item_id)
                if not room:
                    return None

                participant_id = next(
                    (
                        p.get("id")
                        for p in room.get("participants", [])
                        if p["email"] == notification.user.email
                    ),
                    None,
                )
                if not participant_id:
                    return None

                unread_data = chat_client.get_unread_messages(
                    participant_id=participant_id, room_id=notification.item_id
                )

                return {
                    "room": room,
                    "unread_messages": unread_data,
                }
            except Exception as e:
                # Log the error if you have logging configured
                import logging

                logger = logging.getLogger(__name__)
                logger.error(
                    f"Error fetching chat room {notification.item_id}: {str(e)}"
                )
                return None

        return None


class EmailDetailSerializer(serializers.Serializer):
    message_id = serializers.CharField()

    def get_email_message(self, message_id):
        integration = GoogleIntegration.objects.filter(
            user=self.context["request"].user
        ).first()
        if not integration:
            return None

        gmail_service = GmailService(integration)
        return gmail_service.get_message_details(message_id)

    def to_representation(self, instance):
        email = self.get_email_message(instance["message_id"])
        return (
            {
                "message_id": instance["message_id"],
                **EmailMessageSerializer(email).data,
            }
            if email
            else {}
        )
