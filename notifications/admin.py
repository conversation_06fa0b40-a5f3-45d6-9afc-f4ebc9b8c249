from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from notifications.delete_invalid_notifications import (
    delete_invalid_notifications,
)

from .models import Notification
from .models import NotificationPreference


def delete_invalid_notifications_action(modeladmin, request, queryset):
    deleted_count = delete_invalid_notifications()
    modeladmin.message_user(
        request, f"{deleted_count} invalid notifications deleted successfully."
    )


class NotificationAdmin(ImportExportModelAdmin):
    list_display = (
        "user",
        "category",
        "item_id",
        "is_read",
        "created_at",
        "updated_at",
    )
    list_filter = ("category", "is_read", "created_at")
    search_fields = ("user__email", "item_id")
    ordering = ("-created_at",)
    raw_id_fields = ("user",)

    actions = [delete_invalid_notifications_action]


admin.site.register(Notification, NotificationAdmin)
admin.site.register(NotificationPreference)
