from accounts.models import User
from core.models import BaseModel
from django.db import models
from django.utils.translation import gettext_lazy as _


class NotificationPreferenceManager(models.Manager):
    def get_user_notification_preference(self, user):
        """
        Get or create a NotificationPreference for the given user.
        This ensures that every user always has notification preferences.
        """

        preference, _ = self.get_or_create(user=user, defaults={"user": user})

        return preference


class NotificationPreference(BaseModel):
    id = models.AutoField(primary_key=True, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    emails = models.BooleanField(default=True)
    insurance = models.BooleanField(default=True)
    license = models.BooleanField(default=True)
    document = models.BooleanField(default=True)
    pending_invites = models.BooleanField(default=True)
    chat = models.BooleanField(default=True)
    email_chat = models.BooleanField(default=True)

    objects: NotificationPreferenceManager = NotificationPreferenceManager()

    @property
    def all(self):
        return (
            self.emails
            and self.insurance
            and self.license
            and self.document
            and self.pending_invites
            and self.chat
            and self.email_chat
        )

    def __str__(self):
        return f" {self.user.email}"


class Notification(BaseModel):
    class Category:
        EMAILS = "emails"
        LICENSE_REMINDERS = "license_reminders"
        LICENSE_EXPIRATIONS = "license_expirations"
        INSURANCE_REMINDERS = "insurance_reminders"
        INSURANCE_EXPIRATIONS = "insurance_expirations"
        DOCUMENT_REMINDERS = "document_reminders"
        DOCUMENT_EXPIRATIONS = "document_expirations"
        PENDING_INVITES = "pending_invites"
        CHAT_MESSAGES = "chat_messages"

        CHOICES = (
            (EMAILS, _("Emails")),
            (LICENSE_REMINDERS, _("License Reminders")),
            (LICENSE_EXPIRATIONS, _("License Expirations")),
            (INSURANCE_REMINDERS, _("Insurance Reminders")),
            (INSURANCE_EXPIRATIONS, _("Insurance Expirations")),
            (DOCUMENT_REMINDERS, _("Document Reminders")),
            (DOCUMENT_EXPIRATIONS, _("Document Expirations")),
            (PENDING_INVITES, _("Pending Invites")),
            (CHAT_MESSAGES, _("Chat Messages")),
        )

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="notifications"
    )
    category = models.CharField(max_length=50, choices=Category.CHOICES)
    item_id = models.UUIDField()
    message = models.TextField(null=True, blank=True)
    is_read = models.BooleanField(default=False)

    def mark_as_read(self):
        self.is_read = True

    def save(self, *args, **kwargs):
        from core.dependency_injection import service_locator

        # Invalidate and trigger cache regeneration
        service_locator.notification_service.clear_notifications_cache(
            self.user
        )
        return super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        from core.dependency_injection import service_locator

        # Invalidate and trigger cache regeneration
        service_locator.notification_service.clear_notifications_cache(
            self.user
        )
        return super().delete(*args, **kwargs)

    class Meta:
        indexes = [models.Index(fields=["created_at"])]
        ordering = ["-created_at"]
