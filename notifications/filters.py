from core.constants import Features
from django_filters import rest_framework as filters

from .models import Notification

FILTER_CHOICES = (
    (Features.EXPIRATIONS, "expirations"),
    (Features.REMINDERS, "reminders"),
    (Features.DOCUMENTS, "documents"),
    (Features.LICENSES, "licenses"),
    (Features.INSURANCES, "insurances"),
    (Features.COMMUNICATIONS, "communications"),
    (Features.PENDING_INVITES, "pending_invites"),
    (Features.CHAT_MESSAGES, "chat_messages"),
)


class NotificationFilter(filters.FilterSet):
    filter_by = filters.CharFilter(method="apply_filters")

    class Meta:
        model = Notification
        fields = ["filter_by"]

    def parse_filters(self, filters_str):
        return [f.strip().lower() for f in filters_str.split(",") if f.strip()]

    def apply_filters(self, queryset, name, value):
        filters = self.parse_filters(value)

        expiration_categories = {
            Features.LICENSES: Notification.Category.LICENSE_EXPIRATIONS,
            Features.INSURANCES: Notification.Category.INSURANCE_EXPIRATIONS,
            Features.DOCUMENTS: Notification.Category.DOCUMENT_EXPIRATIONS,
        }
        reminder_categories = {
            Features.LICENSES: Notification.Category.LICENSE_REMINDERS,
            Features.INSURANCES: Notification.Category.INSURANCE_REMINDERS,
            Features.DOCUMENTS: Notification.Category.DOCUMENT_REMINDERS,
        }

        filtered_categories = set()
        specific_types = set()

        for filter_type in filters:
            if filter_type in [Features.EXPIRATIONS, Features.REMINDERS]:
                filtered_categories.add(filter_type)
            elif filter_type in [
                Features.LICENSES,
                Features.INSURANCES,
                Features.DOCUMENTS,
            ]:
                specific_types.add(filter_type)
            elif filter_type == Features.CHAT_MESSAGES:
                filtered_categories.add(Notification.Category.CHAT_MESSAGES)

        if (
            Features.EXPIRATIONS in filtered_categories
            or Features.REMINDERS in filtered_categories
        ):
            if Features.EXPIRATIONS in filtered_categories:
                filtered_categories = (
                    {expiration_categories[t] for t in specific_types}
                    if specific_types
                    else set(expiration_categories.values())
                )
            if Features.REMINDERS in filtered_categories:
                filtered_categories = (
                    {reminder_categories[t] for t in specific_types}
                    if specific_types
                    else set(reminder_categories.values())
                )
        elif specific_types:
            filtered_categories.update(
                expiration_categories[t] for t in specific_types
            )
            filtered_categories.update(
                reminder_categories[t] for t in specific_types
            )

        if Features.COMMUNICATIONS in filters:
            filtered_categories.add(Notification.Category.EMAILS)

        if Features.PENDING_INVITES in filters:
            filtered_categories.add(Notification.Category.PENDING_INVITES)

        if Features.CHAT_MESSAGES in filters:
            filtered_categories.add(Notification.Category.CHAT_MESSAGES)

        if filtered_categories:
            queryset = queryset.filter(category__in=filtered_categories)

        return queryset
