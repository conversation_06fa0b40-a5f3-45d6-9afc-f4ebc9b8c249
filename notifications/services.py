from accounts.models import User
from django.conf import settings
from django.core.cache import cache
from general.cache_keys import REDIS_CACHE_KEY


class NotificationService:
    def cache_notifications(self, user: User, notifications_qs):
        # Ensure the queryset is ordered and evaluate it.
        ordered_qs = notifications_qs.order_by("-created_at")
        # Cache a list of primary keys to avoid lazy evaluation issues.
        notifications_ids = list(ordered_qs.values_list("pk", flat=True))
        cache_key = f"{REDIS_CACHE_KEY.get_notifications_key(str(user.id))}:ordering=created_at"
        cache.set(
            cache_key, notifications_ids, settings.NOTIFICATION_CACHE_TIMEOUT
        )

    def get_cached_notifications(self, user: User):
        cache_key = f"{REDIS_CACHE_KEY.get_notifications_key(str(user.id))}:ordering=created_at"
        return cache.get(cache_key)

    def clear_notifications_cache(self, user: User):
        cache_key = f"{REDIS_CACHE_KEY.get_notifications_key(str(user.id))}:ordering=created_at"
        cache.delete(cache_key)
        # Trigger background regeneration to rebuild the cache with current ordering.
        from notifications.tasks import regenerate_notifications_cache

        regenerate_notifications_cache.delay(user.id)
