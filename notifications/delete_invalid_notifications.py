import logging

from django.db import transaction
from notifications.models import Notification
from notifications.signals import model_to_category


related_models = list(model_to_category.keys())

logger = logging.getLogger(__name__)


def delete_invalid_notifications():

    try:
        with transaction.atomic():
            valid_ids = set()

            for model in related_models:

                valid_ids.update(
                    model.objects.values_list("id", flat=True).iterator()
                )

            if not valid_ids:
                logger.warning("No valid item IDs found in related models.")
                return 0

            deleted_count, _ = Notification.objects.exclude(
                item_id__in=valid_ids
            ).delete()
            logger.info(f"Deleted {deleted_count} invalid notifications.")
            return deleted_count

    except Exception as e:
        logger.exception(f"Failed to delete invalid notifications. {str(e)}")
        return 0
