from accounts.factories import UserFactory
from communication.factories import EmailMessageFactory
from company.factories import InsuranceFactory
from company.factories import LicensesFactory
from django.urls import reverse
from django.utils import timezone
from notifications.models import Notification
from notifications.models import NotificationPreference
from rest_framework import status
from storage.factories import FileFactory
from testing.base import BaseAPITest


class NotificationPreferenceTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        super().setUp()

    def test_get_notification_preference(self):
        self.client.force_authenticate(user=self.user)

        # Ensure the GET endpoint returns the correct data
        url = reverse("notification-preference")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check if the default notification preference is created for the user
        preference = NotificationPreference.objects.get(user=self.user)
        self.assertEqual(response.data["emails"], preference.emails)
        self.assertEqual(response.data["document"], preference.document)
        self.assertEqual(response.data["insurance"], preference.insurance)
        self.assertEqual(response.data["license"], preference.license)

    def test_patch_notification_preference(self):
        self.client.force_authenticate(user=self.user)

        # Ensure the PATCH endpoint updates the notification preferences correctly
        url = reverse("notification-preference")
        data = {
            "emails": False,
            "document": False,
            "insurance": True,
            "license": True,
        }
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check if the preferences are updated in the database
        preference = NotificationPreference.objects.get(user=self.user)
        self.assertFalse(preference.emails)
        self.assertFalse(preference.document)
        self.assertTrue(preference.insurance)
        self.assertTrue(preference.license)

    def test_create_notification_preference(self):
        self.client.force_authenticate(user=self.user)

        # Ensure that a new notification preference is not created during the test
        preferences_count_before = NotificationPreference.objects.count()

        url = reverse("notification-preference")
        response = self.client.post(url)
        self.assertEqual(
            response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED
        )

        # Check if the count of preferences remains the same
        preferences_count_after = NotificationPreference.objects.count()
        self.assertEqual(preferences_count_before, preferences_count_after)

    def test_fetch_email_notifications(self):
        self.client.force_authenticate(user=self.user)

        # Create some EmailMessage instances with is_read=False for testing
        email_message_1 = EmailMessageFactory(
            is_read=False, created_by=self.user
        )
        email_message_2 = EmailMessageFactory(
            is_read=False, created_by=self.user
        )

        # Create Notification instances
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.EMAILS,
            item_id=email_message_1.id,
        )
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.EMAILS,
            item_id=email_message_2.id,
        )

        url = reverse("notifications")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)

    def test_fetch_license_notifications(self):
        self.client.force_authenticate(user=self.user)

        # Create a License instance with a reminder date and expiration date
        license_reminders = LicensesFactory(
            reminder=timezone.now(), created_by=self.user
        )
        license_expirations = LicensesFactory(
            valid_to=timezone.now(), created_by=self.user
        )

        # Create Notification instances
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.LICENSE_REMINDERS,
            item_id=license_reminders.id,
        )
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.LICENSE_EXPIRATIONS,
            item_id=license_expirations.id,
        )

        url = reverse("notifications")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(
            any(
                notification["category"]
                == Notification.Category.LICENSE_REMINDERS
                for notification in response.data["results"]
            )
        )
        self.assertTrue(
            any(
                notification["category"]
                == Notification.Category.LICENSE_EXPIRATIONS
                for notification in response.data["results"]
            )
        )

    def test_fetch_insurance_notifications(self):
        self.client.force_authenticate(user=self.user)

        # Create an Insurance instance with a reminder date and expiration date
        insurance_reminders = InsuranceFactory(
            reminder=timezone.now(), created_by=self.user
        )
        insurance_expirations = InsuranceFactory(
            valid_to=timezone.now(), created_by=self.user
        )

        # Create Notification instances
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.INSURANCE_REMINDERS,
            item_id=insurance_reminders.id,
        )
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.INSURANCE_EXPIRATIONS,
            item_id=insurance_expirations.id,
        )

        url = reverse("notifications")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(
            any(
                notification["category"]
                == Notification.Category.INSURANCE_REMINDERS
                for notification in response.data["results"]
            )
        )
        self.assertTrue(
            any(
                notification["category"]
                == Notification.Category.INSURANCE_EXPIRATIONS
                for notification in response.data["results"]
            )
        )

    def test_fetch_document_notifications(self):
        self.client.force_authenticate(user=self.user)

        # Create a File instance with a reminder date and expiration date
        document_reminder = FileFactory(
            reminder=timezone.now(), uploaded_by=self.user
        )
        document_expiration = FileFactory(
            expire_at=timezone.now(), uploaded_by=self.user
        )

        # Create Notification instances
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.DOCUMENT_REMINDERS,
            item_id=document_reminder.id,
        )
        Notification.objects.create(
            user=self.user,
            category=Notification.Category.DOCUMENT_EXPIRATIONS,
            item_id=document_expiration.id,
        )

        url = reverse("notifications")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(
            any(
                notification["category"]
                == Notification.Category.DOCUMENT_REMINDERS
                for notification in response.data["results"]
            )
        )
        self.assertTrue(
            any(
                notification["category"]
                == Notification.Category.DOCUMENT_EXPIRATIONS
                for notification in response.data["results"]
            )
        )
