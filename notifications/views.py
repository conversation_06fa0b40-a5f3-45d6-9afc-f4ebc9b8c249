from core.constants import Features
from core.dependency_injection import service_locator
from django.db.models import Case
from django.db.models import When
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .filters import NotificationFilter
from .models import Notification
from .models import NotificationPreference
from .serializers import EmailDetailSerializer
from .serializers import NotificationListCreateSerializer
from .serializers import NotificationPreferenceSerializer
from .serializers import NotificationRetrieveUpdateDeleteSerializer


class NotificationPreferenceView(generics.RetrieveUpdateAPIView):
    queryset = NotificationPreference.objects.all()
    serializer_class = NotificationPreferenceSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        user = self.request.user
        obj, _ = NotificationPreference.objects.get_or_create(user=user)
        return obj


class NotificationListCreateView(generics.ListCreateAPIView):
    serializer_class = NotificationListCreateSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = NotificationFilter

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "filter_by",
                openapi.IN_QUERY,
                description="Comma-separated list of filters",
                type=openapi.TYPE_ARRAY,
                items=openapi.Items(
                    type=openapi.TYPE_STRING,
                    enum=[
                        Features.EXPIRATIONS,
                        Features.REMINDERS,
                        Features.DOCUMENTS,
                        Features.LICENSES,
                        Features.INSURANCES,
                        Features.COMMUNICATIONS,
                        Features.PENDING_INVITES,
                        Features.CHAT_MESSAGES,
                    ],
                ),
                collection_format="csv",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        user = self.request.user
        # Try to retrieve the cached list of notification primary keys.
        cached_ids = (
            service_locator.notification_service.get_cached_notifications(user)
        )
        if cached_ids is not None:
            # Rebuild the QuerySet and preserve the order using a Case expression.
            preserved_order = Case(
                *[When(pk=pk, then=pos) for pos, pk in enumerate(cached_ids)]
            )
            queryset = Notification.objects.filter(pk__in=cached_ids).order_by(
                preserved_order
            )
            return queryset

        # If no cache is found, build the queryset with user preferences.
        queryset = Notification.objects.filter(user=user)
        (
            notification_preference,
            _,
        ) = NotificationPreference.objects.get_or_create(user=user)
        if not notification_preference.emails:
            queryset = queryset.exclude(category=Notification.Category.EMAILS)
        if not notification_preference.license:
            queryset = queryset.exclude(
                category__in=[
                    Notification.Category.LICENSE_REMINDERS,
                    Notification.Category.LICENSE_EXPIRATIONS,
                ]
            )
        if not notification_preference.insurance:
            queryset = queryset.exclude(
                category__in=[
                    Notification.Category.INSURANCE_REMINDERS,
                    Notification.Category.INSURANCE_EXPIRATIONS,
                ]
            )
        if not notification_preference.document:
            queryset = queryset.exclude(
                category__in=[
                    Notification.Category.DOCUMENT_REMINDERS,
                    Notification.Category.DOCUMENT_EXPIRATIONS,
                ]
            )
        if not notification_preference.pending_invites:
            queryset = queryset.exclude(
                category=Notification.Category.PENDING_INVITES
            )
        if not notification_preference.chat:
            queryset = queryset.exclude(
                category=Notification.Category.CHAT_MESSAGES
            )

        queryset = queryset.order_by("-created_at")
        # Cache the evaluated and ordered queryset.
        service_locator.notification_service.cache_notifications(
            user, queryset
        )
        return queryset

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class NotificationRetrieveUpdateDeleteView(
    generics.RetrieveUpdateDestroyAPIView
):
    queryset = Notification.objects.all()
    permission_classes = [IsAuthenticated]
    serializer_class = NotificationRetrieveUpdateDeleteSerializer

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_read = True
        instance.save()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class EmailDetailView(generics.GenericAPIView):
    serializer_class = EmailDetailSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data={"message_id": kwargs["message_id"]}
        )
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data)
