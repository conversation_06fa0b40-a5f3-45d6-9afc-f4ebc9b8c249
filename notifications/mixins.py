from typing import List

from company.models import Insurance
from company.models import Licenses
from core.dependency_injection import service_locator
from notifications.models import Notification
from storage.models import File


class NotificationCleanupMixin:
    """Mixin to handle cleanup of notifications when models are updated."""

    def cleanup_item_notifications(
        self, item_id: str, categories: List[str]
    ) -> None:
        """
        Delete notifications for a specific item and categories.
        Use this method when you just need to clear specific notification categories
        without checking field updates.

        Args:
            item_id: UUID of the item
            categories: List of notification categories to delete
        """
        # Bulk delete the notifications
        Notification.objects.filter(
            item_id=item_id, category__in=categories
        ).delete()
        # Clear the notifications cache for the current user
        service_locator.notification_service.clear_notifications_cache(
            self.request.user
        )

    def get_notification_categories(self, instance, updated_fields):
        """
        Determine which notification categories to clean up based on updated fields.
        Use this method for field-aware notification cleanup.

        Args:
            instance: Model instance being updated
            updated_fields: Dictionary of fields that were updated

        Returns:
            List of notification categories to clean up
        """
        categories = []

        # Define mappings for reminder and expiration notifications by model type
        reminder_mapping = {
            File: Notification.Category.DOCUMENT_REMINDERS,
            Licenses: Notification.Category.LICENSE_REMINDERS,
            Insurance: Notification.Category.INSURANCE_REMINDERS,
        }

        expiration_mapping = {
            File: Notification.Category.DOCUMENT_EXPIRATIONS,
            Licenses: Notification.Category.LICENSE_EXPIRATIONS,
            Insurance: Notification.Category.INSURANCE_EXPIRATIONS,
        }

        # Check for reminder updates
        if "reminder" in updated_fields:
            for model_class, category in reminder_mapping.items():
                if isinstance(instance, model_class):
                    categories.append(category)
                    break  # Instance should only match one type

        # Check for expiration updates
        if any(
            field in updated_fields
            for field in ["valid_from", "valid_to", "expire_at"]
        ):
            for model_class, category in expiration_mapping.items():
                if isinstance(instance, model_class):
                    categories.append(category)
                    break

        return categories

    def cleanup_notifications(self, instance, updated_fields):
        """
        Delete notifications based on field updates.
        Use this method when you need to clean up notifications based on which fields were updated.

        Args:
            instance: Model instance being updated
            updated_fields: Dictionary of fields that were updated
        """
        categories = self.get_notification_categories(instance, updated_fields)

        if categories:
            self.cleanup_item_notifications(str(instance.id), categories)
