# Generated by Django 3.2.17 on 2024-11-21 18:49
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("notifications", "0008_auto_20240904_1201"),
    ]

    operations = [
        migrations.AddField(
            model_name="notificationpreference",
            name="chat",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="notification",
            name="category",
            field=models.CharField(
                choices=[
                    ("emails", "Emails"),
                    ("license_reminders", "License Reminders"),
                    ("license_expirations", "License Expirations"),
                    ("insurance_reminders", "Insurance Reminders"),
                    ("insurance_expirations", "Insurance Expirations"),
                    ("document_reminders", "Document Reminders"),
                    ("document_expirations", "Document Expirations"),
                    ("pending_invites", "Pending Invites"),
                    ("chat_messages", "Chat Messages"),
                ],
                max_length=50,
            ),
        ),
    ]
