# Generated by Django 3.2.17 on 2024-06-24 09:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notifications', '0006_auto_20240502_1839'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.CharField(choices=[('emails', 'Emails'), ('license_reminders', 'License Reminders'), ('license_expirations', 'License Expirations'), ('insurance_reminders', 'Insurance Reminders'), ('insurance_expirations', 'Insurance Expirations'), ('document_reminders', 'Document Reminders'), ('document_expirations', 'Document Expirations')], max_length=50)),
                ('item_id', models.UUIDField()),
                ('is_read', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
