# Generated by Django 3.2.17 on 2024-05-02 18:39

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0005_notificationpreference_document_reminders'),
    ]

    operations = [
        migrations.RenameField(
            model_name='notificationpreference',
            old_name='document_expirations',
            new_name='document',
        ),
        migrations.RemoveField(
            model_name='notificationpreference',
            name='document_reminders',
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='updated_at',
            field=models.DateTime<PERSON><PERSON>(auto_now=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='notificationpreference',
            name='id',
            field=models.AutoField(editable=False, primary_key=True, serialize=False),
        ),
    ]
