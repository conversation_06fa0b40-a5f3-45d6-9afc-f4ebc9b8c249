# Generated by Django 3.2.17 on 2025-02-12 19:00
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("notifications", "0010_notificationpreference_email_chat"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="notification",
            options={"ordering": ["-created_at"]},
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["created_at"], name="notificatio_created_46ad24_idx"
            ),
        ),
    ]
