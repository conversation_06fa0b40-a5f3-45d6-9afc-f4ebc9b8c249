import json
import logging
from functools import wraps
from typing import Optional
from typing import Union

from celery import shared_task
from company.models import Insurance
from company.models import Licenses
from core.constants import Features
from core.dependency_injection import service_locator
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone
from django_celery_beat.models import ClockedSchedule
from django_celery_beat.models import PeriodicTask
from firebase_admin import messaging
from notifications.models import Notification
from notifications.models import NotificationPreference
from project.models import ProjectInvite
from storage.models import File
from utils.firebase.messaging import FirebaseMessageService
from utils.utils import create_or_update_notification

User = get_user_model()

logger = logging.getLogger(__name__)
messaging_service = FirebaseMessageService()


def handle_scheduling_errors(func):
    """Decorator to handle scheduling errors and provide logging."""

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception:
            logger.error(
                f"Error in scheduling task {func.__name__}",
                exc_info=True,
                extra={
                    "task_name": func.__name__,
                    "task_args": args,
                    "task_kwargs": kwargs,
                },
            )
            return None

    return wrapper


def schedule_notification(
    task_name: str,
    reminder_time: timezone.datetime,
    item_id: str,
    task_type: str,
) -> Optional[PeriodicTask]:
    """
    Schedule a notification with error handling and validation.

    Args:
        task_name: Unique identifier for the task
        reminder_time: When the notification should be sent
        item_id: ID of the item (insurance/license/document)
        task_type: Type of task to be executed
    """
    try:
        schedule, _ = ClockedSchedule.objects.get_or_create(
            clocked_time=reminder_time
        )

        task_params = {
            "clocked": schedule,
            "name": task_name,
            "task": task_type,
            "args": json.dumps([str(item_id)]),
            "one_off": True,
            "start_time": reminder_time,
        }

        task, created = PeriodicTask.objects.update_or_create(
            name=task_name, defaults=task_params
        )
        return task
    except Exception:
        logger.error(
            "Error scheduling notification",
            exc_info=True,
            extra={
                "task_name": task_name,
                "item_id": item_id,
                "task_type": task_type,
            },
        )
        return None


def send_notification(
    user,
    title: str,
    msg: str,
    category: str,
    item_id: Union[str, int],
    feature: str,
):
    """Send a notification to a user."""
    if user is None:
        logger.warning(
            "Attempted to send notification to None user",
            extra={"title": title, "category": category, "item_id": item_id},
        )
        return

    try:
        notification_preference = (
            NotificationPreference.objects.get_user_notification_preference(
                user
            )
        )
        if not getattr(notification_preference, feature.lower(), True):
            return

        # Create or update the notification
        create_or_update_notification(user, category, item_id)

        topic = str(user.id)
        data_payload = {
            "id": str(item_id),
            "category": feature,
        }
        message = messaging.Message(
            topic=topic,
            notification=messaging.Notification(title=title, body=msg),
            data=data_payload,
        )
        messaging_service.send_message_to_topic(message)
    except Exception:
        logger.error(
            "Error sending notification",
            exc_info=True,
            extra={
                "user_id": getattr(user, "id", None),
                "title": title,
                "category": category,
                "item_id": item_id,
            },
        )


def schedule_item_notifications(
    items: models.QuerySet,
    task_name_prefix: str,
    task_type: str,
    time_attr: str,
    item_type: str,
) -> None:
    """
    Generic function to schedule notifications for items.

    Args:
        items: QuerySet of items to schedule notifications for
        task_name_prefix: Prefix for the task name
        task_type: Type of task to execute
        time_attr: Attribute name for the time field
        item_type: Type of item ('insurance', 'license', or 'document')
    """
    start_hour = timezone.now().hour
    end_hour = start_hour + 1

    for item in items:
        if not item.created_by and not getattr(item, "uploaded_by", None):
            logger.warning(
                f"No user associated with {item_type}",
                extra={"item_id": item.id, "item_type": item_type},
            )
            continue

        reminder_time = getattr(item, time_attr)
        if not reminder_time:
            logger.warning(
                f"No reminder time for {item_type}",
                extra={"item_id": item.id, "item_type": item_type},
            )
            continue

        if reminder_time.hour in range(start_hour, end_hour):
            task_name = f"{task_name_prefix}_{item.id}"
            task = schedule_notification(
                task_name, reminder_time, item.id, task_type
            )

            if not task:
                logger.error(
                    f"Failed to schedule {item_type} notification",
                    extra={
                        "item_id": item.id,
                        "item_type": item_type,
                        "task_name": task_name,
                    },
                )


def delete_reminder_notifications(user, item_id, reminder_category):
    """Delete reminder notifications for an item when it expires."""
    try:
        # Delete the reminder notifications for this item
        notifications_deleted = Notification.objects.filter(
            user=user, item_id=item_id, category=reminder_category
        ).delete()[0]

        if notifications_deleted > 0:
            logger.info(
                f"Deleted {notifications_deleted} reminder notification(s)",
                extra={
                    "user_id": user.id,
                    "item_id": item_id,
                    "category": reminder_category,
                },
            )

            # Clear the user's notification cache to reflect changes
            service_locator.notification_service.clear_notifications_cache(
                user
            )

    except Exception:
        logger.error(
            "Error deleting reminder notifications",
            exc_info=True,
            extra={"user_id": user.id, "item_id": item_id},
        )


def schedule_document_notifications(
    files: models.QuerySet,
    task_name_prefix: str,
    task_type: str,
    time_attr: str,
) -> None:
    """Schedules notifications for documents."""
    schedule_item_notifications(
        files, task_name_prefix, task_type, time_attr, "document"
    )


@shared_task()
@handle_scheduling_errors
def schedule_documents_reminder_notifications():
    """Schedule document reminder notifications with error handling."""
    files = File.objects.get_files_that_need_to_remind_today()
    schedule_document_notifications(
        files,
        "document_reminder",
        "notifications.tasks.notify_user_about_document_reminder",
        "reminder",
    )


@shared_task()
@handle_scheduling_errors
def schedule_documents_expiration_notifications():
    """Schedule document expiration notifications with error handling."""
    files = File.objects.get_files_that_expire_today()
    schedule_document_notifications(
        files,
        "document_expire_at",
        "notifications.tasks.notify_user_about_document_expiration",
        "expire_at",
    )


@shared_task()
def notify_user_about_document_expiration(file_pk: str):
    """Send notification about document expiration."""
    try:
        file = File.objects.get(pk=file_pk)
        user = file.uploaded_by
        if not user:
            logger.warning(
                "No user associated with document", extra={"file_id": file_pk}
            )
            return

        # Delete any existing reminder notifications first
        delete_reminder_notifications(
            user, file.id, Notification.Category.DOCUMENT_REMINDERS
        )

        expire_at = file.expire_at.strftime("%d %B, %Y at %I:%M %p")
        msg = f"{file.original_file_name} document expired {expire_at}"
        send_notification(
            user,
            "Document Expiration",
            msg,
            Notification.Category.DOCUMENT_EXPIRATIONS,
            file.id,
            Features.DOCUMENTS,
        )
    except File.DoesNotExist:
        logger.error(
            "Document not found for expiration notification",
            extra={"file_id": file_pk},
        )
    except Exception:
        logger.error(
            "Error sending document expiration notification",
            exc_info=True,
            extra={"file_id": file_pk},
        )


@shared_task()
def notify_user_about_document_reminder(file_pk: str):
    """Send notification about document reminder."""
    try:
        file = File.objects.get(pk=file_pk)
        user = file.uploaded_by
        if not user:
            logger.warning(
                "No user associated with document", extra={"file_id": file_pk}
            )
            return

        expire_at = file.expire_at.strftime("%d %B, %Y at %I:%M %p")
        msg = f"{file.original_file_name} document expires {expire_at}"
        send_notification(
            user,
            "Document Expiration Reminder",
            msg,
            Notification.Category.DOCUMENT_REMINDERS,
            file.id,
            Features.DOCUMENTS,
        )
    except File.DoesNotExist:
        logger.error(
            "Document not found for reminder notification",
            extra={"file_id": file_pk},
        )
    except Exception:
        logger.error(
            "Error sending document reminder notification",
            exc_info=True,
            extra={"file_id": file_pk},
        )


@shared_task()
@handle_scheduling_errors
def schedule_insurance_reminder_notifications():
    """Schedule insurance reminder notifications with error handling."""
    insurances = Insurance.objects.get_insurances_that_need_remind_today()
    schedule_item_notifications(
        insurances,
        "insurance_reminder",
        "notifications.tasks.notify_user_about_insurance_reminder",
        "reminder",
        "insurance",
    )


@shared_task()
@handle_scheduling_errors
def schedule_insurance_expiration_notifications():
    """Schedule insurance expiration notifications with error handling."""
    insurances = Insurance.objects.get_insurances_that_need_remind_today()
    schedule_item_notifications(
        insurances,
        "insurance_valid_to",
        "notifications.tasks.notify_user_about_insurance_expiration",
        "valid_to",
        "insurance",
    )


@shared_task()
def notify_user_about_insurance_expiration(insurance_pk: str):
    """Send notification about insurance expiration."""
    try:
        insurance = Insurance.objects.get(pk=insurance_pk)
        if not insurance.created_by:
            logger.warning(
                "No user associated with insurance",
                extra={"insurance_id": insurance_pk},
            )
            return

        # Delete any existing reminder notifications first
        delete_reminder_notifications(
            insurance.created_by,
            insurance.id,
            Notification.Category.INSURANCE_REMINDERS,
        )

        valid_to = insurance.valid_to.strftime("%d %B, %Y at %I:%M %p")
        msg = f"{insurance.carrier} insurance expired {valid_to}"
        send_notification(
            insurance.created_by,
            "Insurance Expiration",
            msg,
            Notification.Category.INSURANCE_EXPIRATIONS,
            insurance.id,
            Features.INSURANCES,
        )
    except Insurance.DoesNotExist:
        logger.error(
            "Insurance not found for expiration notification",
            extra={"insurance_id": insurance_pk},
        )
    except Exception:
        logger.error(
            "Error sending insurance expiration notification",
            exc_info=True,
            extra={"insurance_id": insurance_pk},
        )


@shared_task()
def notify_user_about_insurance_reminder(insurance_pk: str):
    """Send notification about insurance reminder."""
    try:
        insurance = Insurance.objects.get(pk=insurance_pk)
        if not insurance.created_by:
            logger.warning(
                "No user associated with insurance",
                extra={"insurance_id": insurance_pk},
            )
            return

        valid_to = insurance.valid_to.strftime("%d %B, %Y at %I:%M %p")
        msg = f"{insurance.carrier} insurance expires {valid_to}"
        send_notification(
            insurance.created_by,
            "Insurance Expiration Reminder",
            msg,
            Notification.Category.INSURANCE_REMINDERS,
            insurance.id,
            Features.INSURANCES,
        )
    except Insurance.DoesNotExist:
        logger.error(
            "Insurance not found for reminder notification",
            extra={"insurance_id": insurance_pk},
        )
    except Exception:
        logger.error(
            "Error sending insurance reminder notification",
            exc_info=True,
            extra={"insurance_id": insurance_pk},
        )


@shared_task()
@handle_scheduling_errors
def schedule_license_reminder_notifications():
    """Schedule license reminder notifications with error handling."""
    licenses = Licenses.objects.get_licenses_that_need_remind_today()
    schedule_item_notifications(
        licenses,
        "license_reminder",
        "notifications.tasks.notify_user_about_license_reminder",
        "reminder",
        "license",
    )


@shared_task()
@handle_scheduling_errors
def schedule_license_expiration_notifications():
    """Schedule license expiration notifications with error handling."""
    licenses = Licenses.objects.get_licenses_that_expire_today()
    schedule_item_notifications(
        licenses,
        "license_valid_to",
        "notifications.tasks.notify_user_about_license_expiration",
        "valid_to",
        "license",
    )


@shared_task()
def notify_user_about_license_expiration(license_pk: str):
    """Send notification about license expiration."""
    try:
        license = Licenses.objects.get(pk=license_pk)
        if not license.created_by:
            logger.warning(
                "No user associated with license",
                extra={"license_id": license_pk},
            )
            return

        # Delete any existing reminder notifications first
        delete_reminder_notifications(
            license.created_by,
            license.id,
            Notification.Category.LICENSE_REMINDERS,
        )

        valid_to = license.valid_to.strftime("%d %B, %Y at %I:%M %p")
        msg = f"{license.name} license expired {valid_to}"
        send_notification(
            license.created_by,
            "License Expiration",
            msg,
            Notification.Category.LICENSE_EXPIRATIONS,
            license.id,
            Features.LICENSES,
        )
    except Licenses.DoesNotExist:
        logger.error(
            "License not found for expiration notification",
            extra={"license_id": license_pk},
        )
    except Exception:
        logger.error(
            "Error sending license expiration notification",
            exc_info=True,
            extra={"license_id": license_pk},
        )


@shared_task()
def notify_user_about_license_reminder(license_pk: str):
    """Send notification about license reminder."""
    try:
        license = Licenses.objects.get(pk=license_pk)
        if not license.created_by:
            logger.warning(
                "No user associated with license",
                extra={"license_id": license_pk},
            )
            return

        valid_to = license.valid_to.strftime("%d %B, %Y at %I:%M %p")
        msg = f"{license.name} license expires {valid_to}"
        send_notification(
            license.created_by,
            "License Expiration Reminder",
            msg,
            Notification.Category.LICENSE_REMINDERS,
            license.id,
            Features.LICENSES,
        )
    except Licenses.DoesNotExist:
        logger.error


@shared_task()
def notify_user_about_new_invite(invite_id):
    if settings.TEST_DEBUG:
        return
    invite = ProjectInvite.objects.get(pk=invite_id)

    project = invite.project
    msg = f"{project.created_by.first_name} {project.created_by.last_name} invited you to join {project.name}"
    send_notification(
        invite.user,
        "New Project Invite",
        msg,
        "pending_invites",
        invite.id,
        Features.PENDING_INVITES,
    )


@shared_task()
def schedule_invite_expiration_notifications():
    invites = ProjectInvite.objects.get_invites_that_expire_today()
    for invite in invites:
        task_name = f"invite_expiration_{invite.id}"
        schedule_notification(
            task_name,
            timezone.now(),
            invite.id,
            "notifications.tasks.notify_user_about_invite_expiration",
        )


@shared_task()
def schedule_invite_reminder_notifications():
    invites = ProjectInvite.objects.get_invites_that_need_to_remind_today()
    for invite in invites:
        task_name = f"invite_reminder_{invite.id}"
        schedule_notification(
            task_name,
            timezone.now(),
            invite.id,
            "notifications.tasks.notify_user_about_invite_reminder",
        )


@shared_task()
def notify_user_about_invite_expiration(invite_id):
    invite = ProjectInvite.objects.get(pk=invite_id)
    invite.status = "expired"
    invite.save()
    user = invite.user
    msg = f"Your invitation to join {invite.project.name} has expired"
    messaging_service.send_message_to_topic(
        messaging.Message(
            topic=str(user.id),
            notification=messaging.Notification(
                title="Invite Expiration", body=msg
            ),
            data={
                "id": str(invite.id),
                "category": Features.PROJECT_INVITE_EXPIRATIONS,
            },
        )
    )


@shared_task()
def notify_user_about_invite_reminder(invite_id):
    invite = ProjectInvite.objects.get(pk=invite_id)
    user = invite.user
    expire_at = (invite.created_at + timezone.timedelta(days=7)).strftime(
        "%d %B, %Y at %I:%M %p"
    )
    msg = (
        f"Your invitation to join {invite.project.name} expires on {expire_at}"
    )
    messaging_service.send_message_to_topic(
        messaging.Message(
            topic=str(user.id),
            notification=messaging.Notification(
                title="Invite Expiration Reminder", body=msg
            ),
            data={
                "id": str(invite.id),
                "category": Features.PROJECT_INVITE_REMINDERS,
            },
        )
    )


@shared_task()
def delete_expired_invites_notifications():
    """
    Delete expired invites and their associated notifications.
    Also clear the notifications cache for the invite's user.
    """
    invites_to_delete = ProjectInvite.objects.get_invites_to_delete()
    for invite in invites_to_delete:
        # Delete related notifications for pending invites
        Notification.objects.filter(
            item_id=invite.id, category=Notification.Category.PENDING_INVITES
        ).delete()
        # Clear notifications cache for the user if available
        if invite.user:
            service_locator.notification_service.clear_notifications_cache(
                invite.user
            )


@shared_task()
def delete_accepted_rejected_invites_notifications():
    """
    Delete accepted or rejected invites that were not removed during updates.
    """
    invites_to_delete = ProjectInvite.objects.filter(
        status__in=["accepted", "rejected"]
    )
    for invite in invites_to_delete:
        # Delete any notifications associated with this invite
        Notification.objects.filter(
            item_id=invite.id, category=Notification.Category.PENDING_INVITES
        ).delete()
        # Clear notifications cache for the user if available
        if invite.user:
            service_locator.notification_service.clear_notifications_cache(
                invite.user
            )


@shared_task()
def regenerate_notifications_cache(user_id):
    """
    Regenerate notifications cache for a specific user in the background.
    """
    try:
        from core.dependency_injection import service_locator

        user = User.objects.get(id=user_id)
        queryset = Notification.objects.filter(user=user)
        (
            notification_preference,
            _,
        ) = NotificationPreference.objects.get_or_create(user=user)

        if not notification_preference.emails:
            queryset = queryset.exclude(category=Notification.Category.EMAILS)
        if not notification_preference.license:
            queryset = queryset.exclude(
                category__in=[
                    Notification.Category.LICENSE_REMINDERS,
                    Notification.Category.LICENSE_EXPIRATIONS,
                ]
            )
        if not notification_preference.insurance:
            queryset = queryset.exclude(
                category__in=[
                    Notification.Category.INSURANCE_REMINDERS,
                    Notification.Category.INSURANCE_EXPIRATIONS,
                ]
            )
        if not notification_preference.document:
            queryset = queryset.exclude(
                category__in=[
                    Notification.Category.DOCUMENT_REMINDERS,
                    Notification.Category.DOCUMENT_EXPIRATIONS,
                ]
            )
        if not notification_preference.pending_invites:
            queryset = queryset.exclude(
                category=Notification.Category.PENDING_INVITES
            )
        if not notification_preference.chat:
            queryset = queryset.exclude(
                category=Notification.Category.CHAT_MESSAGES
            )

        queryset = queryset.order_by("-created_at")
        service_locator.notification_service.cache_notifications(
            user, queryset
        )
    except User.DoesNotExist:
        logger.error(
            f"User with ID {user_id} not found for cache regeneration"
        )
    except Exception as e:
        logger.error(f"Error regenerating notifications cache: {e}")


@shared_task()
def cleanup_task_from_db():
    PeriodicTask.objects.filter(enabled=False, one_off=True).delete()
