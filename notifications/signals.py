from accounts.models import User
from company.models import Insurance
from company.models import Licenses
from company.models import Member
from django.db.models.signals import post_save
from django.db.models.signals import pre_delete
from notifications.models import Notification
from project.models import ProjectInvite
from storage.models import File


# Helper function to delete Notification entries
def delete_notification(instance, category):
    Notification.objects.filter(
        item_id=instance.id, category=category
    ).delete()


model_to_category = {
    Licenses: [
        Notification.Category.LICENSE_REMINDERS,
        Notification.Category.LICENSE_EXPIRATIONS,
    ],
    Insurance: [
        Notification.Category.INSURANCE_REMINDERS,
        Notification.Category.INSURANCE_EXPIRATIONS,
    ],
    File: [
        Notification.Category.DOCUMENT_REMINDERS,
        Notification.Category.DOCUMENT_EXPIRATIONS,
    ],
    ProjectInvite: Notification.Category.PENDING_INVITES,
    Member: Notification.Category.PENDING_INVITES,
}


# Common signal handler function
def handle_notification(sender, instance, created=False, **kwargs):

    category = model_to_category.get(type(instance))
    if not category:
        return

    if created:
        return

    # Only delete notifications if instance is soft deleted
    if hasattr(instance, "is_deleted") and instance.is_deleted:
        from core.dependency_injection import service_locator

        if isinstance(category, list):
            notifications = Notification.objects.filter(
                item_id=instance.id, category__in=category
            )
        else:
            notifications = Notification.objects.filter(
                item_id=instance.id, category=category
            )

        # Clear cache for each affected user's notifications
        for notification in notifications:
            try:
                user = User.objects.get(id=notification.user)
                service_locator.notification_service.clear_notifications_cache(
                    user
                )
            except User.DoesNotExist:
                continue

        notifications.delete()


def delete_notifications_for_associated_model(
    sender, instance, created=False, **kwargs
):

    Notification.objects.filter(item_id=instance.id).delete()


# Connect to the post_save signal only for soft delete
post_save_models = [Licenses, Insurance, File]
for model in post_save_models:
    post_save.connect(handle_notification, sender=model)

for model in [*post_save_models, ProjectInvite, Member]:
    pre_delete.connect(delete_notifications_for_associated_model, sender=model)
