from unittest import skip
from unittest.mock import Mock
from unittest.mock import patch

from backoffice.service import (
    AnalyticsService,
)  # Adjust import based on your actual service class name
from django.test import TestCase


class BackofficeServiceTest(TestCase):
    def setUp(self):
        self.service = AnalyticsService()
        self.user_id = "test_user_123"

    @skip("run this test locally only")
    @patch("backoffice.service.RunReportRequest")
    def test_get_user_analytics(self, mock_run_report_request):
        # Mock response row
        mock_row = Mock()
        mock_row.dimension_values = [
            Mock(value="2024-01-01"),  # date
            Mock(value="page_view"),  # event_name
            Mock(value="engagement"),  # event_category
            Mock(value="home_page"),  # event_label
        ]
        mock_row.metric_values = [
            Mock(value="5"),  # event_count
            Mock(value="10.5"),  # event_value
        ]

        # Mock response
        mock_response = Mock()
        mock_response.rows = [mock_row]

        # Mock the run_request method
        self.service.run_request = Mock(return_value=mock_response)

        # Call the method
        result = self.service.get_user_analytics(
            user_id=self.user_id, days=30, offset_days=0
        )

        # Verify the request was made correctly
        self.service.run_request.assert_called_once()

        # Assert the structure of the response
        self.assertIn("daily_data", result)
        self.assertIn("summary", result)

        # Check daily data
        daily_data = result["daily_data"]
        self.assertIn("2024-01-01", daily_data)
        self.assertIn("page_view", daily_data["2024-01-01"])

        event_data = daily_data["2024-01-01"]["page_view"]
        self.assertEqual(event_data["category"], "engagement")
        self.assertEqual(event_data["label"], "home_page")
        self.assertEqual(event_data["count"], 5)
        self.assertEqual(event_data["value"], 10.5)

        # Test with multiple days of data
        mock_row2 = Mock()
        mock_row2.dimension_values = [
            Mock(value="2024-01-02"),
            Mock(value="button_click"),
            Mock(value="interaction"),
            Mock(value="submit_button"),
        ]
        mock_row2.metric_values = [Mock(value="3"), Mock(value="6.0")]

        mock_response.rows = [mock_row, mock_row2]

        result = self.service.get_user_analytics(
            user_id=self.user_id, days=30, offset_days=0
        )

        # Verify multiple days are handled correctly
        self.assertEqual(len(result["daily_data"]), 2)
        self.assertIn("2024-01-02", result["daily_data"])

        # Test with empty response
        mock_response.rows = []
        result = self.service.get_user_analytics(
            user_id=self.user_id, days=30, offset_days=0
        )

        self.assertEqual(result["daily_data"], {})
