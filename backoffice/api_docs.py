from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from backoffice.category import GOOGLE_ANALYTICS_REPORT_CATEGORY

GOOGLE_ANALYTICS_SWAGGER_DOCS = swagger_auto_schema(
    operation_description="Filter by days or offset_days or category",
    responses={},
    manual_parameters=[


        openapi.Parameter(
            "days",
            openapi.IN_QUERY,
            description="Days",
            type=openapi.TYPE_NUMBER,
        ),
        openapi.Parameter(
            "offset_days",
            openapi.IN_QUERY,
            description="Offset Days",
            type=openapi.TYPE_NUMBER,
        ),
        openapi.Parameter(
            "category",
            openapi.IN_QUERY,
            description="Category",
            type=openapi.TYPE_STRING,
            enum=GOOGLE_ANALYTICS_REPORT_CATEGORY.ALL,
        ),
    ],
)
