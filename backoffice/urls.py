from django.urls import path

from . import views


app_name = "backoffice"
urlpatterns = [
    path("users/", views.ListUsersView.as_view(), name="auth_token"),
    path(
        "users/<str:pk>/",
        views.RetrieveUserView.as_view(),
        name="backoffice_user_details",
    ),
    path(
        "users/<str:pk>/analytics/",
        views.RetrieveUserGoogleAnalyticsView.as_view(),
        name="backoffice_user_analytics",
    ),
    path(
        "statistics/",
        views.DashboardStatisticsView.as_view(),
        name="statistics",
    ),
    path(
        "google-analytics/",
        views.GoogleAnalyticsView.as_view(),
        name="google_analytics",
    ),
    path(
        "support", views.SupportListMessageView.as_view(), name="support-list"
    ),
    path(
        "auditlogs/",
        views.AuditLogList.as_view(),
        name="auditlog-list",
    ),
    # path("auditlogs/<int:pk>/", views.AuditLogDetail.as_view(), name="auditlog-detail"),
]
