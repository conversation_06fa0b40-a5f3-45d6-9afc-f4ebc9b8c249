from auditlog.models import LogEntry
from core.dependency_injection import service_locator
from crum import get_current_request
from django.db.models.signals import post_save
from django.dispatch import receiver


@receiver(post_save, sender=LogEntry)
def update_user_agent(sender, instance: LogEntry, created: bool, **kwargs):

    request = get_current_request()
    if not request:
        return

    user_agent_string = request.META.get("HTTP_USER_AGENT")
    if not user_agent_string:
        return

    service_locator.analytics_service.process_user_agent_and_update_log.delay(
        "self", instance.id, user_agent_string
    )


def init_signals():

    pass
