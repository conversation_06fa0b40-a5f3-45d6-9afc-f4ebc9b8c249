class GOOGLE_ANALYTICS_REPORT_CATEGORY:
    COMPREHENSIVE_REPORT = "comprehensive_report"
    USER_BEHAVIOR = "user_behavior"
    PAGE_PERFORMANCE = "page_performance"
    USER_ACQUISITION = "user_acquisition"
    USER_TECHNOLOGY = "user_technology"
    ACTIVE_USERS_COUNTRY = "active_users_country"
    ACTIVE_USERS_DEVICE_MODEL = "active_users_device_model"

    ALL = [COMPREHENSIVE_REPORT, USER_BEHAVIOR, PAGE_PERFORMANCE, USER_ACQUISITION,
           USER_TECHNOLOGY, ACTIVE_USERS_COUNTRY, ACTIVE_USERS_DEVICE_MODEL]

    CHOICES = [
        (COMPREHENSIVE_REPORT, "Comprehensive Report"),
        (USER_BEHAVIOR, "User Behavior"),
        (PAGE_PERFORMANCE, "Page Performance"),
        (USER_ACQUISITION, "User Acquisition"),
        (USER_TECHNOLOGY, "User Technology"),
        (ACTIVE_USERS_COUNTRY, "Active Users Country"),
        (ACTIVE_USERS_DEVICE_MODEL, "Active Users Device Model"),
    ]
