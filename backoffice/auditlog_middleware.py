import jwt
from auditlog.context import set_actor
from django.conf import settings
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from jwt.exceptions import ExpiredSignatureError

User = get_user_model()


class SetAuditLogActorMiddleware:

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return self.get_response(request)

        token = auth_header.split(" ")[1]

        try:
            # Decode the JWT token to get the user ID
            decoded_token = jwt.decode(
                token, settings.SECRET_KEY, algorithms=["HS256"]
            )
            user_id = decoded_token["user_id"]
            user = User.objects.get(id=user_id)
        except (ValueError, jwt.exceptions.DecodeError, User.DoesNotExist):
            return self.get_response(request)
        except ExpiredSignatureError:

            error = {
                "detail": "Given token not valid for any token type",
                "code": "token_not_valid",
                "messages": [
                    {
                        "token_class": "AccessToken",
                        "token_type": "access",
                        "message": "Token is invalid or expired",
                    }
                ],
            }

            return JsonResponse(error, status=401)

        with set_actor(user):

            response = self.get_response(request)

        return response
