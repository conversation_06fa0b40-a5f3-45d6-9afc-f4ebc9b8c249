import logging
from collections import defaultdict
from datetime import datetime
from datetime import timedelta
from typing import Any
from typing import Dict
from typing import List

from celery import shared_task
from google.analytics.data_v1beta.types import DateRange
from google.analytics.data_v1beta.types import Filter
from google.analytics.data_v1beta.types import FilterExpression
from google.analytics.data_v1beta.types import RunReportRequest
from utils.firebase import AnalyticsConfig
from utils.firebase import AnalyticsReporter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AnalyticsService:
    def __init__(self):
        self.config = AnalyticsConfig()
        self.reporter = AnalyticsReporter(self.config)

    def run_request(self, request):
        try:
            return self.reporter.client.run_report(request)
        except Exception as e:
            logger.error(f"Error fetching data: {str(e)}", exc_info=True)

            return None

    def create_date_range(
        self, days: int = 30, offset_days: int = 0
    ) -> List[Dict[str, str]]:
        """Create date range with optional offset"""
        end_date = datetime.now() - timedelta(days=offset_days)
        start_date = end_date - timedelta(days=days)
        return [
            {
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d"),
            }
        ]

    def get_comprehensive_user_report(
        self, days: int = 30, offset_days: int = 0
    ) -> Dict[str, Any]:
        request = RunReportRequest(
            property=f"properties/{self.config.property_id}",
            dimensions=[
                {"name": "date"},
                {"name": "sessionSource"},
                {"name": "sessionMedium"},
                {"name": "sessionDefaultChannelGrouping"},
                {"name": "country"},
                {"name": "region"},
            ],
            metrics=[
                {"name": "totalUsers"},
                {"name": "newUsers"},
                {"name": "eventCount"},
                {"name": "screenPageViews"},
                {"name": "userEngagementDuration"},
                {"name": "averageSessionDuration"},
                {"name": "bounceRate"},
            ],
            date_ranges=self.create_date_range(days, offset_days),
        )

        response = self.run_request(request)

        data_by_date = defaultdict(lambda: defaultdict(dict))
        for row in response.rows:
            date = datetime.strptime(
                row.dimension_values[0].value, "%Y%m%d"
            ).strftime("%Y-%m-%d")
            channel_grouping = (
                row.dimension_values[3].value
                if len(row.dimension_values) > 3
                else "unknown"
            )
            country = row.dimension_values[4].value
            region = row.dimension_values[5].value

            metrics = {
                "total_users": int(row.metric_values[0].value),
                "new_users": int(row.metric_values[1].value),
                "event_count": int(row.metric_values[2].value),
                "page_views": int(row.metric_values[3].value),
                "engagement_duration": float(row.metric_values[4].value),
                "avg_session_duration": float(row.metric_values[5].value),
                "bounce_rate": float(row.metric_values[6].value),
            }

            key = f"{channel_grouping}-{country}-{region}"
            data_by_date[date][key] = metrics

        return {
            "daily_data": {str(k): v for k, v in data_by_date.items()},
            "summary": self.calculate_summary(data_by_date),
        }

    def calculate_summary(self, data_by_date: Dict) -> Dict[str, Any]:
        summary = defaultdict(lambda: defaultdict(float))

        for date_data in data_by_date.values():
            for source_medium, metrics in date_data.items():
                for metric_name, value in metrics.items():
                    summary[source_medium][metric_name] += value

        return dict(summary)

    def get_page_performance(
        self, days: int = 30, offset_days: int = 0
    ) -> dict:
        request = RunReportRequest(
            property=f"properties/{self.config.property_id}",
            dimensions=[
                {"name": "pagePath"},
                {"name": "pageTitle"},
                {"name": "pageReferrer"},
                {"name": "country"},
            ],
            metrics=[
                {"name": "screenPageViews"},
                {"name": "totalUsers"},
                {"name": "averageSessionDuration"},
                {"name": "bounceRate"},
            ],
            date_ranges=self.create_date_range(
                days=days, offset_days=offset_days
            ),
        )

        response = self.run_request(request)
        data = []

        for row in response.rows:
            data.append(
                {
                    "page_path": row.dimension_values[0].value,
                    "page_title": row.dimension_values[1].value.lower(),
                    "page_referrer": row.dimension_values[2].value,
                    "country": row.dimension_values[3].value,
                    "page_views": int(row.metric_values[0].value),
                    "users": int(row.metric_values[1].value),
                    "avg_session_duration": float(row.metric_values[2].value),
                    "bounce_rate": float(row.metric_values[3].value),
                }
            )

        return data

    def get_user_acquisition(
        self, days: int = 30, offset_days: int = 0
    ) -> dict:
        request = RunReportRequest(
            property=f"properties/{self.config.property_id}",
            dimensions=[
                {"name": "sessionSource"},
                {"name": "sessionMedium"},
                {"name": "sessionCampaignName"},
                {"name": "sessionDefaultChannelGrouping"},
                {"name": "country"},
                {"name": "region"},
            ],
            metrics=[
                {"name": "totalUsers"},
                {"name": "newUsers"},
                {"name": "eventCount"},
                {"name": "conversions"},
                {"name": "screenPageViews"},
            ],
            date_ranges=self.create_date_range(days, offset_days=offset_days),
        )

        response = self.run_request(request)
        data = []

        for row in response.rows:
            data.append(
                {
                    "source": row.dimension_values[0].value,
                    "medium": row.dimension_values[1].value,
                    "campaign": row.dimension_values[2].value,
                    "channel_grouping": row.dimension_values[3].value,
                    "country": row.dimension_values[4].value,
                    "region": row.dimension_values[5].value,
                    "total_users": int(row.metric_values[0].value),
                    "new_users": int(row.metric_values[1].value),
                    "events": int(row.metric_values[2].value),
                    "conversions": int(row.metric_values[3].value),
                    "page_views": int(row.metric_values[4].value),
                }
            )

        return data

    def get_user_technology(self, days: int = 30, offset_days: int = 0):
        dimensions_list = [
            [{"name": "deviceCategory"}, {"name": "deviceModel"}],
            [{"name": "browser"}, {"name": "appVersion"}],
            [{"name": "operatingSystem"}, {"name": "operatingSystemVersion"}],
        ]

        results = {}
        for dimensions in dimensions_list:
            request = RunReportRequest(
                property=f"properties/{self.config.property_id}",
                dimensions=dimensions,
                metrics=[
                    {"name": "totalUsers"},
                    {"name": "screenPageViews"},
                    {"name": "averageSessionDuration"},
                ],
                date_ranges=self.create_date_range(
                    days=days, offset_days=offset_days
                ),
            )

            response = self.run_request(request)
            data = []

            for row in response.rows:
                data.append(
                    {
                        "primary": row.dimension_values[0].value,
                        "secondary": row.dimension_values[1].value,
                        "users": int(row.metric_values[0].value),
                        "page_views": int(row.metric_values[1].value),
                        "avg_session_duration": float(
                            row.metric_values[2].value
                        ),
                    }
                )

            aggregated_data, total_data = self.aggregate_device_technology(
                data
            )

            results[dimensions[0]["name"]] = {
                "data": data,
                "aggregated": aggregated_data,
                "total": total_data,
            }

        return results

    def aggregate_device_technology(self, user_technology_data):
        aggregated_data = defaultdict(
            lambda: {"users": 0, "pageViews": 0, "avgSessionDuration": 0.0}
        )

        total_data = {"users": 0, "pageViews": 0, "avgSessionDuration": 0.0}

        for device_data in user_technology_data:
            primary = device_data["primary"]
            users = device_data["users"]
            page_views = device_data["page_views"]
            avg_session_duration = device_data["avg_session_duration"]

            aggregated_data[primary]["users"] += users
            aggregated_data[primary]["pageViews"] += page_views
            aggregated_data[primary][
                "avgSessionDuration"
            ] += avg_session_duration

            total_data["users"] += users
            total_data["pageViews"] += page_views
            total_data["avgSessionDuration"] += avg_session_duration

        if total_data["users"] > 0:
            total_data["avgSessionDuration"] /= total_data["users"]

        return dict(aggregated_data), total_data

    def get_user_behavior(
        self, days: int = 30, offset_days: int = 0
    ) -> Dict[str, Any]:
        """Analyze user behavior patterns"""
        request = RunReportRequest(
            property=f"properties/{self.config.property_id}",
            dimensions=[{"name": "hour"}, {"name": "dayOfWeek"}],
            metrics=[
                {"name": "totalUsers"},
                {"name": "screenPageViews"},
                {"name": "averageSessionDuration"},
            ],
            date_ranges=self.create_date_range(
                days=days, offset_days=offset_days
            ),
        )

        response = self.run_request(request)
        hourly_data = defaultdict(lambda: defaultdict(dict))

        for row in response.rows:
            hour = int(row.dimension_values[0].value)
            day = int(row.dimension_values[1].value)

            hourly_data[day][hour] = {
                "users": int(row.metric_values[0].value),
                "page_views": int(row.metric_values[1].value),
                "avg_session_duration": float(row.metric_values[2].value),
            }

        return dict(hourly_data)

    def get_active_users_by_country(
        self, days: int = 30, offset_days: int = 0
    ) -> dict:
        """Get active users segmented by country"""
        request = RunReportRequest(
            property=f"properties/{self.config.property_id}",
            dimensions=[{"name": "country"}],
            metrics=[{"name": "activeUsers"}],
            date_ranges=self.create_date_range(days, offset_days),
        )

        response = self.run_request(request)
        data = []

        for row in response.rows:
            data.append(
                {
                    "country": row.dimension_values[0].value,
                    "active_users": int(row.metric_values[0].value),
                }
            )

        return data

    def get_active_users_by_device_model(
        self, days: int = 30, offset_days: int = 0
    ) -> dict:
        """Get active users segmented by device model"""
        request = RunReportRequest(
            property=f"properties/{self.config.property_id}",
            dimensions=[{"name": "deviceModel"}],
            metrics=[{"name": "activeUsers"}],
            date_ranges=self.create_date_range(
                days=days, offset_days=offset_days
            ),
        )

        response = self.run_request(request)
        data = []

        for row in response.rows:
            data.append(
                {
                    "device_model": row.dimension_values[0].value,
                    "active_users": int(row.metric_values[0].value),
                }
            )

        return data

    def get_user_analytics(
        self, user_id: str, days: int = 30, offset_days: int = 0
    ) -> Dict[str, Any]:
        """Get analytics data for a user identified by the user_id."""
        # Validate property ID
        property_id = self.config.property_id
        if not property_id or not property_id.isdigit():
            raise ValueError(
                "Invalid property ID. A numeric Property ID is required."
            )

        # Prepare date ranges
        end_date = (datetime.utcnow() - timedelta(days=offset_days)).strftime(
            "%Y-%m-%d"
        )
        start_date = (
            datetime.utcnow() - timedelta(days=days + offset_days)
        ).strftime("%Y-%m-%d")

        # Construct the RunReportRequest
        request = RunReportRequest(
            property=f"properties/{property_id}",
            dimensions=[
                {"name": "date"},
                {"name": "eventName"},
                {"name": "userId"},
            ],
            metrics=[
                {"name": "screenPageViews"},
            ],
            date_ranges=[DateRange(start_date=start_date, end_date=end_date)],
            dimension_filter=FilterExpression(
                filter=Filter(
                    field_name="user_id",  # Use custom dimension for user_id
                    string_filter=Filter.StringFilter(
                        match_type=Filter.StringFilter.MatchType.EXACT,
                        value=user_id,
                    ),
                )
            ),
        )

        # Send the request and get the response
        response = self.run_request(request)

        if not response or not response.rows:
            return {"daily_data": {}, "summary": {}}

        # Process the response rows
        data_by_date = defaultdict(lambda: defaultdict(dict))
        for row in response.rows:
            date = row.dimension_values[0].value
            event_name = row.dimension_values[1].value
            event_count = int(row.metric_values[0].value)

            data_by_date[date][event_name] = {
                "count": event_count,
            }

        return {
            "daily_data": {str(k): v for k, v in data_by_date.items()},
            "summary": {
                "total_events": sum(
                    event["count"]
                    for date_events in data_by_date.values()
                    for event in date_events.values()
                )
            },
        }

    @shared_task
    def process_user_agent_and_update_log(self, log_entry_id, user_agent_data):
        from auditlog.models import LogEntry
        from core.dependency_injection import service_locator

        if not user_agent_data:
            return

        user_agent = service_locator.general_service.get_user_agent(
            user_agent_data
        )

        try:
            log_entry: LogEntry = LogEntry.objects.get(id=log_entry_id)

            additional_data = (
                log_entry.additional_data if log_entry.additional_data else {}
            )

            user_agent = {
                "device": user_agent.device.family,
                "family": user_agent.os.family,
                "browser": user_agent.browser.family,
                "browser_version": user_agent.browser.version_string,
                "is_mobile": user_agent.is_mobile,
                "is_tablet": user_agent.is_tablet,
                "is_pc": user_agent.is_pc,
                "is_bot": user_agent.is_bot,
                "is_touch_capable": user_agent.is_touch_capable,
                "os": user_agent.os.family,
                "os_version": user_agent.os.version_string,
            }

            additional_data["user_agent"] = user_agent

            LogEntry.objects.filter(id=log_entry.pk).update(
                additional_data=additional_data
            )

        except LogEntry.DoesNotExist:
            pass
