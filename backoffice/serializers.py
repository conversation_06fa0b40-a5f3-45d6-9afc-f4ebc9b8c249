from accounts.serializers import UserSummarySerializer
from auditlog.models import LogEntry
from backoffice.category import GOOGLE_ANALYTICS_REPORT_CATEGORY
from core.dependency_injection import service_locator
from rest_framework import serializers


class KPISerializer(serializers.Serializer):
    value = serializers.IntegerField()
    name = serializers.CharField()
    description = serializers.CharField()
    percentage = serializers.FloatField()


class DashboardStatisticsSerializer(serializers.Serializer):
    kpis = KPISerializer(many=True)


class GoogleAnalyticsSerializer(serializers.Serializer):
    days = serializers.IntegerField(default=30)
    offset_days = serializers.IntegerField(default=0, allow_null=True)
    category = serializers.ChoiceField(
        choices=GOOGLE_ANALYTICS_REPORT_CATEGORY.ALL, allow_null=True
    )

    def fetch_reports(self, days, offset_days=0, category=None):
        all_reports = {
            GOOGLE_ANALYTICS_REPORT_CATEGORY.COMPREHENSIVE_REPORT: service_locator.analytics_service.get_comprehensive_user_report(
                days, offset_days
            ),
            GOOGLE_ANALYTICS_REPORT_CATEGORY.USER_BEHAVIOR: service_locator.analytics_service.get_user_behavior(
                days, offset_days
            ),
            GOOGLE_ANALYTICS_REPORT_CATEGORY.PAGE_PERFORMANCE: service_locator.analytics_service.get_page_performance(
                days, offset_days
            ),
            GOOGLE_ANALYTICS_REPORT_CATEGORY.USER_ACQUISITION: service_locator.analytics_service.get_user_acquisition(
                days, offset_days
            ),
            GOOGLE_ANALYTICS_REPORT_CATEGORY.USER_TECHNOLOGY: service_locator.analytics_service.get_user_technology(
                days, offset_days
            ),
            GOOGLE_ANALYTICS_REPORT_CATEGORY.ACTIVE_USERS_COUNTRY: service_locator.analytics_service.get_active_users_by_country(
                days, offset_days
            ),
            GOOGLE_ANALYTICS_REPORT_CATEGORY.ACTIVE_USERS_DEVICE_MODEL: service_locator.analytics_service.get_active_users_by_device_model(
                days, offset_days
            ),
        }

        if category:
            return {category: all_reports.get(category)}

        return all_reports


class BaseAuditLogSerializer(serializers.ModelSerializer):
    actor = UserSummarySerializer(read_only=True)
    name = serializers.SerializerMethodField()
    app_name = serializers.CharField(source="content_type.app_label")
    action = serializers.SerializerMethodField()

    class Meta:
        model = LogEntry
        fields = [
            "id",
            "app_name",
            "action",
            "timestamp",
            "object_pk",
            "object_repr",
            "actor",
            "name",
            "additional_data",
        ]

    def get_action(self, obj: LogEntry):
        return f"{obj.get_action_display()}d"

    def get_name(self, obj: LogEntry):
        return obj.object_repr


class AuditLogSerializer(BaseAuditLogSerializer):
    pass


class AbstractAuditLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = None
        fields = "__all__"


class AuditLogDetailSerializer(BaseAuditLogSerializer):
    details = serializers.SerializerMethodField()

    def get_details(self, obj: LogEntry):
        try:
            content_type_model: LogEntry = obj.content_type.model_class()
            AbstractAuditLogSerializer.Meta.model = content_type_model
            query_set = content_type_model.objects.filter(
                pk=obj.object_pk
            ).first()
            return AbstractAuditLogSerializer(query_set).data
        except Exception:
            return None

    class Meta:
        model = LogEntry
        fields = "__all__"
