from accounts.models import User
from accounts.serializers import UserDetailsSerializer
from accounts.serializers import UserSummarySerializer
from auditlog.models import LogEntry
from backoffice.api_docs import GOOGLE_ANALYTICS_SWAGGER_DOCS
from backoffice.filters import BackofficeUserFilter
from backoffice.filters import LogFilter
from backoffice.serializers import DashboardStatisticsSerializer
from backoffice.serializers import GoogleAnalyticsSerializer
from core.dependency_injection import service_locator
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from general.models import SupportMessage
from general.serializers import SupportMessageSerializer
from rest_framework import filters
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.generics import ListAPIView
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAdminUser
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from .serializers import AuditLogDetailSerializer
from .serializers import AuditLogSerializer


class BackofficeViewMixin:
    permission_classes = [IsAuthenticated, IsAdminUser]


class ListUsersView(BackofficeViewMixin, ListAPIView):
    queryset = User.objects.all()
    serializer_class = UserSummarySerializer
    filter_backends = [
        filters.OrderingFilter,
        filters.SearchFilter,
        DjangoFilterBackend,
    ]
    filterset_class = BackofficeUserFilter
    ordering_fields = "__all__"
    search_fields = ["email", "first_name", "last_name", "mobile"]


class RetrieveUserView(BackofficeViewMixin, RetrieveAPIView):
    serializer_class = UserDetailsSerializer
    queryset = User.objects.all()


def generateUserKPIs():
    current_date = timezone.now()
    last_month_date = current_date.replace(
        day=1, month=max(current_date.month - 1, 1)
    )
    this_month_date = timezone.now().replace()

    all_users = User.objects.get_queryset()
    total_users = all_users.count()

    users_created_this_month = all_users.filter_by_date(
        after=last_month_date, before=this_month_date
    )

    active_users = all_users.active().count()
    inactive_users = all_users.inactive().count()

    total_users_kpi = {
        "value": total_users,
        "name": "Total Users",
        "description": f"{users_created_this_month.count()} users join this month",
        "percentage": round(
            (users_created_this_month.count() / total_users) * 100, 2
        ),
    }

    active_users_kpi = {
        "value": active_users,
        "name": "Active Users",
        "description": f"{inactive_users} users are inactive",
        "percentage": round((active_users / total_users) * 100, 2),
    }

    staff_users = all_users.staff().count()

    staff_users_kpi = {
        "value": staff_users,
        "name": "Staff Users",
        "description": f"{staff_users} {'is' if staff_users<2 else 'are' } Staff users",
        "percentage": round((staff_users / total_users) * 100, 2),
    }

    return [total_users_kpi, active_users_kpi, staff_users_kpi]


class DashboardStatisticsView(BackofficeViewMixin, GenericAPIView):
    serializer_class = DashboardStatisticsSerializer

    def get(self, request, *args, **kwargs):
        users_kpis = generateUserKPIs()
        serializer = self.serializer_class(data={"kpis": users_kpis})
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data)


class GoogleAnalyticsView(BackofficeViewMixin, GenericAPIView):
    serializer_class = GoogleAnalyticsSerializer

    @GOOGLE_ANALYTICS_SWAGGER_DOCS
    def get(self, request: Request):
        days = request.query_params.get("days", 30)
        offset_days = request.query_params.get("offset_days", 0)
        category = request.query_params.get("category", None)
        serializer = GoogleAnalyticsSerializer(
            data={
                "days": days,
                "offset_days": offset_days,
                "category": category,
            }
        )

        if serializer.is_valid():
            reports = serializer.fetch_reports(
                serializer.validated_data["days"],
                serializer.validated_data["offset_days"],
                serializer.validated_data["category"],
            )
            return Response(reports, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RetrieveUserGoogleAnalyticsView(BackofficeViewMixin, RetrieveAPIView):
    def get_queryset(self):
        return User.objects.all()

    def retrieve(self, request, *args, **kwargs):
        user = self.get_object()
        service_locator.analytics_service.get_user_analytics(str(user.id)),
        return Response(status=status.HTTP_200_OK)


class SupportListMessageView(BackofficeViewMixin, ListAPIView):
    serializer_class = SupportMessageSerializer
    queryset = SupportMessage.objects.all()


class AuditLogList(BackofficeViewMixin, ListAPIView):
    serializer_class = AuditLogSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = LogFilter
    queryset = LogEntry.objects.order_by("-timestamp").select_related(
        "content_type"
    )


class AuditLogDetail(BackofficeViewMixin, RetrieveAPIView):
    queryset = LogEntry.objects.all()
    serializer_class = AuditLogDetailSerializer
    filter_backends = [DjangoFilterBackend]
