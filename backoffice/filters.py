import django_filters
from accounts.models import User
from auditlog.models import LogEntry
from django.db import models
from django.db.models.query import QuerySet


class BackofficeUserFilter(django_filters.FilterSet):
    email = django_filters.CharFilter(lookup_expr="icontains")
    first_name = django_filters.CharFilter(lookup_expr="icontains")
    last_name = django_filters.CharFilter(lookup_expr="icontains")
    mobile = django_filters.CharFilter(lookup_expr="icontains")
    timezone = django_filters.CharFilter(
        lookup_expr="icontains", method="filter_timezone"
    )

    def filter_timezone(self, queryset: QuerySet[User], name, value):
        from general.models import Setting

        return queryset.annotate(
            timezone=models.Subquery(
                Setting.objects.filter(user=models.OuterRef("pk")).values(
                    "timezone"
                )[:1]
            )
        ).filter(timezone__icontains=value)

    class Meta:
        model = User
        fields = ["email", "first_name", "last_name", "mobile", "timezone"]


class LogFilter(django_filters.FilterSet):

    after = django_filters.DateFilter(
        field_name="timestamp__date", lookup_expr="gte"
    )
    before = django_filters.DateFilter(
        field_name="timestamp__date", lookup_expr="lte"
    )

    email = django_filters.CharFilter(
        field_name="actor__email",
        help_text="user email",
    )
    action = django_filters.CharFilter(
        field_name="action",
        help_text="action performed eg create,access,save,delete",
    )
    user = django_filters.UUIDFilter(field_name="actor__id", required=True)
    app_name = django_filters.CharFilter(
        field_name="content_type__app_label",
        help_text="app name eg project,company,storage",
    )

    class Meta:
        model = LogEntry
        fields = [
            "user",
            "email",
            "action",
            "after",
            "before",
            "app_name",
        ]
