import logging
import warnings

from django.conf import settings
from django.test.runner import DiscoverRunner


class CustomTestRunner(DiscoverRunner):
    # fixtures = ["user_roles/fixtures/user_roles.json"]

    def setup_test_environment(self):
        # Custom setup code before running tests
        # This method is called once before the test suite is executed.
        super().setup_test_environment()
        logging.disable(logging.CRITICAL)
        settings.DEBUG = True
        settings.TEST_DEBUG = True
        settings.FILE_UPLOAD_STORAGE = "local"
        settings.IS_USING_LOCAL_STORAGE = True

        # update CACHE setting
        settings.CACHES = {
            "default": {
                "BACKEND": "django.core.cache.backends.dummy.DummyCache",
            }
        }

    def teardown_test_environment(self):
        # Custom teardown code after running tests
        # This method is called once after all tests have been executed.
        pass

    def run_tests(self, test_labels, extra_tests=None, **kwargs):

        with warnings.catch_warnings():
            # warnings.simplefilter("ignore", ResourceWarning)
            warnings.filterwarnings("ignore", category=RuntimeWarning)
            return super().run_tests(
                test_labels, extra_tests=extra_tests, **kwargs
            )
