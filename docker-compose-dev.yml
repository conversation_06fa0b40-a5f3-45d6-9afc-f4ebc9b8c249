version: "3.8"

services:
  db:
    image: postgres:14.1-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432"
    volumes:
      - db:/var/lib/postgresql/data

  web:
    build: .
    command: bash -c "bash ./scripts/entrypoint.bash"
    container_name: tuulbox_core
    volumes:
      - .:/tuulbox_core
    env_file:
      - .env
    environment:
      - DATABASE_NAME=postgres
      - DATABASE_HOST=db
    ports:
      - "7000:7000"
    expose:
      - 7000
    depends_on:
      - db

  nginx:
    build: ./nginx
    ports:
      - 1337:80
    depends_on:
      - web

volumes:
  db:
    driver: local
