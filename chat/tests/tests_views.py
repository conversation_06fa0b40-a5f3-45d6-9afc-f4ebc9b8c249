import uuid
from unittest.mock import call
from unittest.mock import patch

from accounts.factories import UserFactory
from accounts.models import User
from chat.models import ProjectRoom
from contacts.factories import ContactFactory
from django.urls import reverse
from project.factories import ProjectFactory
from rest_framework import status
from rest_framework.test import APITestCase


class ChatIntegrationTest(APITestCase):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)

        # Fixed UUIDs for consistent testing
        self.room_id = uuid.UUID("7db12001-816e-4ad0-a432-a475d84fe409")
        self.chat_id = uuid.UUID("8db12001-816e-4ad0-a432-a475d84fe410")
        self.timestamp = "2024-10-31T21:45:02.392Z"

        # Authenticated client
        self.client.force_authenticate(user=self.user)

    def _get_url(self, action, room_id=None, **kwargs):
        """Helper method to generate URLs for different actions"""
        room_id = room_id or self.room_id

        url_patterns = {
            "room": reverse("room", kwargs={"room_id": room_id}),
            "participants": reverse(
                "room-participants", kwargs={"room_id": room_id}
            ),
            "add_participants": reverse(
                "room-add-participants", kwargs={"room_id": room_id}
            ),
            "add_participants_by_ids": reverse(
                "room-add-participants-by-ids", kwargs={"room_id": room_id}
            ),
            "remove_participant": reverse(
                "room-remove-participant",
                kwargs={
                    "room_id": room_id,
                    "participant_id": kwargs.get("participant_id"),
                },
            )
            if kwargs.get("participant_id")
            else None,
            "messages": reverse("room-messages", kwargs={"room_id": room_id}),
            "update_message": reverse(
                "message-operations",
                kwargs={"room_id": room_id, "chat_id": kwargs["chat_id"]},
            )
            if kwargs.get("chat_id")
            else None,
            "delete_message": reverse(
                "message-operations",
                kwargs={"room_id": room_id, "chat_id": kwargs["chat_id"]},
            )
            if kwargs.get("chat_id")
            else None,
        }
        return url_patterns[action]

    @patch("chat.views.ChatClient")
    def test_create_project_room(self, MockChatClient):
        """Test creating a new project chat room"""
        # Create a project first
        self.project = ProjectFactory(
            created_by=self.user, name="Test Project"
        )

        url = reverse("project-rooms")
        # Create contacts
        contacts = ContactFactory.create_batch(2)
        manual_participants = [
            {
                "name": "Manual User",
                "email": "<EMAIL>",
                "data": {"key": "value"},
            }
        ]

        data = {
            "project_id": [str(self.project.id)],
            "name": "Test Project Room",
            "tags": [str(self.project.id)],
            "contact_ids": [str(contact.id) for contact in contacts],
            "manual_participants": manual_participants,
            "group_avatar": "https://example.com/image.png",
        }

        # Mock the chat service response with project room data
        mock_room_data = {
            "id": str(self.room_id),
            "name": "Test Project Room",  # Match the name in the request
            "tags": [str(self.project.id)],
            "participants": [
                {
                    "id": "participant-1",
                    "name": self.user.get_full_name(),
                    "email": self.user.email,
                },
                # Additional participants will be added by the service
            ],
            "group_avatar": "https://example.com/image.png",
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.create_room.return_value = mock_room_data

        response = self.client.post(url, data=data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["created_by"]["id"], str(self.user.id))

        # Check that room was created with correct members
        room = ProjectRoom.objects.get(room_id=self.room_id)
        self.assertIn(self.user, room.members.all())

        # Check that users were created for contacts and added as members
        for contact in contacts:
            user = User.objects.get(email=contact.email)
            self.assertIn(user, room.members.all())

        # Check that manual participant user was created and added as member
        manual_user = User.objects.get(email="<EMAIL>")
        self.assertIn(manual_user, room.members.all())

        # Verify chat client was called with correct data
        mock_client_instance.create_room.assert_called_once()
        call_args = mock_client_instance.create_room.call_args[0][0]
        self.assertEqual(
            len(call_args["participants"]), 4
        )  # creator + 2 contacts + 1 manual

    @patch("chat.views.ChatClient")
    def test_get_project_rooms(self, MockChatClient):
        """Test retrieving all chat rooms for a project"""
        url = reverse("project-rooms")
        response = self.client.get(url, {"project_id": str(self.project.id)})

        # Create some test rooms in the database
        room = ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room 1",
            created_by=self.user,
        )
        room.members.add(self.user)  # Add current user as member

        # Create another room where user is not a member
        other_room = ProjectRoom.objects.create(
            room_id=uuid.uuid4(),
            project=self.project,
            name="Test Room 2",
            created_by=self.user,
        )
        # Ensure the user is not a member of the other room
        self.assertNotIn(self.user, other_room.members.all())

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 1
        )  # Should only see room where user is member
        self.assertEqual(response.data[0]["name"], "Test Room 1")

    @patch("chat.views.ChatClient")
    def test_create_project_room_missing_participants(self, MockChatClient):
        """Test creating a project room without participants"""
        url = reverse("project-rooms")

        data = {
            "project_id": [str(self.project.id)],
            "name": "Test Project Room",
            "contact_ids": [],
            "manual_participants": [],
        }

        response = self.client.post(url, data=data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("detail", response.data)
        self.assertEqual(
            response.data["detail"],
            "At least one additional participant is required.",
        )

    @patch("chat.views.ChatClient")
    def test_get_room(self, MockChatClient):
        """Test retrieving a chat room"""
        participant_id = str(uuid.uuid4())
        mock_room_data = {
            "id": self.room_id,
            "name": "Test Room",
            "created_at": self.timestamp,
            "participants": [
                {
                    "id": participant_id,
                    "name": self.user.get_full_name(),
                    "email": self.user.email,
                }
            ],
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data

        response = self.client.get(self._get_url("room"))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.room_id)

        # Verify both calls to get_room
        mock_client_instance.get_room.assert_has_calls(
            [
                # First call to verify room participation
                call(room_id=self.room_id),
                # Second call to get room data with participant info
                call(
                    room_id=self.room_id,
                    participant_id=participant_id,
                    last_n_messages=None,
                    participant_timezone="UTC",
                ),
            ]
        )

    @patch("chat.views.ChatClient")
    def test_update_project_room(self, MockChatClient):
        """Test updating a project chat room"""
        # Create a project room in the database
        project_room = ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room",
            created_by=self.user,
        )

        mock_room_data = {
            "id": self.room_id,
            "name": "Test Room",
            "created_at": self.timestamp,
            "data": {"key": "value"},
            "tags": ["tag1", "tag2"],
            "group_avatar": "https://example.com/image.png",
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data
        mock_client_instance.update_room.return_value = {
            **mock_room_data,
            "name": "Updated Room",
        }

        update_data = {
            "name": "Updated Room",
            "data": {"new_key": "new_value"},
            "group_avatar": "https://example.com/image.png",
        }

        response = self.client.patch(
            self._get_url("room"), data=update_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], "Updated Room")

        # Verify local database was updated
        project_room.refresh_from_db()
        self.assertEqual(project_room.name, "Updated Room")

        # Verify chat service was updated with correct data including tags
        mock_client_instance.update_room.assert_called_once_with(
            room_id=self.room_id,
            data={
                "name": "Updated Room",
                "data": {"key": "value", "new_key": "new_value"},
                "tags": ["tag1", "tag2"],
                "group_avatar": "https://example.com/image.png",
            },
        )

    @patch("chat.views.ChatClient")
    def test_update_room_not_found(self, MockChatClient):
        """Test updating a non-existent room"""
        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = None

        update_data = {
            "name": "Updated Room",
            "data": {"new_key": "new_value"},
            "group_avatar": "https://example.com/image.png",
        }

        response = self.client.patch(
            self._get_url("room"), data=update_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    @patch("chat.views.ChatClient")
    def test_delete_project_room_successful(self, MockChatClient):
        """Test successful deletion of project room from both chat service and local database"""
        ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room",
            created_by=self.user,
        )

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = {
            "participants": [
                {"email": self.user.email, "id": "participant-123"}
            ]
        }
        mock_client_instance.delete_room.return_value = None

        response = self.client.delete(
            self._get_url("room", room_id=self.room_id)
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        mock_client_instance.delete_room.assert_called_once_with(
            room_id=self.room_id, participant_id="participant-123"
        )
        self.assertFalse(
            ProjectRoom.objects.filter(room_id=self.room_id).exists()
        )

    @patch("chat.views.ChatClient")
    def test_delete_room_service_failure(self, MockChatClient):
        """Test deletion when chat service fails but local deletion succeeds"""
        ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room",
            created_by=self.user,
        )

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = {
            "participants": [
                {"email": self.user.email, "id": "participant-123"}
            ]
        }
        mock_client_instance.delete_room.side_effect = Exception(
            "Chat service error"
        )

        response = self.client.delete(
            self._get_url("room", room_id=self.room_id)
        )

        self.assertEqual(
            response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        mock_client_instance.delete_room.assert_called_once_with(
            room_id=self.room_id, participant_id="participant-123"
        )
        self.assertFalse(
            ProjectRoom.objects.filter(room_id=self.room_id).exists()
        )

    @patch("chat.views.ChatClient")
    def test_add_participants(self, MockChatClient):
        """Test adding participants to a chat room"""
        # Create a project room first
        room = ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room",
            created_by=self.user,
        )
        room.members.add(self.user)

        contacts = ContactFactory.create_batch(2)
        contact_ids = [str(contact.id) for contact in contacts]
        manual_participant = {
            "name": "Manual User",
            "email": "<EMAIL>",
            "data": {"key": "value"},
        }

        mock_participants_data = {
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": f"{contact.first_name} {contact.last_name}",
                    "email": contact.email,
                }
                for contact in contacts
            ]
            + [
                {
                    "id": str(uuid.uuid4()),
                    "name": manual_participant["name"],
                    "email": manual_participant["email"],
                }
            ]
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.add_participants.return_value = (
            mock_participants_data
        )

        response = self.client.post(
            self._get_url("add_participants"),
            data={
                "contact_ids": contact_ids,
                "manual_participants": [manual_participant],
            },
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data["participants"]), 3
        )  # 2 contacts + 1 manual

        # Verify users were created and added as members
        for contact in contacts:
            user = User.objects.get(email=contact.email)
            self.assertIn(user, room.members.all())

        manual_user = User.objects.get(email="<EMAIL>")
        self.assertIn(manual_user, room.members.all())

        mock_client_instance.add_participants.assert_called_once()

    @patch("chat.views.ChatClient")
    def test_remove_participant(self, MockChatClient):
        """Test removing a participant from a chat room"""
        # Create a project room and add a participant
        room = ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room",
            created_by=self.user,
        )

        participant_email = "<EMAIL>"
        participant_user = UserFactory(email=participant_email)
        room.members.add(participant_user)

        participant_id = uuid.uuid4()

        mock_client_instance = MockChatClient.return_value
        # Mock get_participant to return participant details
        mock_client_instance.get_participant.return_value = {
            "id": str(participant_id),
            "email": participant_email,
        }
        mock_client_instance.remove_participant.return_value = {
            "success": True
        }

        response = self.client.post(
            self._get_url("remove_participant", participant_id=participant_id)
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify participant was removed from members
        self.assertNotIn(participant_user, room.members.all())

        mock_client_instance.remove_participant.assert_called_once_with(
            room_id=self.room_id, participant_id=participant_id
        )

    @patch("chat.views.ChatClient")
    def test_remove_participant_nonexistent_user(self, MockChatClient):
        """Test removing a participant that doesn't exist in the database"""
        ProjectRoom.objects.create(
            room_id=self.room_id,
            project=self.project,
            name="Test Room",
            created_by=self.user,
        )

        participant_id = uuid.uuid4()

        mock_client_instance = MockChatClient.return_value
        # Mock get_participant to return participant details
        mock_client_instance.get_participant.return_value = {
            "id": str(participant_id),
            "email": "<EMAIL>",
        }
        mock_client_instance.remove_participant.return_value = {
            "success": True
        }

        response = self.client.post(
            self._get_url("remove_participant", participant_id=participant_id)
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_client_instance.remove_participant.assert_called_once()

    @patch("chat.views.ChatClient")
    def test_add_participants_by_ids(self, MockChatClient):
        """Test adding participants by their IDs"""
        participant_ids = [str(uuid.uuid4()) for _ in range(2)]

        mock_participants_data = {
            "participants": [
                {
                    "id": pid,
                    "name": f"User {i}",
                    "email": f"user{i}@example.com",
                }
                for i, pid in enumerate(participant_ids)
            ]
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.add_participants_by_ids.return_value = (
            mock_participants_data
        )

        response = self.client.post(
            self._get_url("add_participants_by_ids"),
            data=[participant_ids],
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["participants"]), 1)
        mock_client_instance.add_participants_by_ids.assert_called_once()

    @patch("chat.views.ChatClient")
    def test_get_participants(self, MockChatClient):
        """Test listing participants in a chat room"""
        mock_room_data = {
            "id": self.room_id,
            "name": "Test Room",
            "created_at": self.timestamp,
        }

        mock_participants_data = {
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": self.user.get_full_name(),
                    "email": self.user.email,
                },
                {
                    "id": str(uuid.uuid4()),
                    "name": "Another User",
                    "email": "<EMAIL>",
                },
            ],
            "total": 2,
            "page": 1,
            "size": 50,
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data
        mock_client_instance.get_participants.return_value = (
            mock_participants_data
        )

        response = self.client.get(self._get_url("participants"))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["participants"]), 2)
        mock_client_instance.get_participants.assert_called_once()

    @patch("chat.views.ChatClient")
    def test_send_message(self, MockChatClient):
        """Test sending a message in a chat room"""
        # Create ProjectRoom instance
        ProjectRoom.objects.create(
            room_id=self.room_id, name="Test Room", created_by=self.user
        )

        mock_room_data = {
            "id": self.room_id,
            "name": "Test Room",
            "created_at": self.timestamp,
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": self.user.get_full_name(),
                    "email": self.user.email,
                }
            ],
        }
        mock_message_data = {
            "id": str(uuid.uuid4()),
            "content": "Test message",
            "room_id": self.room_id,
            "participant_id": mock_room_data["participants"][0]["id"],
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data
        mock_client_instance.create_chat.return_value = mock_message_data

        response = self.client.post(
            self._get_url("messages"),
            data={
                "content": "Test message",
            },
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["content"], "Test message")
        mock_client_instance.create_chat.assert_called_once()

    @patch("chat.views.ChatClient")
    def test_send_message_not_participant(self, MockChatClient):
        """Test sending a message when user is not a participant"""
        # Create ProjectRoom instance
        ProjectRoom.objects.create(
            room_id=self.room_id, name="Test Room", created_by=self.user
        )

        mock_room_data = {
            "id": self.room_id,
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": "Other User",
                    "email": "<EMAIL>",
                }
            ],
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data

        response = self.client.post(
            self._get_url("messages"),
            data={
                "content": "Test message",
            },
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        mock_client_instance.create_chat.assert_not_called()

    @patch("chat.views.ChatClient")
    def test_get_messages(self, MockChatClient):
        """Test retrieving messages from a chat room"""
        # Mock room data with participants
        mock_room_data = {
            "id": str(self.room_id),
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "email": self.user.email,
                }
            ],
        }

        mock_messages_data = [
            {
                "id": str(uuid.uuid4()),
                "content": "Message 1",
                "created_at": self.timestamp,
            },
            {
                "id": str(uuid.uuid4()),
                "content": "Message 2",
                "created_at": self.timestamp,
            },
        ]

        mock_client_instance = MockChatClient.return_value
        # Mock the get_room method
        mock_client_instance.get_room.return_value = mock_room_data
        # Mock the get_chats_in_room method
        mock_client_instance.get_chats_in_room.return_value = (
            mock_messages_data
        )

        response = self.client.get(self._get_url("messages"))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        # Verify get_room was called with UUID object
        mock_client_instance.get_room.assert_called_once_with(
            room_id=self.room_id  # Use UUID object directly
        )

        # Verify get_chats_in_room was called with correct parameters
        mock_client_instance.get_chats_in_room.assert_called_once_with(
            room_id=self.room_id,  # Use UUID object directly
            participant_id=mock_room_data["participants"][0]["id"],
            order="desc",
        )

    @patch("chat.views.ChatClient")
    def test_update_message(self, MockChatClient):
        """Test updating a chat message"""
        mock_room_data = {
            "id": str(self.room_id),
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": self.user.get_full_name(),
                    "email": self.user.email,
                }
            ],
        }

        mock_updated_message = {
            "id": str(self.chat_id),
            "content": "Updated message content",
            "updated_at": self.timestamp,
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data
        mock_client_instance.update_chat.return_value = mock_updated_message

        data = {"content": "Updated message content", "attachments": []}

        response = self.client.patch(
            self._get_url("update_message", chat_id=self.chat_id),
            data=data,
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["content"], data["content"])
        mock_client_instance.update_chat.assert_called_once()

    @patch("chat.views.ChatClient")
    def test_delete_message(self, MockChatClient):
        """Test deleting a chat message"""
        # Mock the room response to include the test user as a participant
        mock_room_data = {
            "id": str(self.room_id),
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": self.user.get_full_name(),
                    "email": self.user.email,
                }
            ],
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data
        mock_client_instance.delete_chat.return_value = None

        response = self.client.delete(
            self._get_url("delete_message", chat_id=self.chat_id)
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        mock_client_instance.delete_chat.assert_called_once_with(
            str(self.chat_id)
        )

    @patch("chat.views.ChatClient")
    def test_update_message_not_participant(self, MockChatClient):
        """Test updating a message when user is not a participant"""
        mock_room_data = {
            "id": str(self.room_id),
            "participants": [
                {
                    "id": str(uuid.uuid4()),
                    "name": "Other User",
                    "email": "<EMAIL>",
                }
            ],
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_room.return_value = mock_room_data

        data = {"content": "Updated message content"}

        response = self.client.patch(
            self._get_url("update_message", chat_id=self.chat_id),
            data=data,
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("detail", response.data)
        mock_client_instance.update_chat.assert_not_called()

    @patch("chat.views.ChatClient")
    def test_create_attachment(self, MockChatClient):
        """Test creating new attachments"""
        url = reverse("create-attachment")

        data = [
            {
                "filename": "test.pdf",
                "mime_type": "application/pdf",
                "participant_id": str(uuid.uuid4()),
            }
        ]

        mock_attachment_data = [
            {
                "id": str(uuid.uuid4()),
                "filename": "test.pdf",
                "mime_type": "application/pdf",
                "url": "https://example.com/test.pdf",
            }
        ]

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.create_attachment.return_value = (
            mock_attachment_data
        )

        response = self.client.post(url, data=data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["filename"], data[0]["filename"])
        mock_client_instance.create_attachment.assert_called_once_with(data)

    @patch("chat.views.ChatClient")
    def test_get_attachment(self, MockChatClient):
        """Test getting a presigned URL for an attachment"""
        attachment_id = uuid.uuid4()
        url = reverse(
            "get-delete-attachment", kwargs={"attachment_id": attachment_id}
        )

        mock_presigned_data = {
            "url": "https://example.com/presigned-url",
            "filename": "test.pdf",
            "mime_type": "application/pdf",
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.generate_presigned_url.return_value = (
            mock_presigned_data
        )

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["url"], mock_presigned_data["url"])

    @patch("chat.views.ChatClient")
    def test_delete_attachment(self, MockChatClient):
        """Test deleting an attachment"""
        attachment_id = uuid.uuid4()
        url = reverse(
            "get-delete-attachment", kwargs={"attachment_id": attachment_id}
        )

        MockChatClient.return_value

        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    @patch("chat.views.ChatClient")
    def test_get_unread_messages(self, MockChatClient):
        """Test retrieving unread messages"""
        participant_id = uuid.uuid4()
        mock_messages = [
            {
                "id": str(uuid.uuid4()),
                "content": "Test message 1",
                "sent_at": "2024-01-01T12:00:00Z",
                "sender_id": str(uuid.uuid4()),
            },
            {
                "id": str(uuid.uuid4()),
                "content": "Test message 2",
                "sent_at": "2024-01-01T12:01:00Z",
                "sender_id": str(uuid.uuid4()),
            },
        ]

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.get_unread_messages.return_value = mock_messages

        url = reverse(
            "room-unread-messages",
            kwargs={"participant_id": participant_id},
        )

        response = self.client.get(url)

        # Assert successful response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, mock_messages)
        mock_client_instance.get_unread_messages.assert_called_once_with(
            participant_id=participant_id
        )

    @patch("chat.views.ChatClient")
    def test_generate_participant_token(self, MockChatClient):
        """Test generating a token for a participant"""
        participant_id = uuid.uuid4()
        mock_token_data = {
            "token": "sample.jwt.token",
            "expires_at": "2024-12-31T23:59:59Z",
        }

        mock_client_instance = MockChatClient.return_value
        mock_client_instance.generate_token_for_participant.return_value = (
            mock_token_data
        )

        url = reverse(
            "generate-participant-token",
            kwargs={"participant_id": participant_id},
        )

        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["token"], mock_token_data["token"])
        self.assertEqual(
            response.data["expires_at"], mock_token_data["expires_at"]
        )
        mock_client_instance.generate_token_for_participant.assert_called_once_with(
            participant_id
        )
