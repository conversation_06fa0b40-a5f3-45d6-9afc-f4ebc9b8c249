from django.urls import include
from django.urls import path
from rest_framework.routers import DefaultRouter

from .views import ChatViewSet

router = DefaultRouter()
router.register(r"rooms", ChatViewSet, basename="chat")

room_patterns = [
    path(
        "",
        ChatViewSet.as_view(
            {
                "get": "get_room",
                "patch": "update_room",
                "delete": "delete_room",
            }
        ),
        name="room",
    ),
    path(
        "participant/",
        ChatViewSet.as_view({"get": "participants"}),
        name="room-participants",
    ),
    path(
        "add-participant/",
        ChatViewSet.as_view({"post": "add_participants"}),
        name="room-add-participants",
    ),
    path(
        "add-participants-by-ids/",
        ChatViewSet.as_view({"post": "add_participants_by_ids"}),
        name="room-add-participants-by-ids",
    ),
    path(
        "remove-participant/<uuid:participant_id>/",
        ChatViewSet.as_view({"post": "remove_participant"}),
        name="room-remove-participant",
    ),
    path(
        "search",
        ChatViewSet.as_view({"get": "search_messages"}),
        name="room-messages-search",
    ),
]

search_patterns = [
    path(
        "search/",
        ChatViewSet.as_view({"get": "search_rooms"}),
        name="room-search",
    ),
]

message_patterns = [
    path(
        "rooms/<uuid:room_id>/<uuid:chat_id>/",
        ChatViewSet.as_view(
            {"patch": "update_message", "delete": "delete_message"}
        ),
        name="message-operations",
    ),
    path(
        "rooms/<uuid:room_id>/messages/",
        ChatViewSet.as_view({"get": "get_messages", "post": "send_message"}),
        name="room-messages",
    ),
]

participant_patterns = [
    path(
        "<uuid:participant_id>/",
        ChatViewSet.as_view({"get": "get_participant"}),
        name="get-participant",
    ),
    path(
        "<uuid:participant_id>/token/",
        ChatViewSet.as_view({"post": "generate_participant_token"}),
        name="generate-participant-token",
    ),
    path(
        "unread/<uuid:participant_id>/",
        ChatViewSet.as_view({"get": "get_unread_messages"}),
        name="room-unread-messages",
    ),
]

attachment_patterns = [
    path(
        "",
        ChatViewSet.as_view({"post": "create_attachment"}),
        name="create-attachment",
    ),
    path(
        "<uuid:attachment_id>/",
        ChatViewSet.as_view(
            {"get": "get_attachment", "delete": "delete_attachment"}
        ),
        name="get-delete-attachment",
    ),
]

project_room_patterns = [
    path(
        "rooms/",
        ChatViewSet.as_view(
            {"post": "create_project_room", "get": "project_rooms"}
        ),
        name="project-rooms",
    ),
]
urlpatterns = [
    path("rooms/", include(search_patterns)),
    # Room operations
    path("rooms/<uuid:room_id>/", include(room_patterns)),
    # Chat messages
    path("message/", include(message_patterns)),
    # Participant operations
    path("participant/", include(participant_patterns)),
    # Attachments
    path("attachment/", include(attachment_patterns)),
    # Project room operations
    path("", include(project_room_patterns)),
]
