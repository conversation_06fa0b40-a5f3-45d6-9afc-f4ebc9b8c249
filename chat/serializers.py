import logging

from accounts.serializers import UserSummarySerializer
from general.models import Setting
from rest_framework import serializers
from utils.utils import chat_client

from .models import ProjectRoom

logger = logging.getLogger(__name__)


class ProjectRoomSerializer(serializers.ModelSerializer):
    created_by = UserSummarySerializer()
    room_details = serializers.SerializerMethodField()

    class Meta:
        model = ProjectRoom
        fields = [
            "id",
            "room_id",
            "project",
            "name",
            "is_archived",
            "created_by",
            "created_at",
            "room_details",
            "members",
        ]
        read_only_fields = [
            "id",
            "room_id",
            "created_by",
            "created_at",
            "room_details",
        ]

    def get_room_details(self, obj: ProjectRoom):
        try:
            # Get last_n_messages from context
            last_n_messages = self.context.get("last_n_messages")
            # Get request from context
            request = self.context.get("request")

            if not request or not request.user:
                return None

            # First, get the room to find the participant ID
            initial_room = chat_client.get_room(room_id=obj.room_id)

            # Find the current user's participant ID
            current_participant = next(
                (
                    p
                    for p in initial_room.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )

            if not current_participant:
                logger.error(
                    f"User {request.user.email} is not a participant in room {obj.room_id}"
                )
                return None

            # Get user timezone
            participant_timezone = Setting.objects.get_user_timezone(
                request.user
            )

            # Get room details with participant ID and timezone

            room_details = chat_client.get_room(
                room_id=obj.room_id,
                participant_id=current_participant["id"],
                last_n_messages=last_n_messages,
                participant_timezone=participant_timezone,
                fetch_only=True,
            )

            return room_details

        except Exception as e:

            logger.error(
                f"Failed to get room details for room_id {obj.room_id}: {str(e)}"
            )
            return None

    def validate_project(self, value):
        request = self.context.get("request")
        if request and not value.created_by == request.user:
            if not value.projectinvite_set.filter(
                user=request.user, status="accepted"
            ).exists():
                raise serializers.ValidationError(
                    "You don't have access to this project"
                )
        return value


class ParticipantSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()
    name = serializers.CharField(
        max_length=100, required=False, allow_null=True
    )
    email = serializers.EmailField()
    token = serializers.CharField()
    data = serializers.DictField(
        child=serializers.JSONField(), required=False, allow_null=True
    )
    timezone = serializers.CharField(required=True)


class ParticipantCreateAsListSerializer(serializers.Serializer):
    participants = serializers.ListField(
        child=serializers.JSONField(),
        help_text="List of participants added by UUIDs",
    )


class RoomSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False)
    is_archived = serializers.BooleanField(required=False)
    group_avatar = serializers.UUIDField(
        required=False, allow_null=True, write_only=True
    )


class AttachmentSerializer(serializers.Serializer):
    url = serializers.URLField(required=False, allow_null=True)
    filename = serializers.CharField(max_length=255)
    s3_key = serializers.CharField(max_length=255)
    mime_type = serializers.CharField(max_length=255)
    file_size = serializers.IntegerField()
    created_by = serializers.UUIDField()
    upload_finished_at = serializers.DateTimeField(
        required=False, allow_null=True
    )
    presigned_data = serializers.DictField(required=False, allow_null=True)
    download_url = serializers.URLField(required=False, allow_null=True)


class CreateAttachmentSerializer(serializers.Serializer):
    filename = serializers.CharField(max_length=255, required=False)
    mime_type = serializers.CharField(max_length=255, required=False)
    participant_id = serializers.UUIDField()


class ChatSerializers(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    created_at = serializers.DateTimeField(required=False)
    updated_at = serializers.DateTimeField(required=False)
    content = serializers.CharField(required=False)
    room_id = serializers.UUIDField(required=False)
    participant_id = serializers.UUIDField(required=False)
    attachments = serializers.ListField(
        child=serializers.UUIDField(), required=False
    )
    created_by = ParticipantSerializer(required=False)
