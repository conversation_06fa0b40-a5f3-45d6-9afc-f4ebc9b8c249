import logging
from collections import defaultdict
from datetime import datetime
from typing import Dict
from typing import Optional

from accounts.models import User
from celery import shared_task
from core.constants import Features
from core.core_services.sendgrid_service import CoreService
from core.core_services.sendgrid_service import SendGridService
from django.conf import settings
from firebase_admin import messaging
from notifications.models import NotificationPreference
from utils.firebase.messaging import FirebaseMessageService
from utils.utils import chat_client
from utils.utils import create_or_update_notification

from .models import ProjectRoom

messaging_service = FirebaseMessageService()
core_service = CoreService(sendgrid_service=SendGridService())

logger = logging.getLogger(__name__)


def send_push_notification(user, title, msg, category, item_id, project_id):
    if user is None:
        return

    try:
        # Check notification preferences
        notification_preference = (
            NotificationPreference.objects.get_user_notification_preference(
                user
            )
        )
        if not getattr(notification_preference, category.lower(), True):
            return

        # Create or update notification
        create_or_update_notification(user, category, item_id, msg)

        topic = str(user.id)
        data_payload = {
            "room_id": str(item_id),
            "category": category,
        }

        # Only add project_id to payload if it exists
        if project_id is not None:
            data_payload["project_id"] = str(project_id)

        message = messaging.Message(
            topic=topic,
            notification=messaging.Notification(
                title=title, body=msg[:100] + "..." if len(msg) > 100 else msg
            ),
            data=data_payload,
        )

        messaging_service.send_message_to_topic(message)
        logger.info(f"Successfully sent push notification to user {user.id}")

    except Exception as e:
        logger.error(
            f"Failed to send push notification to user {user.id}: {str(e)}"
        )


def send_email_notification(
    recipient_email, sender_name, message_content, room_name
):
    """Send email notification to users."""
    try:
        recipient = User.objects.filter(email=recipient_email).first()
        context = {
            "sender_name": sender_name,
            "message_content": message_content,
            "room_name": room_name,
            "recipient_first_name": recipient.first_name if recipient else "",
            "redirect_url": settings.FRONTEND_URL,
            "current_year": datetime.now().year,
            "play_store_app_url": settings.PLAY_STORE_APP_URL,
            "app_store_app_url": settings.APP_STORE_APP_URL,
        }

        core_service.send_email(
            subject=f"New message from {sender_name} on tuulbox",
            template_path="chat.html",
            template_context=context,
            to_emails=[recipient_email],
        )

        logger.info(
            f"Successfully sent email notification to {recipient_email}"
        )

    except Exception as e:
        logger.error(f"Failed to send email to {recipient_email}: {str(e)}")


@shared_task
def process_chat_notifications(
    room_id: str, sender_id: str, message_content: str
) -> None:
    """Process chat notifications for a message."""
    try:
        # Get necessary data
        sender = User.objects.get(id=sender_id)
        project_room: ProjectRoom = ProjectRoom.objects.get(room_id=room_id)
        room_data = chat_client.get_room(room_id=room_id)

        # Get participant emails excluding sender
        participant_emails = [
            p["email"]
            for p in room_data.get("participants", [])
            if p["email"] != sender.email
        ]

        for email in participant_emails:
            try:
                # Try to get the recipient user
                recipient = User.objects.get(email=email)

                # Check if email notification is needed
                should_send_email = project_room.project and not (
                    recipient == project_room.project.created_by
                    or project_room.project.user_has_access(recipient)
                )

                # Send push notification
                send_push_notification(
                    user=recipient,
                    title=sender.get_full_name(),
                    msg=message_content,
                    category=Features.CHAT_MESSAGES,
                    item_id=room_id,
                    project_id=project_room.project.id
                    if project_room.project
                    else None,
                )

                # Send email if needed
                if should_send_email:
                    send_email_notification(
                        recipient_email=email,
                        sender_name=sender.get_full_name(),
                        message_content=message_content,
                        room_name=project_room.name,
                    )

                create_or_update_notification(
                    user=recipient,
                    category=Features.CHAT_MESSAGES,
                    item_id=room_id,
                    message=message_content,
                )

            except User.DoesNotExist:
                # If recipient is not a user, send email notification
                send_email_notification(
                    recipient_email=email,
                    sender_name=sender.get_full_name(),
                    message_content=message_content,
                    room_name=project_room.name,
                )

    except User.DoesNotExist:
        logger.error(
            f"Sender {sender_id} not found while processing chat notifications"
        )
    except ProjectRoom.DoesNotExist:
        logger.error(
            f"Project room {room_id} not found while processing chat notifications"
        )
    except Exception as e:
        logger.error(f"Error processing chat notifications: {str(e)}")


class UnreadMessagesService:
    def __init__(self, chat_client):
        self.chat_client = chat_client

    def get_room_summary(
        self, room_data: dict, participant_id: str
    ) -> Optional[dict]:
        """Get summary of unread messages for a room and participant."""
        try:
            unread_data = self.chat_client.get_unread_messages(
                participant_id=participant_id
            )
            unread_count = len(unread_data.get("items", []))

            if unread_count > 0:
                last_message = (
                    room_data.get("lastChat", [{}])[0].get("content", "")
                    if room_data.get("lastChat")
                    else ""
                )
                return {
                    "name": room_data.get("name", ""),
                    "unread_count": unread_count,
                    "last_message": last_message,
                }
        except Exception as e:
            logger.error(
                f"Error getting room summary for participant {participant_id}: {str(e)}"
            )
        return None

    def get_unread_messages(self) -> Dict[str, Dict]:
        """Get unread messages for all users across all rooms."""
        user_unread = defaultdict(lambda: {"total_unread": 0, "rooms": []})

        for room in ProjectRoom.objects.all():
            try:
                room_data = self.chat_client.get_room(room_id=room.room_id)

                for participant in room_data.get("participants", []):
                    email = participant.get("email")
                    participant_id = participant.get("id")

                    if not email or not participant_id:
                        continue

                    room_summary = self.get_room_summary(
                        room_data, participant_id
                    )
                    if room_summary:
                        user_unread[email]["total_unread"] += room_summary[
                            "unread_count"
                        ]
                        user_unread[email]["rooms"].append(room_summary)

            except Exception as e:
                logger.error(f"Error processing room {room.room_id}: {str(e)}")
                continue

        return user_unread


@shared_task
def send_daily_unread_messages_notification():
    """Send daily email notifications to users about their unread messages."""
    try:
        unread_service = UnreadMessagesService(chat_client)

        # Get unread messages for all users
        user_unread = unread_service.get_unread_messages()

        # Send notifications
        for email, data in user_unread.items():
            if data["total_unread"] == 0:
                continue

            try:
                # Get the user and their notification preferences
                user: User = User.objects.get(email=email)
                notification_preference = NotificationPreference.objects.get_user_notification_preference(
                    user
                )

                # Skip if email_chat notifications are disabled
                if not getattr(
                    notification_preference, Features.EMAIL_CHAT, True
                ):
                    logger.info(
                        f"Skipping unread messages summary for {email} - email_chat notifications disabled"
                    )
                    continue

                # Generate room summaries
                room_summaries = [
                    f"{room['name']} - {room['unread_count']} unread messages\n"
                    f"Last message: {room['last_message'][:100]}..."
                    for room in data["rooms"]
                ]

                # Send email notification
                total_unread = data["total_unread"]
                core_service.send_email(
                    subject=f"You've got {total_unread} unread messages on tuulbox",
                    template_path="unread_messages_summary.html",
                    template_context={
                        "recipient_first_name": user.first_name,
                        "total_unread": total_unread,
                        "room_summaries": room_summaries,
                        "redirect_url": settings.FRONTEND_URL,
                        "current_year": datetime.now().year,
                        "play_store_app_url": settings.PLAY_STORE_APP_URL,
                        "app_store_app_url": settings.APP_STORE_APP_URL,
                    },
                    to_emails=[email],
                )
                logger.info(f"Sent unread messages summary to {email}")

            except User.DoesNotExist:
                logger.error(f"User not found for email {email}")
            except Exception as e:
                logger.error(f"Failed to send email to {email}: {str(e)}")

    except Exception as e:
        logger.error(f"Error in daily unread messages task: {str(e)}")
