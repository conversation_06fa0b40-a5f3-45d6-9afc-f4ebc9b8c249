from chat.serializers import ChatSerializers
from chat.serializers import ParticipantCreateAsListSerializer
from chat.serializers import ProjectRoomSerializer
from chat.serializers import RoomSerializer
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema


PROJECT_ID_PARAM = openapi.Parameter(
    "project_id",
    openapi.IN_QUERY,
    description="Optional: Project ID for the chat room",
    type=openapi.TYPE_STRING,
    required=False,
)


CREATE_PROJECT_ROOM_SWAGGER_DOCS = swagger_auto_schema(
    method="post",
    manual_parameters=[
        PROJECT_ID_PARAM,
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            "name": openapi.Schema(type=openapi.TYPE_STRING),
            "group_avatar": openapi.Schema(
                type=openapi.TYPE_STRING, nullable=True, format="uuid"
            ),
            "contact_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                ),
            ),
            "collaborator_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                ),
            ),
            "manual_participants": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "name": openapi.Schema(
                            type=openapi.TYPE_STRING, nullable=True
                        ),
                        "email": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_EMAIL,
                        ),
                        "data": openapi.Schema(type=openapi.TYPE_OBJECT),
                    },
                    required=["email"],
                ),
            ),
        },
    ),
    responses={
        200: ProjectRoomSerializer,
        201: ProjectRoomSerializer,
        400: "Invalid data provided",
        404: "Project not found",
        500: "Internal server error",
    },
)


LIST_PROJECT_ROOMS_SWAGGER_DOCS = swagger_auto_schema(
    manual_parameters=[
        PROJECT_ID_PARAM,
        openapi.Parameter(
            "last_n_messages",
            openapi.IN_QUERY,
            description="Optional: Number of latest messages to retrieve",
            type=openapi.TYPE_INTEGER,
            required=False,
        ),
        openapi.Parameter(
            "project_name",
            openapi.IN_QUERY,
            description="Filter by project name",
            type=openapi.TYPE_STRING,
            required=False,
        ),
        openapi.Parameter(
            "member_email",
            openapi.IN_QUERY,
            description="Filter by member email",
            type=openapi.TYPE_STRING,
            required=False,
        ),
        openapi.Parameter(
            "member_name",
            openapi.IN_QUERY,
            description="Filter by member name",
            type=openapi.TYPE_STRING,
            required=False,
        ),
    ],
    responses={
        200: ProjectRoomSerializer(many=True),
        400: "Invalid filter parameters",
        404: "Project not found",
        500: "Internal server error",
    },
)


GET_ROOM_SWAGGER_DOCS = swagger_auto_schema(
    method="get",
    manual_parameters=[
        openapi.Parameter(
            "participant_id",
            openapi.IN_QUERY,
            description="Optional participant ID",
            type=openapi.TYPE_STRING,
            required=False,
        ),
        openapi.Parameter(
            "last_n_messages",
            openapi.IN_QUERY,
            description="Optional: Number of latest messages to retrieve",
            type=openapi.TYPE_INTEGER,
            required=False,
        ),
    ],
    responses={
        200: RoomSerializer,
        403: "Forbidden - User is not a participant",
        500: "Internal server error",
    },
)


SEARCH_ROOMS_SWAGGER_DOCS = swagger_auto_schema(
    method="get",
    manual_parameters=[
        openapi.Parameter(
            "name",
            openapi.IN_QUERY,
            description="Optional: Search rooms by name",
            type=openapi.TYPE_STRING,
            required=False,
        ),
        openapi.Parameter(
            "participant_email",
            openapi.IN_QUERY,
            description="Optional: Search rooms by participant email",
            type=openapi.TYPE_STRING,
            format=openapi.FORMAT_EMAIL,
            required=False,
        ),
    ],
    responses={
        200: RoomSerializer(many=True),
        400: "Invalid parameters",
        500: "Internal server error",
    },
)


ADD_PARTICIPANTS_SWAGGER_DOCS = swagger_auto_schema(
    method="post",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            "contact_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                ),
            ),
            "collaborator_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                ),
            ),
            "manual_participants": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "name": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            nullable=True,
                        ),
                        "email": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_EMAIL,
                        ),
                        "data": openapi.Schema(type=openapi.TYPE_OBJECT),
                    },
                    required=["email"],
                ),
            ),
        },
    ),
    responses={
        200: ParticipantCreateAsListSerializer,
        400: "No valid participants provided",
        404: "Room not found",
        500: "Internal server error",
    },
)


SEND_MESSAGE_SWAGGER_DOCS = swagger_auto_schema(
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            "content": openapi.Schema(
                type=openapi.TYPE_STRING, description="Message content"
            ),
            "attachments": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Attachment ID",
                    format="uuid",
                ),
            ),
        },
        anyOf=[{"required": ["content"]}, {"required": ["attachments"]}],
    ),
    responses={
        200: ChatSerializers,
        400: "Either message content or attachments are required",
        403: "User is not a participant in this chat room",
        500: "Internal server error",
    },
)


SEARCH_CHAT_SWAGGER_DOCS = swagger_auto_schema(
    method="get",
    manual_parameters=[
        openapi.Parameter(
            "content",
            openapi.IN_QUERY,
            description="Search for chat content",
            type=openapi.TYPE_STRING,
            required=False,
        ),
        openapi.Parameter(
            "participant_email",
            openapi.IN_QUERY,
            description="Filter by participant email",
            type=openapi.TYPE_STRING,
            format=openapi.FORMAT_EMAIL,
            required=False,
        ),
    ],
    responses={
        200: ChatSerializers(many=True),
        403: "User is not a participant in this chat room",
        404: "Room not found",
        500: "Internal server error",
    },
)
