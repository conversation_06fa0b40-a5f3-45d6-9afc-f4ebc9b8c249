# Generated by Django 3.2.17 on 2024-11-25 14:29
import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("project", "0021_remove_project_chat_room_id"),
        ("chat", "0003_globalroom"),
    ]

    operations = [
        migrations.AlterField(
            model_name="projectroom",
            name="project",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chat_rooms",
                to="project.project",
            ),
        ),
        migrations.DeleteModel(
            name="GlobalRoom",
        ),
    ]
