<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New Message Notification - Tuulbox</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.5;
        color: #333;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        padding: 20px;
      }
      .logo-container {
        text-align: center;
        margin: 15px 0 30px;
        border-bottom: 1px solid #e5e5e5;
        padding-bottom: 10px;
      }
      .logo {
        max-width: 120px;
      }
      .content {
        text-align: left;
        padding: 0;
      }
      h1 {
        font-size: 16px;
        margin-bottom: 20px;
        color: #000;
        font-weight: bold;
      }
      .greeting {
        font-size: 14px;
        margin-bottom: 15px;
        color: #333;
      }
      .message-text {
        font-size: 14px;
        margin-bottom: 20px;
        color: #333;
        line-height: 1.6;
      }
      .message-content {
        border-left: 4px solid #3e6cb1;
        padding-left: 15px;
        margin: 15px 0;
        color: #555;
        font-style: italic;
      }
      .continue-text {
        font-size: 14px;
        margin: 20px 0;
        color: #333;
      }
      .button-container {
        margin: 20px 0;
      }
      .reply-button {
        display: inline-block;
        background-color: #102340;
        color: white !important;
        text-decoration: none !important;
        padding: 8px 20px;
        border-radius: 10px;
        font-weight: normal;
        font-size: 14px;
      }
      .description {
        font-size: 14px;
        color: #333;
        text-align: left;
        margin: 25px 0;
        line-height: 1.6;
      }
      .signature {
        text-align: left;
        margin: 20px 0;
        font-size: 14px;
        color: #333;
      }
      .footer {
        background-color: #f5f5f5;
        padding: 15px;
        margin-top: 20px;
        text-align: center;
      }
      .app-buttons {
        text-align: center;
        margin: 15px 0;
      }
      .app-button {
        display: inline-block;
        margin: 0 5px;
        max-width: 140px;
      }
      .app-img {
        max-width: 100%;
        height: auto;
      }
      .support {
        text-align: center;
        margin: 10px 0;
      }
      .support a {
        color: #2d63b4;
        text-decoration: none;
      }
      .copyright {
        text-align: center;
        font-size: 12px;
        color: #666;
        margin-top: 5px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo-container">
        <img
          src="https://ci3.googleusercontent.com/meips/ADKq_NY3Je-Nv2Ux_U7O6IVKQYBo4oKud0e8ToYqD6YN_FplmmsNtALRMeVHBDU7lVaqWQOXL-CjB9qYU-3OX05SgDE1p-tkz23GkmsiTp3bj_ScxjU7315HnO4kD1uL_Q2Fgm2Q=s0-d-e1-ft#https://tuulbox-core.s3.amazonaws.com/static/core/Tuulbox-logotype%20(1).png"
          alt="tuulbox logo"
          class="logo"
        />
      </div>

      <div class="content">
        <h1>New message from {{sender_name}} on tuulbox</h1>

        <p class="greeting">Hi {{recipient_first_name}},</p>

        <p class="message-text">
          {{sender_name}} just sent you a message on tuulbox
        </p>

        <div class="message-content">{{message_content}}</div>

        <p class="continue-text">
          Click the link to continue the conversation on tuulbox.
        </p>

        <div class="button-container">
          <a href="{{redirect_url}}" class="reply-button">Reply</a>
        </div>

        <p class="continue-text">
          If the button doesn’t respond, please copy and paste this link into
          your browser <br />
          <a href="{{redirect_url}}">{{redirect_url}}</a>
        </p>

        <div class="description">
          Manage Construction Projects, Tasks, Contacts & Files on the go.
          Designed by a general contractor for fellow construction company
          owners, tuulbox has what you need without the needless features of
          other construction software. Easy to use and exactly what you need.
        </div>

        <div class="description">
          If you have any questions, feel free to contact us.
        </div>

        <div class="signature">
          Best regards,<br />
          The tuulbox Team.
        </div>
      </div>

      <div class="footer">
        <div class="app-buttons">
          <a href="{{app_store_app_url}}" class="app-button">
            <img
              src="https://ci3.googleusercontent.com/meips/ADKq_NZgZnu704BuPci2KO50auBgxGgoZQJItnlTSM2-32YjF4xCvS142tVxkmCGTfwXLwc51IZLMvZnQPLL1uNHNkbiusHcwj2ulicSwy7oH2zjTLSNt-wwRaP5Nr_OGrtvacvBPxlFJw1vdAI6dL5YWEyx=s0-d-e1-ft#https://res.cloudinary.com/heyset/image/upload/v1703634102/apple-app-store-badge_j9oeve.png"
              alt="Download on the App Store"
              class="app-img"
            />
          </a>
          <a href="{{play_store_app_url}}" class="app-button">
            <img
              src="https://ci3.googleusercontent.com/meips/ADKq_NZdJsrTTnbo-woCHgaYl6f795Yl6ajh5j_x86mIw_xCxWmZCXBqDS87_WAeGg8M0s4XY2YLXy-oVuC3A-dNMxXxd_BYYxU9Ybd5ad9bwCne__75Sdefl6lyijxGjTBg4fC-_1H40JE=s0-d-e1-ft#https://res.cloudinary.com/heyset/image/upload/v1703603001/google-play_g9cf9r.png"
              alt="Get it on Google Play"
              class="app-img"
            />
          </a>
        </div>

        <div class="support">
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>

        <div class="copyright">
          © {{current_year}} tuulbox. All rights reserved
        </div>
      </div>
    </div>
  </body>
</html>
