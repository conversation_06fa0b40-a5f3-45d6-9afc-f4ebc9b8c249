import logging
from typing import Dict
from typing import Optional

from core.services.simple_cache_service import BaseCacheService

logger = logging.getLogger(__name__)


class ContactService(BaseCacheService):
    """Contact-specific caching service."""

    def get_cache_timeout_setting(self) -> str:
        """Return the settings attribute name for cache timeout."""
        return "CONTACT_CACHE_TIMEOUT"

    def get_cache_prefix(self) -> str:
        """Return the cache key prefix for this service."""
        return "contact"

    def get_additional_cache_params(
        self, query_params: Optional[Dict] = None
    ) -> Dict:
        """Add contact-specific parameters to cache key."""
        additional_params = {
            # add more as needed
        }

        return additional_params


class SubcontractorService(BaseCacheService):
    """Subcontractor-specific caching service."""

    def get_cache_timeout_setting(self) -> str:
        """Return the settings attribute name for cache timeout."""
        return "SUBCONTRACTOR_CACHE_TIMEOUT"

    def get_cache_prefix(self) -> str:
        """Return the cache key prefix for this service."""
        return "subcontractor"

    def get_additional_cache_params(
        self, query_params: Optional[Dict] = None
    ) -> Dict:
        """Add subcontractor-specific parameters to cache key."""
        additional_params = {
            # add more as needed
        }

        return additional_params
