# Generated by Django 3.2.17 on 2023-07-14 11:11
import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Contact",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("full_name", models.CharField(max_length=255)),
                (
                    "email",
                    models.EmailField(
                        blank=True, default="", max_length=254, null=True
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True, default="", max_length=255, null=True
                    ),
                ),
                (
                    "phone_number",
                    models.Char<PERSON>ield(
                        blank=True, default="", max_length=255, null=True
                    ),
                ),
                (
                    "company",
                    models.CharField(
                        blank=True, default="", max_length=255, null=True
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contacts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
