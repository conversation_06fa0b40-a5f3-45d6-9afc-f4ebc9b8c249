import factory

from .models import Contact
from .models import Subcontractor
from .models import SubcontractorCertificate
from .models import SubContractorEstimate


class ContactFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Contact

    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    email = factory.Faker("email")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("secondary_address")
    city = factory.Faker("city")
    state = factory.Faker("state_abbr")
    zip_code = factory.Faker("zipcode")
    country = factory.Faker("country")
    phone_number = factory.Faker("phone_number")
    extension = factory.Faker("random_int")
    company = factory.Faker("company")
    uploaded_by = factory.SubFactory("accounts.factories.UserFactory")


class SubcontractorFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Subcontractor

    contact = factory.SubFactory(ContactFactory)
    has_tax_documents = factory.Faker("boolean")


class SubcontractorCertificateFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SubcontractorCertificate

    contractor = factory.SubFactory(SubcontractorFactory)
    certificate = factory.SubFactory("storage.factories.FileFactory")
    file_category = factory.Faker(
        "random_element", elements=SubcontractorCertificate.FileCategory.ALL
    )


class SubContractorEstimateFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SubContractorEstimate

    project = factory.SubFactory("projects.factories.ProjectFactory")
    subcontractor = factory.SubFactory(SubcontractorFactory)
