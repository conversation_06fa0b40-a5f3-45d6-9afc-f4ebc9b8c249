from accounts.factories import UserFactory
from company.factories import CompanyFactory
from company.factories import InsuranceFactory
from company.factories import LicensesFactory
from company.models import Insurance
from company.models import Licenses
from company.models import Subcontractor
from contacts.factories import ContactFactory
from contacts.factories import SubcontractorCertificateFactory
from contacts.factories import SubContractorEstimateFactory
from contacts.factories import SubcontractorFactory
from contacts.models import Contact
from contacts.models import SubcontractorCertificate
from django.urls import reverse
from project.factories import ProjectFactory
from rest_framework import status
from storage.factories import FileFactory
from testing.base import BaseAPITest


class ContactTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)

        super().setUp()

    def test_unauthenticated_user(self):
        # Given that I have an unauthenticated user
        # When I try to access the contact endpoint
        url = reverse("contacts:contact")
        self.client.force_authenticate(user=None)

        # Then I should get a 401 response
        response = self.client.get(url)
        self.assertEqual(response.status_code, 401)

        response = self.client.post(url, {})
        self.assertEqual(response.status_code, 401)

        url = reverse(
            "contacts:contact",
            kwargs={"pk": "4cbb14eb-faec-4139-83cd-7ec95a50c9d5"},
        )
        response = self.client.put(url, {})
        self.assertEqual(response.status_code, 401)

        response = self.client.patch(url)
        self.assertEqual(response.status_code, 401)

        response = self.client.delete(url)
        self.assertEqual(response.status_code, 401)

    def test_create_contact(self):
        # Given that I have an authenticated user
        self.client.force_authenticate(user=self.user)
        # When I create a Contact
        url = reverse("contacts:contact")
        data = {
            "first_name": "John",
            "last_name": "",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St",
            "address_line_2": "Apt 101",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
            "phone_number": "2024567890",
            "extension": "12345",
            "company": "ABC Inc",
        }
        response = self.client.post(url, data, format="json")
        # Then I should get a 201 response
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["first_name"], data["first_name"])
        self.assertEqual(response.data["last_name"], data["last_name"])
        self.assertEqual(response.data["email"], data["email"])
        self.assertEqual(
            response.data["address_line_1"], data["address_line_1"]
        )
        self.assertEqual(
            response.data["address_line_2"], data["address_line_2"]
        )
        self.assertEqual(response.data["city"], data["city"])
        self.assertEqual(response.data["state"], data["state"])
        self.assertEqual(response.data["zip_code"], data["zip_code"])
        self.assertEqual(response.data["country"], data["country"])
        self.assertEqual(response.data["phone_number"], "2024567890")
        self.assertEqual(response.data["extension"], data["extension"])
        self.assertEqual(response.data["company"], data["company"])
        self.assertIsNotNone(response.data["uploaded_by"])

    def test_create_contact_with_profile_picture(self):
        # Given that I have an authenticated user
        self.client.force_authenticate(user=self.user)
        # When I create a Contact
        url = reverse("contacts:contact")
        data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St",
            "address_line_2": "Apt 101",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
            "phone_number": "2024567890",
            "extension": "12345",
            "company": "ABC Inc",
            "profile_picture": self.generate_base64_photo_file(),
        }
        response = self.client.post(url, data, format="json")
        # Then I should get a 201 response
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["first_name"], data["first_name"])
        self.assertEqual(response.data["last_name"], data["last_name"])
        self.assertEqual(response.data["email"], data["email"])
        self.assertEqual(
            response.data["address_line_1"], data["address_line_1"]
        )
        self.assertEqual(
            response.data["address_line_2"], data["address_line_2"]
        )
        self.assertEqual(response.data["city"], data["city"])
        self.assertEqual(response.data["state"], data["state"])
        self.assertEqual(response.data["zip_code"], data["zip_code"])
        self.assertEqual(response.data["country"], data["country"])
        self.assertEqual(response.data["phone_number"], "2024567890")
        self.assertEqual(response.data["extension"], data["extension"])
        self.assertEqual(response.data["company"], data["company"])
        self.assertIsNotNone(response.data["uploaded_by"])
        # self.assertIsNotNone(response.data["profile_picture"])

    def test_list_contacts(self):
        # Given that I have an authenticated user with 1 contact
        self.client.force_authenticate(user=self.user)

        user_contact = ContactFactory(uploaded_by=self.user)
        ContactFactory(uploaded_by=UserFactory())

        # When I list contacts
        url = reverse("contacts:contact")
        response = self.client.get(url)

        count_of_user_contacts = Contact.objects.get_contacts_uploaded_by_user(
            self.user
        ).count()

        # Then I should get a 200 response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), count_of_user_contacts)
        self.assertEqual(
            response.data["results"][0]["id"], str(user_contact.id)
        )

    def test_retrieve_contacts(self):
        # Given that I have an authenticated user with 1 contact
        self.client.force_authenticate(user=self.user)

        user_contact = ContactFactory(uploaded_by=self.user)
        # When I retrieve a contact
        url = reverse("contacts:contact", kwargs={"pk": user_contact.id})
        response = self.client.get(url)

        # Then I should get a 200 response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["id"], str(user_contact.id))
        self.assertEqual(response.data["first_name"], user_contact.first_name)
        self.assertEqual(response.data["last_name"], user_contact.last_name)
        self.assertEqual(response.data["email"], user_contact.email)
        self.assertEqual(
            response.data["address_line_1"], user_contact.address_line_1
        )
        self.assertEqual(
            response.data["address_line_2"], user_contact.address_line_2
        )
        self.assertEqual(response.data["city"], user_contact.city)
        self.assertEqual(response.data["state"], user_contact.state)
        self.assertEqual(response.data["zip_code"], user_contact.zip_code)
        self.assertEqual(response.data["country"], user_contact.country)
        self.assertEqual(response.data["company"], user_contact.company)
        self.assertEqual(
            response.data["uploaded_by"]["id"],
            str(user_contact.uploaded_by.id),
        )
        self.assertIn("is_subcontractor", response.data.keys())
        self.assertIn("subcontractor_id", response.data.keys())

    def test_update_contacts(self):
        # Given that I have an authenticated user with 1 contact
        self.client.force_authenticate(user=self.user)
        user_contact = ContactFactory(uploaded_by=self.user)

        # When I update a contact
        url = reverse("contacts:contact", kwargs={"pk": user_contact.id})
        data = {
            "first_name": "New",
            "last_name": "Name",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St",
            "address_line_2": "Apt 101",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
            "phone_number": "2024567890",
            "extension": "12345",
            "company": "ABC Inc new",
        }

        response = self.client.patch(url, data, format="json")

        # Then I should get a 200 response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["first_name"], data["first_name"])
        self.assertEqual(response.data["last_name"], data["last_name"])
        self.assertEqual(response.data["email"], data["email"])
        self.assertEqual(
            response.data["address_line_1"], data["address_line_1"]
        )
        self.assertEqual(
            response.data["address_line_2"], data["address_line_2"]
        )
        self.assertEqual(response.data["city"], data["city"])
        self.assertEqual(response.data["state"], data["state"])
        self.assertEqual(response.data["zip_code"], data["zip_code"])
        self.assertEqual(response.data["country"], data["country"])
        self.assertEqual(response.data["phone_number"], "2024567890")
        self.assertEqual(response.data["extension"], data["extension"])
        self.assertEqual(response.data["company"], data["company"])
        self.assertEqual(
            response.data["uploaded_by"]["id"],
            str(user_contact.uploaded_by.id),
        )

        # When I try update a contact that does not belong to me
        other_user_contact = ContactFactory(uploaded_by=UserFactory())
        url = reverse("contacts:contact", kwargs={"pk": other_user_contact.id})
        data = {
            "first_name": "New",
            "last_name": "Name",
            "email": "<EMAIL>",
            "address_line_1": "123 Main St new",
            "address_line_2": "Apt 101 new",
            "city": "Anytown",
            "state": "California",
            "zip_code": "12345",
            "country": "USA",
            "phone_number": "**********",
            "extension": "12345",
            "company": "ABC Inc new",
        }

        response = self.client.patch(url, data)

        # Then I should get a 403 response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_contacts(self):
        # Given that I have an authenticated user with 1 contact
        self.client.force_authenticate(user=self.user)

        user_project = ContactFactory(uploaded_by=self.user)

        # When I delete a Contact
        url = reverse("contacts:contact", kwargs={"pk": user_project.id})
        response = self.client.delete(url)

        # Then I should get a 204 response
        self.assertEqual(response.status_code, 204)
        self.assertEqual(
            Contact.objects.get_contacts_uploaded_by_user(self.user).count(), 0
        )

        # When I try delete a Contact that does not belong to me
        other_user_contact = ContactFactory(uploaded_by=UserFactory())
        url = reverse("project:project", kwargs={"pk": other_user_contact.id})
        response = self.client.delete(url)

        # Then I should get a 403 response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class SubcontractorTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.url_name = "contacts:subcontractor"
        super().setUp()

    def test_unauthenticated_user(self):
        # Given that I have an unauthenticated user
        self.client.force_authenticate(user=None)

        # When I try to access the subcontractor endpoint
        url = reverse(self.url_name)

        # Then I should get a 401 response
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.post(url, {})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        url = reverse(
            self.url_name,
            kwargs={"pk": "4cbb14eb-faec-4139-83cd-7ec95a50c9d5"},
        )

        response = self.client.put(url, {})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.patch(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_subcontractor_with_new_contact(self):
        # Given that I have an authenticated user
        self.client.force_authenticate(user=self.user)

        # When I create a Subcontractor with a new contact
        url = reverse(self.url_name)
        file = FileFactory.create()
        licenses_payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "License 1 description",
            "license_number": "1234",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22T11:50:09.831000Z",
            "attachment": str(file.id),
            "attachment_filename": "new file",
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(FileFactory().id), str(FileFactory().id)],
                    "filenames": {str(FileFactory().id): "custom_name.pdf"},
                }
            ],
        }

        insurance_payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "Insurance 1 description",
            "broker": "new insurance broker",
            "agent": "1234",
            "contact": "some contact",
            "email": "<EMAIL>",
            "reminder": "2021-05-22T11:50:09.831000Z",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "policy_number": "P1234",
            "policy": str(file.id),
            "policy_filename": "new file",
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(FileFactory().id), str(FileFactory().id)],
                }
            ],
        }

        data = {
            "has_tax_documents": True,
            "tax_id": "123-45-6789",
            "contact": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "address_line_1": "123 Main St",
                "address_line_2": "Apt 101",
                "city": "Anytown",
                "state": "California",
                "zip_code": "12345",
                "country": "USA",
                "phone_number": "**********",
                "extension": "12345",
                "company": "ABC Inc",
            },
            "licenses": [licenses_payload],
            "insurances": [insurance_payload],
            "certificates_ids": [str(FileFactory().id)],
        }

        response = self.client.post(url, data, format="json")

        # Then I should get a 201 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify contact was created correctly
        contact_id = response.data["contact"]["id"]
        contact = Contact.objects.get(id=contact_id)
        self.assertEqual(contact.first_name, data["contact"]["first_name"])
        self.assertEqual(contact.last_name, data["contact"]["last_name"])
        self.assertEqual(contact.email, data["contact"]["email"])
        self.assertEqual(contact.uploaded_by, self.user)

        # Verify licenses were created correctly
        self.assertEqual(len(response.data["licenses"]), 1)
        license_data = response.data["licenses"][0]
        self.assertEqual(license_data["name"], licenses_payload["name"])
        self.assertEqual(
            license_data["license_number"], licenses_payload["license_number"]
        )

        # Check file attachments for license
        self.assertIsNotNone(license_data["file"])
        self.assertIsNotNone(license_data["all_attachments"])

        # Verify insurances were created correctly
        self.assertEqual(len(response.data["insurances"]), 1)
        insurance_data = response.data["insurances"][0]
        self.assertEqual(
            insurance_data["carrier"], insurance_payload["carrier"]
        )
        self.assertEqual(
            insurance_data["policy_number"], insurance_payload["policy_number"]
        )

        # Check file attachments for insurance
        self.assertIsNotNone(insurance_data["file"])
        self.assertIsNotNone(insurance_data["all_attachments"])

        # Verify certificates were created
        self.assertEqual(
            len(response.data["certificates"]), len(data["certificates_ids"])
        )

    def test_create_subcontractor_with_existing_contact(self):
        # Given that I have an authenticated user and an existing contact
        self.client.force_authenticate(user=self.user)
        existing_contact = ContactFactory(uploaded_by=self.user)

        # When I create a Subcontractor with that contact
        url = reverse(self.url_name)
        data = {
            "has_tax_documents": True,
            "tax_id": "123-45-6789",
            "contact_id": str(existing_contact.id),
            "certificates_ids": [str(FileFactory().id)],
        }

        response = self.client.post(url, data, format="json")

        # Then I should get a 201 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify no new contact was created
        self.assertEqual(
            Contact.objects.get_contacts_uploaded_by_user(self.user).count(), 1
        )

        # Verify the existing contact was used
        self.assertEqual(
            response.data["contact"]["id"], str(existing_contact.id)
        )

    def test_update_subcontractor_basic_info(self):
        # Given an existing subcontractor
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)

        # When I update just the basic info
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {"has_tax_documents": True, "tax_id": "updated-tax-id"}

        response = self.client.patch(url, data, format="json")

        # Then the update should succeed
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["has_tax_documents"], data["has_tax_documents"]
        )
        self.assertEqual(response.data["tax_id"], data["tax_id"])

        # And the database should be updated
        subcontractor.refresh_from_db()
        self.assertEqual(
            subcontractor.has_tax_documents, data["has_tax_documents"]
        )
        self.assertEqual(subcontractor.tax_id, data["tax_id"])

    def test_update_subcontractor_contact_info(self):
        # Given an existing subcontractor
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)

        # When I update the contact info
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "contact": {
                "first_name": "Updated",
                "last_name": "Name",
                "email": "<EMAIL>",
                "phone_number": "************",
            }
        }

        response = self.client.patch(url, data, format="json")

        # Then the update should succeed
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["contact"]["first_name"],
            data["contact"]["first_name"],
        )
        self.assertEqual(
            response.data["contact"]["email"], data["contact"]["email"]
        )

        # And the contact should be updated in the database
        subcontractor.contact.refresh_from_db()
        self.assertEqual(
            subcontractor.contact.first_name, data["contact"]["first_name"]
        )
        self.assertEqual(
            subcontractor.contact.last_name, data["contact"]["last_name"]
        )
        self.assertEqual(subcontractor.contact.email, data["contact"]["email"])
        self.assertEqual(
            subcontractor.contact.phone_number, data["contact"]["phone_number"]
        )

    def test_create_license_for_existing_subcontractor(self):
        # Given an existing subcontractor
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)
        file = FileFactory.create()

        # When I add a new license
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "licenses": [
                {
                    "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
                    "name": "New License",
                    "license_number": "LIC-12345",
                    "valid_from": "2023-01-01",
                    "valid_to": "2025-01-01T00:00:00Z",
                    "reminder": "2024-12-01T00:00:00Z",
                    "attachment": str(file.id),
                    "attachment_filename": "license_doc.pdf",
                }
            ]
        }

        response = self.client.patch(url, data, format="json")

        # Then the license should be created
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["licenses"]), 1)

        # And the license should have the correct data
        license_data = response.data["licenses"][0]
        self.assertEqual(license_data["name"], data["licenses"][0]["name"])
        self.assertEqual(
            license_data["license_number"],
            data["licenses"][0]["license_number"],
        )

        # And it should be properly linked to the subcontractor
        license_instance = Licenses.objects.get(id=license_data["id"])
        self.assertEqual(
            str(license_instance.owner_contact.id), str(subcontractor.id)
        )

    def test_update_existing_license(self):
        # Given a subcontractor with an existing license
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)
        license_instance = Licenses.objects.create(
            license_type=Licenses.LicenseType.BUSINESS_LICENSE,
            name="Initial License",
            license_number="1234",
            valid_from="2021-12-12",
            valid_to="2021-12-12T00:00:00Z",
            reminder="2021-05-22T11:50:09.831000Z",
            owner_contact=subcontractor,
            company_id=self.user.company_set.first().id,
        )

        # When I update the license
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "licenses": [
                {
                    "id": str(license_instance.id),
                    "license_number": "UPDATED-5678",
                    "valid_to": "2026-01-01T00:00:00Z",
                    "reminder": "2025-12-01T00:00:00Z",
                }
            ]
        }

        response = self.client.patch(url, data, format="json")

        # Then the license should be updated
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And the license should have the updated data
        license_data = response.data["licenses"][0]
        self.assertEqual(
            license_data["license_number"],
            data["licenses"][0]["license_number"],
        )

        # And the database should be updated
        license_instance.refresh_from_db()
        self.assertEqual(
            license_instance.license_number,
            data["licenses"][0]["license_number"],
        )

    def test_create_insurance_for_existing_subcontractor(self):
        # Given an existing subcontractor
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)
        file = FileFactory.create()

        # When I add a new insurance
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "insurances": [
                {
                    "insurance_type": Insurance.InsuranceType.WORKERS_COMPENSATION,
                    "carrier": "WorkComp Inc",
                    "broker": "Insurance Broker LLC",
                    "agent": "Jane Smith",
                    "contact": "555-123-4567",
                    "email": "<EMAIL>",
                    "valid_from": "2023-01-01",
                    "valid_to": "2024-01-01T00:00:00Z",
                    "reminder": "2023-12-01T00:00:00Z",
                    "policy_number": "WC-54321",
                    "policy": str(file.id),
                    "policy_filename": "workers_comp_policy.pdf",
                }
            ]
        }

        response = self.client.patch(url, data, format="json")

        # Then the insurance should be created
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["insurances"]), 1)

        # And the insurance should have the correct data
        insurance_data = response.data["insurances"][0]
        self.assertEqual(
            insurance_data["carrier"], data["insurances"][0]["carrier"]
        )
        self.assertEqual(
            insurance_data["policy_number"],
            data["insurances"][0]["policy_number"],
        )

        # And it should be properly linked to the subcontractor
        insurance_instance = Insurance.objects.get(id=insurance_data["id"])
        self.assertEqual(
            str(insurance_instance.owner_contact.id), str(subcontractor.id)
        )

    def test_update_existing_insurance(self):
        # Given a subcontractor with an existing insurance
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)
        insurance_instance = Insurance.objects.create(
            insurance_type=Insurance.InsuranceType.GENERAL_LIABILITY,
            carrier="Initial Insurance Co",
            broker="Initial Broker",
            agent="Initial Agent",
            valid_from="2021-12-12",
            valid_to="2022-12-12T00:00:00Z",
            policy_number="P1234",
            owner_contact=subcontractor,
            company_id=self.user.company_set.first().id,
        )

        # When I update the insurance
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "insurances": [
                {
                    "id": str(insurance_instance.id),
                    "carrier": "Updated Insurance Co",
                    "broker": "Updated Broker",
                    "policy_number": "UPDATED-P5678",
                    "valid_to": "2025-01-01T00:00:00Z",
                }
            ]
        }

        response = self.client.patch(url, data, format="json")

        # Then the insurance should be updated
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And the insurance should have the updated data
        insurance_data = response.data["insurances"][0]
        self.assertEqual(
            insurance_data["carrier"], data["insurances"][0]["carrier"]
        )
        self.assertEqual(
            insurance_data["policy_number"],
            data["insurances"][0]["policy_number"],
        )

        # And the database should be updated
        insurance_instance.refresh_from_db()
        self.assertEqual(
            insurance_instance.carrier, data["insurances"][0]["carrier"]
        )
        self.assertEqual(
            insurance_instance.broker, data["insurances"][0]["broker"]
        )
        self.assertEqual(
            insurance_instance.policy_number,
            data["insurances"][0]["policy_number"],
        )

    def test_update_license_attachments(self):
        # Given a subcontractor with an existing license
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)
        license_instance = Licenses.objects.create(
            license_type=Licenses.LicenseType.BUSINESS_LICENSE,
            name="Initial License",
            license_number="1234",
            valid_from="2021-12-12",
            valid_to="2021-12-12T00:00:00Z",
            owner_contact=subcontractor,
            company_id=self.user.company_set.first().id,
        )

        # And some files
        main_file = FileFactory.create()
        additional_file1 = FileFactory.create()
        additional_file2 = FileFactory.create()

        # When I update the license with attachment operations
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "licenses": [
                {
                    "id": str(license_instance.id),
                    "attachment": str(main_file.id),
                    "attachment_filename": "renamed_license.pdf",
                    "additional_attachments": [
                        {
                            "operation": "add",
                            "files": [
                                str(additional_file1.id),
                                str(additional_file2.id),
                            ],
                            "filenames": {
                                str(additional_file1.id): "attachment1.pdf",
                                str(additional_file2.id): "attachment2.pdf",
                            },
                        }
                    ],
                }
            ]
        }

        response = self.client.patch(url, data, format="json")

        # Then the update should succeed
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And the license should have the attachments
        license_instance.refresh_from_db()
        self.assertEqual(
            str(license_instance.attachment.id), str(main_file.id)
        )

        # And the additional attachments should be added
        self.assertEqual(license_instance.additional_attachments.count(), 2)
        attachment_ids = [
            str(attachment.id)
            for attachment in license_instance.additional_attachments.all()
        ]
        self.assertIn(str(additional_file1.id), attachment_ids)
        self.assertIn(str(additional_file2.id), attachment_ids)

    def test_comprehensive_subcontractor_update(self):
        # Given a subcontractor with license and insurance
        self.client.force_authenticate(user=self.user)
        subcontractor = SubcontractorFactory(contact__uploaded_by=self.user)

        license_instance = Licenses.objects.create(
            license_type=Licenses.LicenseType.BUSINESS_LICENSE,
            name="Initial License",
            license_number="1234",
            valid_from="2021-12-12",
            valid_to="2021-12-12T00:00:00Z",
            owner_contact=subcontractor,
            company_id=self.user.company_set.first().id,
        )

        insurance_instance = Insurance.objects.create(
            insurance_type=Insurance.InsuranceType.GENERAL_LIABILITY,
            carrier="Initial Insurance Co",
            valid_from="2021-12-12",
            valid_to="2022-12-12T00:00:00Z",
            policy_number="P1234",
            owner_contact=subcontractor,
            company_id=self.user.company_set.first().id,
        )

        # When I update everything at once
        url = reverse(self.url_name, kwargs={"pk": subcontractor.id})
        data = {
            "tax_id": "updated-tax-id",
            "contact": {
                "first_name": "Updated First",
                "last_name": "Updated Last",
                "email": "<EMAIL>",
            },
            "licenses": [
                {
                    "id": str(license_instance.id),
                    "license_number": "UPDATED-5678",
                    "valid_to": "2026-01-01T00:00:00Z",
                },
                {
                    # New license to create
                    "license_type": Licenses.LicenseType.OTHER,
                    "name": "New Additional License",
                    "license_number": "NEW-1234",
                    "valid_from": "2023-01-01",
                    "valid_to": "2025-01-01T00:00:00Z",
                },
            ],
            "insurances": [
                {
                    "id": str(insurance_instance.id),
                    "carrier": "Updated Insurance Co",
                    "policy_number": "UPDATED-P5678",
                }
            ],
            "certificates_ids": [str(FileFactory().id)],
        }

        response = self.client.patch(url, data, format="json")

        # Then everything should be updated correctly
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check basic info update
        self.assertEqual(response.data["tax_id"], data["tax_id"])

        # Check contact update
        self.assertEqual(
            response.data["contact"]["first_name"],
            data["contact"]["first_name"],
        )
        self.assertEqual(
            response.data["contact"]["last_name"], data["contact"]["last_name"]
        )
        self.assertEqual(
            response.data["contact"]["email"], data["contact"]["email"]
        )

        # Check licenses update
        self.assertEqual(
            len(response.data["licenses"]), 2
        )  # Updated + new one

        # Find the updated license
        updated_license = next(
            (
                lic
                for lic in response.data["licenses"]
                if lic["id"] == str(license_instance.id)
            ),
            None,
        )
        self.assertIsNotNone(updated_license)
        self.assertEqual(
            updated_license["license_number"],
            data["licenses"][0]["license_number"],
        )

        # Find the new license
        new_license = next(
            (
                lic
                for lic in response.data["licenses"]
                if lic["id"] != str(license_instance.id)
            ),
            None,
        )
        self.assertIsNotNone(new_license)
        self.assertEqual(new_license["name"], data["licenses"][1]["name"])
        self.assertEqual(
            new_license["license_number"],
            data["licenses"][1]["license_number"],
        )

        # Check insurance update
        self.assertEqual(len(response.data["insurances"]), 1)
        updated_insurance = response.data["insurances"][0]
        self.assertEqual(
            updated_insurance["carrier"], data["insurances"][0]["carrier"]
        )
        self.assertEqual(
            updated_insurance["policy_number"],
            data["insurances"][0]["policy_number"],
        )

    def test_get_subcontractor(self):
        # Given that I have an authenticated user with 1 subcontractor
        self.client.force_authenticate(user=self.user)

        user_subcontractor = SubcontractorFactory(
            contact__uploaded_by=self.user
        )
        SubcontractorCertificateFactory(
            contractor=user_subcontractor, certificate=FileFactory()
        )

        # Also create a license and insurance for this subcontractor
        Licenses.objects.create(
            license_type=Licenses.LicenseType.BUSINESS_LICENSE,
            name="Subcontractor License",
            license_number="SC-1234",
            valid_from="2021-12-12",
            valid_to="2023-12-12T00:00:00Z",
            owner_contact=user_subcontractor,
            company_id=self.user.company_set.first().id,
        )

        Insurance.objects.create(
            insurance_type=Insurance.InsuranceType.GENERAL_LIABILITY,
            carrier="Subcontractor Insurance Co",
            valid_from="2021-12-12",
            valid_to="2022-12-12T00:00:00Z",
            policy_number="SC-P1234",
            owner_contact=user_subcontractor,
            company_id=self.user.company_set.first().id,
        )

        # Create a subcontractor belonging to another user
        SubcontractorFactory(contact__uploaded_by=UserFactory())

        # When I list subcontractors
        url = reverse(self.url_name)
        response = self.client.get(url)

        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And I should get only my subcontractors
        count_of_user_subcontractors = (
            Subcontractor.objects.get_subcontractor_added_by_user(
                self.user
            ).count()
        )
        self.assertEqual(
            len(response.data["results"]), count_of_user_subcontractors
        )

        # And the subcontractor should have all related data
        subcontractor_data = response.data["results"][0]
        self.assertEqual(subcontractor_data["id"], str(user_subcontractor.id))

        # Check certificates
        self.assertEqual(
            len(subcontractor_data["certificates"]),
            user_subcontractor.certificates.count(),
        )

        # Check licenses
        self.assertEqual(len(subcontractor_data["licenses"]), 1)
        self.assertEqual(
            subcontractor_data["licenses"][0]["license_number"], "SC-1234"
        )

        # Check insurances
        self.assertEqual(len(subcontractor_data["insurances"]), 1)
        self.assertEqual(
            subcontractor_data["insurances"][0]["policy_number"], "SC-P1234"
        )

    def test_get_subcontractor_by_project(self):
        # Given that I have an authenticated user with a project and subcontractors
        self.client.force_authenticate(user=self.user)

        project = ProjectFactory(created_by=self.user)
        user_subcontractor = SubcontractorFactory(
            contact__uploaded_by=self.user
        )
        other_subcontractor = SubcontractorFactory(
            contact__uploaded_by=UserFactory()
        )

        SubContractorEstimateFactory(
            subcontractor=user_subcontractor, project=project
        )

        SubContractorEstimateFactory(
            subcontractor=other_subcontractor, project=project
        )

        # When I list subcontractors filtered by project
        url = reverse(self.url_name)
        response = self.client.get(url, {"project_id": project.id})

        # Then I should get all subcontractors for that project
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        count_of_project_subcontractors = (
            Subcontractor.objects.get_subcontractor_by_project(project).count()
        )
        self.assertEqual(
            len(response.data["results"]), count_of_project_subcontractors
        )

        # And the first subcontractor should be mine
        self.assertEqual(
            response.data["results"][0]["id"], str(user_subcontractor.id)
        )


class SubcontractorCertificateTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()

        self.url_name = "contacts:subcontractor_certificates"
        super().setUp()

    def test_get_subcontractor_certificate(self):
        # Given that I have an authenticated user with 1 subcontractor
        self.client.force_authenticate(user=self.user)
        user_subcontractor = SubcontractorFactory(
            contact__uploaded_by=self.user
        )
        user_subcontractor_certificate = SubcontractorCertificateFactory(
            contractor=user_subcontractor, certificate=FileFactory()
        )
        SubcontractorCertificateFactory(certificate=FileFactory())

        # When I list subcontractors certificates
        url = reverse(
            self.url_name, kwargs={"subcontractor_id": user_subcontractor.id}
        )
        response = self.client.get(url)

        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data["results"]),
            1,
        )

        # AND I should get the certificates
        certificate_data = response.data["results"][0]
        self.assertEqual(
            certificate_data["id"], str(user_subcontractor_certificate.id)
        )

    def test_create_subcontactors_certifcate(self):
        #  Given that I have an authenticated user with 1 subcontractor
        self.client.force_authenticate(user=self.user)

        user_subcontractor = SubcontractorFactory(
            contact__uploaded_by=self.user
        )
        # When I create a Subcontractor
        url = reverse(
            self.url_name, kwargs={"subcontractor_id": user_subcontractor.id}
        )
        data = {
            "certificate_id": FileFactory().id,
            "file_category": SubcontractorCertificate.FileCategory.TAX_DOCUMENT,
        }

        response = self.client.post(url, data)

        # Then I should get a 201 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["file_category"], data["file_category"])
        self.assertEqual(response.data["contractor"], user_subcontractor.id)
        self.assertIsNotNone(response.data["certificate"])

    def test_update_subcontactors_certifcate(self):
        #  Given that I have an authenticated user with 1 subcontractor
        self.client.force_authenticate(user=self.user)

        user_subcontractor = SubcontractorFactory(
            contact__uploaded_by=self.user
        )
        user_subcontractor_certificate = SubcontractorCertificateFactory(
            contractor=user_subcontractor, certificate=FileFactory()
        )

        # When I updatee a Subcontractor certificate
        url = reverse(
            self.url_name,
            kwargs={
                "subcontractor_id": user_subcontractor.id,
                "pk": user_subcontractor_certificate.id,
            },
        )

        payload = {
            "file_category": SubcontractorCertificate.FileCategory.TAX_DOCUMENT,
            "certificate_id": FileFactory().id,
        }

        response = self.client.patch(url, data=payload)

        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["file_category"], payload["file_category"]
        )
        self.assertEqual(response.data["contractor"], user_subcontractor.id)
        self.assertIsNotNone(response.data["certificate"])
        self.assertEqual(
            response.data["certificate"]["id"], str(payload["certificate_id"])
        )

    def test_delete_subcontactors_certifcate(self):
        #  Given that I have an authenticated user with 1 subcontractor
        self.client.force_authenticate(user=self.user)

        user_subcontractor = SubcontractorFactory(
            contact__uploaded_by=self.user
        )
        user_subcontractor_certificate = SubcontractorCertificateFactory(
            contractor=user_subcontractor, certificate=FileFactory()
        )

        # When I delete a Subcontractor certificate
        url = reverse(
            self.url_name,
            kwargs={
                "subcontractor_id": user_subcontractor.id,
                "pk": user_subcontractor_certificate.id,
            },
        )

        response = self.client.delete(url)

        # Then I should get a 204 response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)


class SubcontractorLicenseTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.subcontractor = SubcontractorFactory()

        self.url = reverse(
            "contacts:subcontractor-licenses",
            kwargs={"subcontractor_id": self.subcontractor.id},
        )
        super().setUp()

    def test_unauthenticated_user(self):
        # Given an unauthenticated user
        self.client.force_authenticate(user=None)

        # When I try to get the licenses
        res = self.client.get(self.url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to create a license
        res = self.client.post(self.url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to update a license
        license = LicensesFactory(owner_contact=self.subcontractor)
        detail_url = reverse(
            "contacts:subcontractor-license",
            kwargs={
                "subcontractor_id": self.subcontractor.id,
                "pk": license.id,
            },
        )
        res = self.client.patch(detail_url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to delete a license
        res = self.client.delete(detail_url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_subcontractor_license(self):
        # Given an authenticated user
        self.client.force_authenticate(user=self.user)

        # When I try to create a license
        file = FileFactory.create()
        payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "Subcontractor License 1",
            "license_number": "SC1234",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "attachment": file.id,
        }

        res = self.client.post(self.url, data=payload)
        # Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # And number of licenses should be 1
        self.assertEqual(self.subcontractor.licenses.count(), 1)

        # Verify the license was created correctly
        license = self.subcontractor.licenses.first()
        self.assertEqual(license.license_type, payload["license_type"])
        self.assertEqual(license.name, payload["name"])
        self.assertEqual(license.license_number, payload["license_number"])

    def test_retrieve_update_delete_subcontractor_license(self):
        # Given an authenticated user and a license
        license = LicensesFactory(owner_contact=self.subcontractor)
        self.client.force_authenticate(user=self.user)

        # When I try to get the licenses
        res = self.client.get(self.url)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And list of licenses should have 1 item
        self.assertEqual(len(res.data["results"]), 1)

        # And When I try to update a license
        file = FileFactory.create()
        url = reverse(
            "contacts:subcontractor-license",
            kwargs={
                "subcontractor_id": self.subcontractor.id,
                "pk": license.id,
            },
        )

        payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "Updated License Name",
            "license_number": "UPDATED123",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "attachment": file.id,
        }

        res = self.client.patch(url, data=payload)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And license should be updated
        license.refresh_from_db()
        self.assertEqual(license.name, payload["name"])
        self.assertEqual(license.license_number, payload["license_number"])

        # And When I try to delete a license
        res = self.client.delete(url)
        # Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # Verify it's marked as deleted
        license.refresh_from_db()
        self.assertTrue(license.is_deleted)

    def test_create_subcontractor_license_with_additional_attachments(self):
        self.client.force_authenticate(user=self.user)

        # Create main attachment and additional attachments
        main_file = FileFactory.create()
        additional_files = [FileFactory.create() for _ in range(3)]

        payload = {
            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
            "name": "License with attachments",
            "license_number": "ATTACH123",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "attachment": main_file.id,
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(file.id) for file in additional_files],
                }
            ],
        }

        res = self.client.post(self.url, data=payload, format="json")
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # Verify the additional attachments were saved
        license = self.subcontractor.licenses.first()
        self.assertEqual(license.additional_attachments.count(), 3)
        self.assertEqual(len(res.data["all_attachments"]), 4)


class SubcontractorInsuranceTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        self.subcontractor = SubcontractorFactory()

        self.url = reverse(
            "contacts:subcontractor-insurance",
            kwargs={"subcontractor_id": self.subcontractor.id},
        )
        super().setUp()

    def test_unauthenticated_user(self):
        # Given an unauthenticated user
        self.client.force_authenticate(user=None)

        # When I try to get the insurances
        res = self.client.get(self.url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to create an insurance
        res = self.client.post(self.url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to update an insurance
        insurance = InsuranceFactory(owner_contact=self.subcontractor)
        detail_url = reverse(
            "contacts:subcontractor-insurance-detail",
            kwargs={
                "subcontractor_id": self.subcontractor.id,
                "pk": insurance.id,
            },
        )
        res = self.client.patch(detail_url, data={})
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to delete an insurance
        res = self.client.delete(detail_url)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_subcontractor_insurance(self):
        # Given an authenticated user
        self.client.force_authenticate(user=self.user)

        # When I try to create an insurance
        file = FileFactory.create()
        payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "Subcontractor Insurance 1",
            "broker": "Broker Name",
            "agent": "Agent Name",
            "policy_number": "POL1234",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "policy": file.id,
        }

        res = self.client.post(self.url, data=payload)
        # Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # And number of insurances should be 1
        self.assertEqual(self.subcontractor.insurances.count(), 1)

        # Verify the insurance was created correctly
        insurance = self.subcontractor.insurances.first()
        self.assertEqual(insurance.insurance_type, payload["insurance_type"])
        self.assertEqual(insurance.carrier, payload["carrier"])
        self.assertEqual(insurance.policy_number, payload["policy_number"])

    def test_retrieve_update_delete_subcontractor_insurance(self):
        # Given an authenticated user and an insurance
        insurance = InsuranceFactory(owner_contact=self.subcontractor)
        self.client.force_authenticate(user=self.user)

        # When I try to get the insurances
        res = self.client.get(self.url)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And list of insurances should have 1 item
        self.assertEqual(len(res.data["results"]), 1)

        # And When I try to update an insurance
        file = FileFactory.create()
        url = reverse(
            "contacts:subcontractor-insurance-detail",
            kwargs={
                "subcontractor_id": self.subcontractor.id,
                "pk": insurance.id,
            },
        )

        payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "Updated Carrier",
            "broker": "Updated Broker",
            "policy_number": "UPDPOL123",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "policy": file.id,
        }

        res = self.client.patch(url, data=payload)
        # Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And insurance should be updated
        insurance.refresh_from_db()
        self.assertEqual(insurance.carrier, payload["carrier"])
        self.assertEqual(insurance.broker, payload["broker"])
        self.assertEqual(insurance.policy_number, payload["policy_number"])

        # And When I try to delete an insurance
        res = self.client.delete(url)
        # Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # Verify it's marked as deleted
        insurance.refresh_from_db()
        self.assertTrue(insurance.is_deleted)

    def test_create_subcontractor_insurance_with_additional_attachments(self):
        self.client.force_authenticate(user=self.user)

        # Create main policy and additional attachments
        main_policy = FileFactory.create()
        additional_files = [FileFactory.create() for _ in range(3)]

        payload = {
            "insurance_type": Insurance.InsuranceType.GENERAL_LIABILITY,
            "carrier": "Insurance with attachments",
            "policy_number": "POLATTACH123",
            "valid_from": "2021-12-12",
            "valid_to": "2021-12-12T00:00:00Z",
            "reminder": "2021-05-22 11:50:09.831000+00:00",
            "policy": main_policy.id,
            "additional_attachments": [
                {
                    "operation": "add",
                    "files": [str(file.id) for file in additional_files],
                }
            ],
        }

        res = self.client.post(self.url, data=payload, format="json")
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        # Verify the additional attachments were saved
        insurance = self.subcontractor.insurances.first()
        self.assertEqual(insurance.additional_attachments.count(), 3)
        self.assertEqual(len(res.data["all_attachments"]), 4)
