from django.urls import path

from .views import CreateListContactView
from .views import CreateListSubcontractorCertificateView
from .views import CreateListSubcontractorView
from .views import RetrieveDestroySubcontractorCertificateView
from .views import RetrieveUpdateDestroyContactView
from .views import RetrieveUpdateDestroySubcontractorView
from .views import SubcontractorInsuranceCreateListView
from .views import SubcontractorInsuranceRetrieveUpdateDeleteView
from .views import SubcontractorLicenseCreateListView
from .views import SubcontractorLicenseRetrieveUpdateDeleteView


app_name = "contacts"
urlpatterns = [
    path(
        "subcontractors/<uuid:subcontractor_id>/certificates/",
        CreateListSubcontractorCertificateView.as_view(),
        name="subcontractor_certificates",
    ),
    path(
        "subcontractors/<uuid:subcontractor_id>/certificates/<pk>/",
        RetrieveDestroySubcontractorCertificateView.as_view(),
        name="subcontractor_certificates",
    ),
    path(
        "subcontractors/",
        CreateListSubcontractorView.as_view(),
        name="subcontractor",
    ),
    path(
        "subcontractors/<uuid:pk>/",
        RetrieveUpdateDestroySubcontractorView.as_view(),
        name="subcontractor",
    ),
    path(
        "<uuid:pk>/",
        RetrieveUpdateDestroyContactView.as_view(),
        name="contact",
    ),
    path(
        "",
        CreateListContactView.as_view(),
        name="contact",
    ),
    # Subcontractor License URLs
    path(
        "<subcontractor_id>/licenses/",
        SubcontractorLicenseCreateListView.as_view(),
        name="subcontractor-licenses",
    ),
    path(
        "<subcontractor_id>/licenses/<pk>/",
        SubcontractorLicenseRetrieveUpdateDeleteView.as_view(),
        name="subcontractor-license",
    ),
    # Subcontractor Insurance URLs
    path(
        "<subcontractor_id>/insurance/",
        SubcontractorInsuranceCreateListView.as_view(),
        name="subcontractor-insurance",
    ),
    path(
        "<subcontractor_id>/insurance/<pk>/",
        SubcontractorInsuranceRetrieveUpdateDeleteView.as_view(),
        name="subcontractor-insurance-detail",
    ),
]
