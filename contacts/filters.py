import django_filters
from project.models import Project

from .models import Contact
from .models import Subcontractor


class ContactFilter(django_filters.FilterSet):
    first_name = django_filters.CharFilter(lookup_expr="icontains")
    last_name = django_filters.CharFilter(lookup_expr="icontains")
    email = django_filters.CharFilter(lookup_expr="icontains")
    phone_number = django_filters.CharFilter(lookup_expr="icontains")
    extension = django_filters.CharFilter(lookup_expr="icontains")
    company = django_filters.CharFilter(lookup_expr="icontains")

    address_line_1 = django_filters.CharFilter(lookup_expr="icontains")
    address_line_2 = django_filters.CharFilter(lookup_expr="icontains")
    city = django_filters.Char<PERSON>ilter(lookup_expr="icontains")
    state = django_filters.Char<PERSON><PERSON><PERSON>(lookup_expr="icontains")
    zip_code = django_filters.Char<PERSON>ilter(lookup_expr="icontains")
    country = django_filters.CharFilter(lookup_expr="icontains")

    class Meta:
        model = Contact
        fields = [
            "first_name",
            "last_name",
            "email",
            "address_line_1",
            "address_line_2",
            "city",
            "state",
            "zip_code",
            "country",
            "phone_number",
            "company",
        ]


class SubcontractFilter(django_filters.FilterSet):

    first_name = django_filters.CharFilter(
        lookup_expr="icontains", field_name="contact__first_name"
    )
    last_name = django_filters.CharFilter(
        lookup_expr="icontains", field_name="contact__last_name"
    )
    email = django_filters.CharFilter(
        lookup_expr="icontains", field_name="contact__email"
    )
    phone_number = django_filters.CharFilter(
        lookup_expr="icontains", field_name="contact__phone_number"
    )
    company = django_filters.CharFilter(
        lookup_expr="icontains", field_name="contact__company"
    )
    project_id = django_filters.CharFilter(method="filter_by_project_id")

    class Meta:
        model = Subcontractor
        fields = [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "company",
            "project_id",
        ]

    def filter_by_project_id(self, queryset, name, value):
        project = Project.objects.filter(id=value).first()
        if not project:
            return queryset.none()
        return Subcontractor.objects.get_subcontractor_by_project(project)
