# create a serializer for the Company model
from typing import Union

from accounts.serializers import PhoneNumberField
from accounts.serializers import UserSerializer
from company.models import Insurance
from company.models import Licenses
from company.serializers import AttachmentHandlerMixin
from company.serializers import CompanySerializer
from company.serializers import DateTimeToDate<PERSON>ield
from company.serializers import FileOperationSerializer
from company.serializers import InsuranceSerializer
from company.serializers import LicensesSerializer
from core.serializers import TimezoneConverterMixin
from django.shortcuts import get_object_or_404
from drf_extra_fields.fields import Base64ImageField
from general.serializers import InFavoriteSerializerMixin
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from storage.models import File
from storage.serializers import FileSerializer

from .models import Contact
from .models import Subcontractor
from .models import SubcontractorCertificate


class ContactSerializer(
    TimezoneConverterMixin,
    InFavoriteSerializerMixin,
    serializers.ModelSerializer,
):
    uploaded_by = serializers.SerializerMethodField()
    is_subcontractor = serializers.SerializerMethodField()
    subcontractor_id = serializers.SerializerMethodField()
    profile_picture = Base64ImageField(required=False)
    formatted_mobile = PhoneNumberField(source="phone_number")
    subcontractor = serializers.SerializerMethodField()

    class Meta:
        model = Contact
        read_only_fields = ("created", "updated", "uploaded_by")
        exclude = ("is_deleted",)

    def create(self, validated_data):
        validated_data["uploaded_by"] = self.context["request"].user
        return super().create(validated_data)

    def get_uploaded_by(self, obj: Contact):
        return UserSerializer(obj.uploaded_by, context=self.context).data

    def get_is_subcontractor(self, obj: Contact):
        return Subcontractor.objects.filter(contact=obj).exists()

    def get_subcontractor_id(self, obj: Contact):
        if self.get_is_subcontractor(obj):
            return Subcontractor.objects.get(contact=obj).id
        return None

    def get_subcontractor(self, obj: Contact):

        if not self.get_is_subcontractor(obj):
            return None

        class SlimSubcontractSerializer(SubcontractSerializer):
            contact = None

        return SlimSubcontractSerializer(
            obj.subcontractor, context=self.context
        ).data


class BaseListContactSerializer(
    InFavoriteSerializerMixin, serializers.ModelSerializer
):
    class Meta:
        model = Contact
        read_only_fields = ("created", "updated", "uploaded_by")
        exclude = ("is_deleted",)


class ListContactSerializer(
    BaseListContactSerializer,
):
    pass


class ListContactV2Serializer(
    BaseListContactSerializer,
):
    pass


class SubcontactorCertificateSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    certificate = serializers.SerializerMethodField()
    certificate_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = SubcontractorCertificate
        read_only_fields = ("created", "updated", "contractor")
        exclude = ("is_deleted",)

    def create(self, validated_data: dict):
        contractor_id = (
            self.context.get("request")
            .parser_context.get("kwargs")
            .get("subcontractor_id")
        )

        validated_data["contractor_id"] = contractor_id

        raw_certificate_id = validated_data.pop("certificate_id")
        certificate: Union[File, None] = File.objects.filter(
            id=raw_certificate_id
        ).first()
        if not certificate:
            raise ValidationError("provide a valid certificate")

        validated_data["certificate"] = certificate
        return super().create(validated_data)

    def get_certificate(self, obj: SubcontractorCertificate):
        return FileSerializer(obj.certificate).data


class ListSubcontractorV2Serializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = Subcontractor
        read_only_fields = ("created", "updated")
        exclude = ("is_deleted",)


class SubcontractSerializer(
    AttachmentHandlerMixin, ListSubcontractorV2Serializer
):
    certificates = serializers.SerializerMethodField()
    contact = ContactSerializer(required=False)
    contact_id = serializers.PrimaryKeyRelatedField(
        queryset=Contact.objects.all(), required=False
    )
    tax_documents = serializers.SerializerMethodField()
    certificates_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False,
    )
    licenses = LicensesSerializer(many=True, required=False)
    insurances = InsuranceSerializer(many=True, required=False)

    def create(self, validated_data: dict):
        # User cannot supply both contact and contact_id
        if validated_data.get("contact") and validated_data.get("contact_id"):
            raise ValidationError(
                "You cannot supply both contact and contact_id at the same time"
            )

        user = self.context["request"].user
        company = user.company_set.first()
        company_id = company.id if company else None

        contact_raw_data = validated_data.pop("contact", None)

        if contact_raw_data:
            contact_raw_data = self.parse_profile_picture(contact_raw_data)
            contact_serializer = ContactSerializer(
                data=contact_raw_data, context=self.context
            )
            contact_serializer.is_valid(raise_exception=True)
            contact = self.create_contact(
                user, contact_serializer.validated_data
            )
            validated_data["contact"] = contact

        contact_id = validated_data.pop("contact_id", None)
        if contact_id:
            validated_data["contact"] = contact_id

        file_ids = validated_data.pop("certificates_ids", [])
        licenses_data = validated_data.pop("licenses", [])
        insurances_data = validated_data.pop("insurances", [])

        # Create subcontractor
        subcontractor = self.create_subcontractor(validated_data)

        # Create certificates
        self.create_subcontractor_certificate(subcontractor, file_ids)

        # Create licenses using LicensesSerializer
        for license_data in licenses_data:
            self.process_license(subcontractor, company_id, license_data)

        # Create insurances using InsuranceSerializer
        for insurance_data in insurances_data:
            self.process_insurance(subcontractor, company_id, insurance_data)

        return subcontractor

    def update(self, instance, validated_data):
        user = self.context["request"].user
        company = user.company_set.first()
        company_id = company.id if company else None

        # Process contact-related updates
        contact_raw_data = validated_data.pop("contact", None)
        contact_id = validated_data.pop("contact_id", None)

        # Check if user is trying to use both methods simultaneously
        if contact_raw_data and contact_id:
            raise ValidationError(
                "You cannot supply both contact and contact_id at the same time"
            )

        # Case 1: Update existing contact with new data
        if contact_raw_data:
            contact_raw_data = self.parse_profile_picture(contact_raw_data)
            contact_serializer = ContactSerializer(
                instance.contact,
                data=contact_raw_data,
                context=self.context,
                partial=True,  # Allow partial updates
            )
            contact_serializer.is_valid(raise_exception=True)
            contact_serializer.save()

        # Case 2: Change to a different existing contact
        elif contact_id and instance.contact.id != contact_id:
            # Verify the contact exists
            try:
                new_contact = Contact.objects.get(id=contact_id)
                # Check if contact is already linked to another subcontractor
                if (
                    Subcontractor.objects.filter(contact=new_contact)
                    .exclude(id=instance.id)
                    .exists()
                ):
                    raise ValidationError(
                        "This contact is already linked to another subcontractor"
                    )
                instance.contact = new_contact
                instance.save(update_fields=["contact"])
            except Contact.DoesNotExist as err:
                raise ValidationError(
                    f"Contact with ID {contact_id} does not exist"
                ) from err

        # Handle licenses data
        licenses_data = validated_data.pop("licenses", [])
        if licenses_data is not None:
            for license_data in licenses_data:
                self.process_license(instance, company_id, license_data)

        # Handle insurances data
        insurances_data = validated_data.pop("insurances", [])
        if insurances_data is not None:
            for insurance_data in insurances_data:
                self.process_insurance(instance, company_id, insurance_data)

        # Update other fields of the subcontractor
        return super().update(instance, validated_data)

    def parse_profile_picture(self, data: dict):
        profile_picture = data.pop("profile_picture", None)

        if profile_picture:
            data["profile_picture"] = self.initial_data.get("contact", {}).get(
                "profile_picture", ""
            )

        return data

    def create_contact(self, user, contact_data: dict):
        return Contact.objects.create(
            **{
                **contact_data,
                "uploaded_by": user,
            }
        )

    def create_subcontractor(self, data: dict):
        try:
            return Subcontractor.objects.create(**data)
        except Exception as e:
            raise ValidationError(
                code="none_field_error", detail=str(e)
            ) from e

    def create_subcontractor_certificate(self, subcontractor, file_id: list):
        files = File.objects.filter(id__in=file_id)
        created_certificates = SubcontractorCertificate.objects.bulk_create(
            [
                SubcontractorCertificate(
                    contractor=subcontractor,
                    certificate=file,
                    file_category=SubcontractorCertificate.FileCategory.TAX_DOCUMENT,
                )
                for file in files
            ]
        )

        return created_certificates

    def get_certificates(self, obj: Subcontractor):
        if obj.certificates.count():
            return SubcontactorCertificateSerializer(
                obj.certificates, many=True
            ).data
        return []

    def get_tax_documents(self, obj: Subcontractor):
        if obj.certificates.count():
            return SubcontactorCertificateSerializer(
                obj.certificates.filter(
                    file_category=SubcontractorCertificate.FileCategory.TAX_DOCUMENT
                ),
                many=True,
            ).data
        return []

    def process_license(self, subcontractor, company_id, license_data):
        """Process license data using LicensesSerializer for validation and creation/update"""
        # Add owner_contact and company_id to the data
        license_data["owner_contact"] = subcontractor.id
        license_data["company_id"] = company_id

        # Check if this is an update (has ID) or create (no ID)
        license_id = license_data.get("id")

        # Create a request context similar to what would be in the view
        context = {
            "request": self.context["request"],
            "view": type("obj", (), {"kwargs": {"company_id": company_id}}),
        }

        if license_id:
            # This is an update operation
            try:
                license_instance = Licenses.objects.get(
                    id=license_id, owner_contact=subcontractor
                )
                # Create a serializer with the existing instance
                license_serializer = LicensesSerializer(
                    license_instance,
                    data=license_data,
                    context=context,
                    partial=True,
                )
                license_serializer.is_valid(raise_exception=True)
                return license_serializer.save()
            except Licenses.DoesNotExist as e:
                raise ValidationError(
                    f"License with ID {license_id} not found for this subcontractor"
                ) from e
        else:
            # This is a create operation
            license_serializer = LicensesSerializer(
                data=license_data, context=context
            )
            license_serializer.is_valid(raise_exception=True)

            # Create the license with owner_contact
            validated_data = license_serializer.validated_data
            validated_data["owner_contact"] = subcontractor
            validated_data["company_id"] = company_id

            # We need to handle the creation manually to set owner_contact
            # Extract the special fields like in the original serializer
            attachment_id = validated_data.pop("attachment", None)
            attachment_filename = validated_data.pop(
                "attachment_filename", None
            )
            attachment_operations = validated_data.pop(
                "additional_attachments", []
            )

            # Create the license object
            license_obj = Licenses.objects.create(**validated_data)

            # Handle attachment if provided
            if attachment_id:
                attachment = get_object_or_404(
                    File, pk=attachment_id, is_deleted=False
                )
                license_obj.attachment = attachment

                # Update filename if provided
                if attachment_filename and attachment_filename.strip():
                    attachment.original_file_name = attachment_filename
                    attachment.save(update_fields=["original_file_name"])

                license_obj.save()

            # Handle additional attachments
            if attachment_operations:
                handler = AttachmentHandlerMixin()
                handler._handle_attachment_operations(
                    license_obj, attachment_operations
                )

            return license_obj

    def process_insurance(self, subcontractor, company_id, insurance_data):
        """Process insurance data using InsuranceSerializer for validation and creation/update"""
        # Add owner_contact and company_id to the data
        insurance_data["owner_contact"] = subcontractor.id
        insurance_data["company_id"] = company_id

        # Check if this is an update (has ID) or create (no ID)
        insurance_id = insurance_data.get("id")

        # Create a request context similar to what would be in the view
        context = {
            "request": self.context["request"],
            "view": type("obj", (), {"kwargs": {"company_id": company_id}}),
        }

        if insurance_id:
            # This is an update operation
            try:
                insurance_instance = Insurance.objects.get(
                    id=insurance_id, owner_contact=subcontractor
                )
                # Create a serializer with the existing instance
                insurance_serializer = InsuranceSerializer(
                    insurance_instance,
                    data=insurance_data,
                    context=context,
                    partial=True,
                )
                insurance_serializer.is_valid(raise_exception=True)
                return insurance_serializer.save()
            except Insurance.DoesNotExist as e:
                raise ValidationError(
                    f"Insurance with ID {insurance_id} not found for this subcontractor"
                ) from e
        else:
            # This is a create operation
            insurance_serializer = InsuranceSerializer(
                data=insurance_data, context=context
            )
            insurance_serializer.is_valid(raise_exception=True)

            # Create the insurance with owner_contact
            validated_data = insurance_serializer.validated_data
            validated_data["owner_contact"] = subcontractor
            validated_data["company_id"] = company_id

            # We need to handle the creation manually to set owner_contact
            # Extract the special fields like in the original serializer
            policy_id = validated_data.pop("policy", None)
            policy_filename = validated_data.pop("policy_filename", None)
            attachment_operations = validated_data.pop(
                "additional_attachments", []
            )

            # Create the insurance object
            insurance_obj = Insurance.objects.create(**validated_data)

            # Handle policy if provided
            if policy_id:
                policy = get_object_or_404(
                    File, pk=policy_id, is_deleted=False
                )
                insurance_obj.policy = policy

                # Update filename if provided
                if policy_filename and policy_filename.strip():
                    policy.original_file_name = policy_filename
                    policy.save(update_fields=["original_file_name"])

                insurance_obj.save()

            # Handle additional attachments
            if attachment_operations:
                handler = AttachmentHandlerMixin()
                handler._handle_attachment_operations(
                    insurance_obj, attachment_operations
                )

            return insurance_obj


class SubcontractorLicenseSerializer(
    TimezoneConverterMixin, AttachmentHandlerMixin, serializers.ModelSerializer
):
    valid_from = DateTimeToDateField()
    attachment = serializers.UUIDField(required=False, write_only=True)
    attachment_filename = serializers.CharField(
        required=False, write_only=True
    )
    additional_attachments = serializers.ListField(
        child=FileOperationSerializer(), required=False, write_only=True
    )
    all_attachments = serializers.SerializerMethodField()
    id = serializers.UUIDField(required=False)
    company = CompanySerializer(read_only=True)

    class Meta:
        model = Licenses
        read_only_fields = (
            "created",
            "updated",
            "created_by",
            "owner_contact",
        )
        ordering = ("name",)
        exclude = ("is_deleted",)

    def create(self, validated_data):
        # Get subcontractor ID from the URL kwargs
        subcontractor_id = (
            self.context.get("request")
            .parser_context.get("kwargs")
            .get("subcontractor_id")
        )

        subcontractor = get_object_or_404(Subcontractor, id=subcontractor_id)
        user = self.context.get("request").user
        company = user.company_set.first()

        # Handle attachment
        attachment_id = validated_data.pop("attachment", None)
        attachment_filename = validated_data.pop("attachment_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        validated_data["owner_contact"] = subcontractor
        if company:
            validated_data["company"] = company
        validated_data["created_by"] = user

        instance = super().create(validated_data)

        # Attach main file if provided
        if attachment_id:
            attachment = get_object_or_404(
                File, pk=attachment_id, is_deleted=False
            )
            instance.attachment = attachment

            # Update filename if provided and not empty
            if attachment_filename and attachment_filename.strip():
                attachment.original_file_name = attachment_filename
                attachment.save(update_fields=["original_file_name"])

            instance.save()

        # Handle additional attachments if provided
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)

        return instance

    def update(self, instance, validated_data):
        # Handle attachment and additional attachments
        attachment_id = validated_data.pop("attachment", None)
        attachment_filename = validated_data.pop("attachment_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        # Check if the new attachment ID is different from the current one before archiving
        if (
            attachment_id
            and instance.attachment
            and str(instance.attachment.id) != str(attachment_id)
        ):
            old_attachment = instance.attachment
            # Archive the old file by calling its archive method.
            old_attachment.archive()
            # Add the archived file to the additional attachments.
            instance.additional_attachments.add(old_attachment)

        instance = super().update(instance, validated_data)

        # If a new attachment is provided, retrieve it and assign.
        if attachment_id:
            new_attachment = get_object_or_404(
                File, pk=attachment_id, is_deleted=False
            )

            # Update filename if provided and not empty
            if attachment_filename and attachment_filename.strip():
                new_attachment.original_file_name = attachment_filename
                new_attachment.save(update_fields=["original_file_name"])

            # Only set the attachment if it's different or not set
            if not instance.attachment or str(instance.attachment.id) != str(
                attachment_id
            ):
                instance.attachment = new_attachment
                instance.save()
        # Update filename of existing attachment if no new attachment is provided
        elif (
            attachment_filename
            and attachment_filename.strip()
            and instance.attachment
        ):
            instance.attachment.original_file_name = attachment_filename
            instance.attachment.save(update_fields=["original_file_name"])

        # Handle additional attachments operations
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)
        return instance

    def get_all_attachments(self, obj: Licenses):
        attachments = list(obj.additional_attachments.filter(is_deleted=False))
        # Include the main attachment in the all_attachments list if it exists
        if obj.attachment and not obj.attachment.is_deleted:
            attachments.append(obj.attachment)
        return FileSerializer(attachments, many=True).data


class SubcontractorInsuranceSerializer(
    TimezoneConverterMixin, AttachmentHandlerMixin, serializers.ModelSerializer
):
    valid_from = DateTimeToDateField()
    policy = serializers.UUIDField(required=False, write_only=True)
    policy_filename = serializers.CharField(required=False, write_only=True)
    additional_attachments = serializers.ListField(
        child=FileOperationSerializer(), required=False, write_only=True
    )
    all_attachments = serializers.SerializerMethodField()
    id = serializers.UUIDField(required=False)
    company = CompanySerializer(read_only=True)

    class Meta:
        model = Insurance
        read_only_fields = (
            "created",
            "updated",
            "created_by",
            "owner_contact",
        )
        exclude = ("is_deleted",)

    def validate_policy(self, value):
        """Validate that the policy file exists and is not deleted."""
        if (
            value
            and not File.objects.filter(id=value, is_deleted=False).exists()
        ):
            raise serializers.ValidationError(
                "The specified policy file does not exist or is deleted"
            )
        return value

    def create(self, validated_data):
        # Get subcontractor ID from the URL kwargs
        subcontractor_id = (
            self.context.get("request")
            .parser_context.get("kwargs")
            .get("subcontractor_id")
        )

        subcontractor = get_object_or_404(Subcontractor, id=subcontractor_id)
        user = self.context.get("request").user
        company = user.company_set.first()

        # Handle policy and additional attachments
        policy_id = validated_data.pop("policy", None)
        policy_filename = validated_data.pop("policy_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        validated_data["owner_contact"] = subcontractor
        if company:
            validated_data["company"] = company
        validated_data["created_by"] = user

        instance = super().create(validated_data)

        # Attach policy file if provided
        if policy_id:
            policy = get_object_or_404(File, pk=policy_id, is_deleted=False)

            # Update filename if provided and not empty
            if policy_filename and policy_filename.strip():
                policy.original_file_name = policy_filename
                policy.save(update_fields=["original_file_name"])

            instance.policy = policy
            instance.save()

        # Handle additional attachments if provided
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)

        return instance

    def update(self, instance, validated_data):
        # Handle policy and additional attachments
        policy_id = validated_data.pop("policy", None)
        policy_filename = validated_data.pop("policy_filename", None)
        attachment_operations = validated_data.pop(
            "additional_attachments", []
        )

        # Check if the new policy ID is different from the current one before archiving
        if (
            policy_id
            and instance.policy
            and str(instance.policy.id) != str(policy_id)
        ):
            old_policy = instance.policy
            old_policy.archive()
            instance.additional_attachments.add(old_policy)

        instance = super().update(instance, validated_data)

        # If a new policy file is provided, retrieve it and assign.
        if policy_id:
            new_policy = get_object_or_404(
                File, pk=policy_id, is_deleted=False
            )

            # Update filename if provided and not empty
            if policy_filename and policy_filename.strip():
                new_policy.original_file_name = policy_filename
                new_policy.save(update_fields=["original_file_name"])

            # Only set the policy if it's different or not set
            if not instance.policy or str(instance.policy.id) != str(
                policy_id
            ):
                instance.policy = new_policy
                instance.save()
        # Update filename of existing policy if no new policy is provided
        elif policy_filename and policy_filename.strip() and instance.policy:
            instance.policy.original_file_name = policy_filename
            instance.policy.save(update_fields=["original_file_name"])

        # Handle additional attachments operations
        if attachment_operations:
            self._handle_attachment_operations(instance, attachment_operations)
        return instance

    def get_all_attachments(self, obj: Insurance):
        attachments = list(obj.additional_attachments.filter(is_deleted=False))
        # Include the policy file in the all_attachments list if it exists
        if obj.policy and not obj.policy.is_deleted:
            attachments.append(obj.policy)
        return FileSerializer(attachments, many=True).data
