from company.filters import <PERSON><PERSON><PERSON>er
from company.filters import LicensesFilter
from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.models import ModulePermission
from company.permissions import CompanyModulePermission
from contacts.filters import ContactFilter
from contacts.filters import SubcontractFilter
from core.constants import Features
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from notifications.mixins import NotificationCleanupMixin
from recent_app.mixins import CreateRecentActivityMixin
from rest_framework import filters
from rest_framework import status
from rest_framework.generics import ListCreateAPIView
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from storage.models import File

from .models import Contact
from .models import Subcontractor
from .models import SubcontractorCertificate
from .serializers import ContactSerializer
from .serializers import SubcontactorCertificateSerializer
from .serializers import SubcontractorInsuranceSerializer
from .serializers import SubcontractorLicenseSerializer
from .serializers import SubcontractSerializer


class BaseContactView:
    serializer_class = ContactSerializer
    permission_classes = (IsAuthenticated, CompanyModulePermission)

    module_name = ModulePermission.CONTACTS

    filter_backends = [
        filters.SearchFilter,
        filters.OrderingFilter,
        DjangoFilterBackend,
    ]
    filterset_class = ContactFilter
    search_fields = [
        "first_name",
        "last_name",
        "email",
        "address_line_1",
        "address_line_2",
        "city",
        "state",
        "zip_code",
        "country",
        "phone_number",
        "company",
    ]
    ordering_fields = "__all__"
    ordering = ["last_name", "first_name"]

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return Contact.objects.get_contacts_uploaded_by_user(
            current_company.user
        )


class CreateListContactView(BaseContactView, ListCreateAPIView):
    pass


class RetrieveUpdateDestroyContactView(
    CreateRecentActivityMixin, BaseContactView, RetrieveUpdateDestroyAPIView
):
    def get_category(self):
        """
        Returns the category of the feature for the recent activity record.
        Uses the Features class to specify the category.
        """
        return Features.CONTACTS


class BaseSubcontractorView:
    serializer_class = SubcontractSerializer
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.SUBCONTRACTORS

    filter_backends = [
        filters.SearchFilter,
        filters.OrderingFilter,
        DjangoFilterBackend,
    ]
    filterset_class = SubcontractFilter
    search_fields = [
        "contact__first_name",
        "contact__last_name",
        "contact__email",
        "contact__phone_number",
        "contact__company",
    ]

    ordering_fields = [
        "contact__first_name",
        "contact__last_name",
        "contact__email",
        "contact__phone_number",
        "contact__company",
    ]
    ordering = ["contact__last_name"]

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return Subcontractor.objects.get_subcontractor_added_by_user(
            current_company.user
        ).prefetch_related("licenses", "insurances")


class RetrieveUpdateDestroySubcontractorView(
    BaseSubcontractorView,
    RetrieveUpdateDestroyAPIView,
):
    def get_category(self):
        """
        Returns the category of the feature for the recent activity record.
        Uses the Features class to specify the category.
        """
        return Features.SUBCONTRACTORS


class CreateListSubcontractorView(BaseSubcontractorView, ListCreateAPIView):
    pass


# subcontractor certificates


class BaseSubcontractorCertificateView:

    serializer_class = SubcontactorCertificateSerializer
    permission_classes = (IsAuthenticated, CompanyModulePermission)
    module_name = ModulePermission.CERTIFICATES

    def get_queryset(self):
        if not self.subcontractor:
            return SubcontractorCertificate.objects.none()
        return SubcontractorCertificate.objects.get_subcontractor_certificates(
            self.subcontractor
        )

    @property
    def subcontractor(self):
        subcontractor_id = self.kwargs.get("subcontractor_id")
        return Subcontractor.objects.filter(id=subcontractor_id).first()

    def get_object(self):
        return get_object_or_404(
            self.get_queryset(),
            id=self.kwargs.get("pk"),
        )


class CreateListSubcontractorCertificateView(
    BaseSubcontractorCertificateView, ListCreateAPIView
):
    pass


class RetrieveDestroySubcontractorCertificateView(
    BaseSubcontractorCertificateView, RetrieveUpdateDestroyAPIView
):
    pass


class SubcontractorLicenseCreateListView(ListCreateAPIView):
    serializer_class = SubcontractorLicenseSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = LicensesFilter
    search_fields = ["name", "license_number"]
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        subcontractor_id = self.kwargs.get("subcontractor_id")
        subcontractor = get_object_or_404(Subcontractor, id=subcontractor_id)
        return Licenses.objects.filter(
            owner_contact=subcontractor, is_deleted=False
        )


class SubcontractorLicenseRetrieveUpdateDeleteView(
    NotificationCleanupMixin,
    CreateRecentActivityMixin,
    RetrieveUpdateDestroyAPIView,
):
    serializer_class = SubcontractorLicenseSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        subcontractor_id = self.kwargs.get("subcontractor_id")
        subcontractor = get_object_or_404(Subcontractor, id=subcontractor_id)
        return Licenses.objects.filter(
            owner_contact=subcontractor, is_deleted=False
        )

    def get_object(self):
        return get_object_or_404(self.get_queryset(), id=self.kwargs.get("pk"))

    def perform_update(self, serializer):
        instance = serializer.save()
        # Get updated fields from serializer
        updated_fields = serializer.validated_data.keys()
        self.cleanup_notifications(instance, updated_fields)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_deleted = True

        # Mark main attachment as deleted if it exists
        if instance.attachment:
            instance.attachment.is_deleted = True
            instance.attachment.save()

        # Mark additional attachments as deleted
        instance.additional_attachments.update(is_deleted=True)

        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_category(self):
        return Features.LICENSES


class SubcontractorInsuranceCreateListView(ListCreateAPIView):
    serializer_class = SubcontractorInsuranceSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = InsuranceFilter
    search_fields = ["carrier", "broker", "agent", "policy_number"]
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        subcontractor_id = self.kwargs.get("subcontractor_id")
        subcontractor = get_object_or_404(Subcontractor, id=subcontractor_id)
        return Insurance.objects.filter(
            owner_contact=subcontractor, is_deleted=False
        )


class SubcontractorInsuranceRetrieveUpdateDeleteView(
    NotificationCleanupMixin,
    CreateRecentActivityMixin,
    RetrieveUpdateDestroyAPIView,
):
    serializer_class = SubcontractorInsuranceSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        subcontractor_id = self.kwargs.get("subcontractor_id")
        subcontractor = get_object_or_404(Subcontractor, id=subcontractor_id)
        return Insurance.objects.filter(
            owner_contact=subcontractor, is_deleted=False
        )

    def get_object(self):
        return get_object_or_404(self.get_queryset(), id=self.kwargs.get("pk"))

    def perform_update(self, serializer):
        instance = serializer.save()
        # Get updated fields from serializer
        updated_fields = serializer.validated_data.keys()
        self.cleanup_notifications(instance, updated_fields)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_deleted = True

        # Mark policy as deleted if it exists
        if instance.policy:
            try:
                instance.policy.is_deleted = True
                instance.policy.save()
            except File.DoesNotExist:
                instance.policy = None

        # Mark additional attachments as deleted
        instance.additional_attachments.update(is_deleted=True)

        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_category(self):
        return Features.INSURANCES
