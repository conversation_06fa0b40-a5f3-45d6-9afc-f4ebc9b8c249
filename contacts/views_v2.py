import logging

from company.models import Company
from contacts.models import Subcontractor
from contacts.serializers import ListContactV2Serializer
from contacts.serializers import ListSubcontractorV2Serializer
from contacts.views import BaseContactView
from contacts.views import BaseSubcontractorView
from core.mixins.cache_mixins import BaseCacheViewMixin
from rest_framework import generics

logger = logging.getLogger(__name__)


class ListContactViewV2(
    BaseCacheViewMixin, BaseContactView, generics.ListAPIView
):
    serializer_class = ListContactV2Serializer

    def get_service_name(self) -> str:
        return "contact_service"

    def get_cache_entity_name(self) -> str:
        return "contact"

    def _get_fresh_queryset_impl(self, user):
        from company.models import Company
        from contacts.models import Contact

        current_company = Company.objects.get_user_current_company(user)
        contacts = Contact.objects.get_contacts_uploaded_by_user(
            current_company.user
        )
        return contacts


class ListSubcontractorViewV2(
    BaseCacheViewMixin, BaseSubcontractor<PERSON>iew, generics.ListAPIView
):
    serializer_class = ListSubcontractorV2Serializer

    def get_service_name(self) -> str:
        return "subcontractor_service"

    def get_cache_entity_name(self) -> str:
        return "subcontractor"

    def get_queryset(self):

        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        subcontractor = Subcontractor.objects.get_subcontractor_added_by_user(
            current_company.user
        ).prefetch_related("licenses", "insurances")

        return subcontractor

    def _get_fresh_queryset_impl(self, user):

        return self.get_queryset()
