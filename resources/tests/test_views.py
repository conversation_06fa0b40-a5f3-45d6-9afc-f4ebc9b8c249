from accounts.factories import UserFactory
from django.urls import reverse
from rest_framework import status
from rest_framework.response import Response
from testing.base import BaseAPITest

from ..factories import ResourceFactory
from ..factories import TagFactory


class ResourceTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.tag = TagFactory(user=self.user)
        self.resource = ResourceFactory(user=self.user, tags=[self.tag])

        self.list_resource_url = reverse(
            "resources:resource_list",
        )

        self.update_resource_url = reverse(
            "resources:resource_detail",
            kwargs={
                "pk": str(self.resource.id),
            },
        )
        super().setUp()

    def test_unauthenticated_user_interracting_with_resources(self):
        #  Given an ananymous user
        #  When I try to get my Resources
        res: Response = self.client.get(self.list_resource_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And When I try to create a new Resource
        res: Response = self.client.post(
            self.list_resource_url,
            {
                "url": "https://www.google.com",
                "tags": [self.tag.id],
            },
        )
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an ananymous user
        #  When I try to update my Resources
        res: Response = self.client.patch(self.update_resource_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an anaonymous user
        #  When I try to delete my Resources
        res: Response = self.client.delete(self.update_resource_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_user_interracting_with_resources(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        #  When I try to get my Resources
        res: Response = self.client.get(self.list_resource_url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And When I try to create a new Resource
        res: Response = self.client.post(
            self.list_resource_url,
            {
                "url": "https://www.google.com",
                "tags_name": ["same_tag"],
                "description": "This is a test description",
            },
        )

        # Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        #  And When I try to update my Resources
        res: Response = self.client.patch(
            self.update_resource_url, {"tags_name": ["new_tag", "same_tag"]}
        )
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)
        # And the resource should have the new tag
        self.assertEqual(len(res.data["tags"]), 2)
        self.assertEqual(res.data["tags"][0]["name"], "new_tag")

        #  When I try to delete my Resources
        res: Response = self.client.delete(self.update_resource_url)
        #  Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # And When I try to get a resource that not my resource
        url = reverse(
            "resources:resource_detail",
            kwargs={
                "pk": 1000,
            },
        )

        res: Response = self.client.get(url)
        # Then I should get a 404
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)


class TagTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.tag = TagFactory(user=self.user)

        self.list_tag_url = reverse(
            "resources:tags_list",
        )

        self.update_tag_url = reverse(
            "resources:tags_detail",
            kwargs={
                "pk": str(self.tag.id),
            },
        )

    def test_unauthenticated_user_interracting_with_tags(self):
        #  Given an ananymous user
        #  When I try to get my Tag
        res: Response = self.client.get(self.list_tag_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        # And  When I try to create a Tag
        res: Response = self.client.post(
            self.list_tag_url,
            {
                "name": "test",
            },
        )
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an ananymous user
        #  When I try to update my Tag
        res: Response = self.client.patch(self.update_tag_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an anaonymous user
        #  When I try to delete my Tag
        res: Response = self.client.delete(self.update_tag_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_user_interracting_with_tags(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        #  When I try to get my Tag
        res: Response = self.client.get(self.list_tag_url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # And  When I try to create a Tag
        res: Response = self.client.post(
            self.list_tag_url,
            {
                "name": "test",
            },
        )
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)

        #  And When I try to update my Tag
        res: Response = self.client.patch(
            self.update_tag_url, {"name": "test2"}
        )
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)
        # And the name should be updated
        self.assertEqual(res.data["name"], "test2")

        #  When I try to delete my Tag
        res: Response = self.client.delete(self.update_tag_url)
        #  Then I should get a 204
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # And When I try to get a resource that not my Tag
        url = reverse(
            "resources:tags_detail",
            kwargs={
                "pk": 1000,
            },
        )

        res: Response = self.client.get(url)
        # Then I should get a 404
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)
