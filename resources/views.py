from company.models import Company
from company.models import ModulePermission
from company.permissions import CompanyModulePermission
from core.constants import Features
from django_filters.rest_framework import DjangoFilterBackend
from recent_app.mixins import CreateRecentActivityMixin
from resources.models import Resource
from resources.models import Tag
from resources.serializers import ResourceSerializer
from resources.serializers import TagSerializer
from rest_framework import filters
from rest_framework.generics import ListCreateAPIView
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.permissions import IsAuthenticated

from .filters import ResourceFilter
from .filters import TagFilter
from .tasks import generate_webpage_thumbnail


class ResourceListView(ListCreateAPIView):
    serializer_class = ResourceSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ["url", "description", "tags__name"]
    filterset_class = ResourceFilter
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.RESOURCES

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return Resource.objects.get_user_resources(current_company.user)

    def perform_create(self, serializer):
        instance = serializer.save()
        instance.thumbnail_status = "pending"
        instance.save()
        generate_webpage_thumbnail.delay(instance.id)


class ResourceDetailView(
    CreateRecentActivityMixin, RetrieveUpdateDestroyAPIView
):
    serializer_class = ResourceSerializer
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.RESOURCES

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return Resource.objects.get_user_resources(current_company.user)

    def perform_destroy(self, instance: Resource):
        instance.is_deleted = True
        instance.save()

    def perform_update(self, serializer):
        instance = serializer.save()
        if "url" in serializer.validated_data:
            instance.thumbnail_status = "pending"
            instance.thumbnail = None
            instance.save()
            generate_webpage_thumbnail.delay(instance.id)

    def get_category(self):
        return Features.RESOURCES


class TagListView(ListCreateAPIView):
    serializer_class = TagSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = TagFilter
    search_fields = ["name"]
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.RESOURCES

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return Tag.objects.get_user_tags(current_company.user)


class TagDetailView(RetrieveUpdateDestroyAPIView):
    serializer_class = TagSerializer
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.RESOURCES

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return Tag.objects.get_user_tags(current_company.user)

    def perform_destroy(self, instance: Tag):
        instance.is_deleted = True
        instance.save()
