from typing import List
from typing import Optional

from accounts.models import User
from core.serializers import TimezoneConverterMixin
from rest_framework import serializers
from typing_extensions import TypedDict
from general.serializers import InFavoriteSerializerMixin

from .models import Resource
from .models import Tag


class TagValidatedData(TypedDict):
    name: str
    created_at: str
    updated_at: str
    user: Optional[User]


class ResourceValidatedData(TypedDict):
    url: str
    description: str
    created_at: str
    updated_at: str
    user: Optional[User]
    tags_name: List[str]


class TagSerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ["id", "name", "created_at", "updated_at"]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data: TagValidatedData):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class ResourceSerializer(TimezoneConverterMixin, InFavoriteSerializerMixin, serializers.ModelSerializer):
    tags = serializers.SerializerMethodField()
    tags_name = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
    )

    class Meta:
        model = Resource
        exclude = ("is_deleted",)
        read_only_fields = (
            "user",
            "id",
            "tags",
            "thumbnail_status",
            "thumbnail_error",
        )
        write_only_fields = ("tags_ids",)

    def create(self, validated_data: ResourceValidatedData):
        user = self.context["request"].user
        validated_data["user"] = user
        tag_names = validated_data.pop("tags_name")

        tags = Tag.objects.create_tags(tag_names, user)
        validated_data["tags"] = tags

        return super().create(validated_data)

    def update(
        self, instance: Resource, validated_data: ResourceValidatedData
    ):
        tag_names = validated_data.pop("tags_name", [])
        user = self.context["request"].user

        tags = Tag.objects.create_tags(tag_names, user)
        validated_data["tags"] = tags

        return super().update(instance, validated_data)

    def get_tags(self, obj: Resource):
        return TagSerializer(obj.tags.all(), many=True).data
