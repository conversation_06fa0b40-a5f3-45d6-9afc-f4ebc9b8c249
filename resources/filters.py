from django_filters import Char<PERSON>ilter
from django_filters import FilterSet

from .models import Resource
from .models import Tag


class ResourceFilter(FilterSet):
    url = CharFilter(lookup_expr="icontains")
    description = CharFilter(lookup_expr="icontains")
    tags = CharFilter(field_name="tags__name", lookup_expr="icontains")

    class Meta:
        model = Resource
        fields = ["url", "description", "tags"]


class TagFilter(FilterSet):
    name = CharFilter(lookup_expr="icontains")

    class Meta:
        model = Tag
        fields = ["name"]
