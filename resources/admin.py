from django.contrib import admin

from .models import Resource
from .tasks import generate_webpage_thumbnail


@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    list_display = ["description", "url", "thumbnail_status", "created_at"]
    actions = ["generate_thumbnails"]

    @admin.action(description="Generate thumbnails for selected resources")
    def generate_thumbnails(self, request, queryset):
        for resource in queryset:
            generate_webpage_thumbnail.delay(resource.id)
