import io
import logging
from typing import Union

from celery import shared_task
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import InMemoryUploadedFile
from PIL import Image
from playwright.sync_api import sync_playwright

from .models import Resource

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_webpage_thumbnail(self, resource_id: int):
    """
    Generate a thumbnail for a webpage using Playwright headless browser.
    Includes retry logic for temporary failures.
    """
    try:
        resource = Resource.objects.get(id=resource_id)

        if not resource.url:
            logger.error(f"Resource {resource_id} has no URL")
            resource.thumbnail_status = "failed"
            resource.save()
            return

        # Update status to processing
        resource.thumbnail_status = "processing"
        resource.save()

        screenshot_bytes = capture_webpage_screenshot(resource.url)
        if screenshot_bytes:
            save_thumbnail(resource, screenshot_bytes)
            # Update status to completed after successful save
            resource.thumbnail_status = "completed"
            resource.save()
        else:
            resource.thumbnail_status = "failed"
            resource.save()

    except Resource.DoesNotExist:
        logger.error(f"Resource with id {resource_id} not found")
    except Exception as exc:
        logger.error(
            f"Failed to generate thumbnail for resource {resource_id}: {exc}"
        )

        # Update status to failed if we're out of retries
        if self.request.retries >= self.max_retries:
            try:
                resource = Resource.objects.get(id=resource_id)
                resource.thumbnail_status = "failed"
                resource.save()
            except Resource.DoesNotExist:
                pass
        raise self.retry(
            exc=exc, countdown=60
        ) from None  # Retry after 1 minute


def capture_webpage_screenshot(url: str) -> Union[bytes, None]:
    """
    Capture a screenshot of a webpage using Playwright.
    Returns the screenshot as bytes or None if failed.
    """
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                viewport={"width": 1280, "height": 800},
                device_scale_factor=1.5,
            )

            page = context.new_page()

            try:
                page.goto(url, timeout=30000, wait_until="networkidle")
            except Exception as e:
                logger.warning(f"Page load timeout for {url}: {e}")
                page.goto(url, timeout=30000, wait_until="domcontentloaded")

            page.wait_for_timeout(2000)

            screenshot_bytes = page.screenshot(
                type="png",
                full_page=False,
                clip={"x": 0, "y": 0, "width": 1280, "height": 800},
            )

            browser.close()
            return screenshot_bytes

    except Exception as e:
        logger.error(f"Failed to capture screenshot for {url}: {e}")
        return None


def save_thumbnail(resource: Resource, screenshot_bytes: bytes):
    """
    Process the screenshot and save it as a thumbnail.
    """
    try:
        img = Image.open(io.BytesIO(screenshot_bytes))
        img.thumbnail((300, 300))

        buffer = io.BytesIO()
        img.save(buffer, format="PNG")
        buffer.seek(0)

        thumb = ContentFile(buffer.getvalue())
        file_name = f"thumbnail_{resource.id}.png"

        if hasattr(resource, "thumbnail"):
            resource.thumbnail.delete(save=False)

        resource.thumbnail.save(
            file_name,
            InMemoryUploadedFile(
                thumb, None, file_name, "image/png", thumb.tell, None
            ),
            save=True,
        )

    except Exception as e:
        logger.error(
            f"Failed to save thumbnail for resource {resource.id}: {e}"
        )
        resource.thumbnail_status = "failed"
        resource.save()
        raise


@shared_task
def generate_all_webpage_thumbnails():
    """
    Generate thumbnails for all resources that don't have them yet.
    Processes in chunks to avoid overload.
    """
    resources_without_thumbnails = Resource.objects.filter(
        is_deleted=False, thumbnail=""
    ).exclude(url="")

    # Process in chunks of 50
    chunk_size = 50
    for i in range(0, resources_without_thumbnails.count(), chunk_size):
        chunk = resources_without_thumbnails[i : i + chunk_size]
        for resource in chunk:
            generate_webpage_thumbnail.delay(resource.id)
