import factory

from .models import Resource
from .models import Tag


class TagFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Tag

    name = factory.Faker("name")
    user = factory.SubFactory("accounts.factories.UserFactory")


class ResourceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Resource

    url = factory.Faker("url")
    description = factory.Faker("text")
    user = factory.SubFactory("accounts.factories.UserFactory")
    # tags = factory.RelatedFactory(TagFactory, "resource")

    @factory.post_generation
    def tags(self, create, extracted, **kwargs):
        if not create or not extracted:
            # Simple build, or nothing to add, do nothing.
            return

        # Add the iterable of groups using bulk addition
        self.tags.add(*extracted)
