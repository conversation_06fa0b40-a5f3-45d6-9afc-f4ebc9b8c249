from typing import List

from accounts.models import User
from core.models import BaseModel
from django.db import models


class ResourceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False)
            .prefetch_related("tags")
            .order_by("-created_at")
        )

    def get_user_resources(self, user: User):
        return self.get_queryset().filter(user=user)


class TagManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False).order_by("name")

    def get_user_tags(self, user: User):
        return self.get_queryset().filter(user=user)

    def create_tags(self, tag_names: List[str], user: User) -> List["Tag"]:
        tags: List["Tag"] = []
        for tag_name in tag_names:
            tag = Tag.objects.filter(name=tag_name, user=user).first()
            if not tag:
                tag, _ = Tag.objects.get_or_create(name=tag_name, user=user)

            tags.append(tag)
        return tags

    def create_tags_from_serializer_validated_data(
        self, validated_data: dict, user: User
    ):
        """
        This function is used to create tags from a serializer's validated_data
        it extracts the tag_names from the validated_data and creates the tags
        """
        tag_names: List[str] = validated_data.pop("tag_names", [])
        tags = Tag.objects.create_tags(tag_names, user)
        return tags


class Tag(BaseModel):
    name = models.CharField(max_length=255)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    objects: TagManager = TagManager()

    def __str__(self):
        return self.name


class Resource(BaseModel):
    url = models.URLField()
    description = models.TextField()
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    tags = models.ManyToManyField(Tag, blank=True)
    thumbnail = models.ImageField(
        upload_to="resource_thumbnails/", null=True, blank=True
    )
    thumbnail_status = models.CharField(
        max_length=20,
        choices=[
            ("pending", "Pending"),
            ("processing", "Processing"),
            ("completed", "Completed"),
            ("failed", "Failed"),
        ],
        default="pending",
    )
    objects = ResourceManager()
