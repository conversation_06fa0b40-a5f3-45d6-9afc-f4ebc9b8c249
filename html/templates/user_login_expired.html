<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tuulbox Verification Link Expired</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
      }

      body {
        background-color: #f9f9f9;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        position: relative;
      }

      .header {
        padding: 20px;
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      }

      .logo {
        height: 40px;
      }

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        max-width: 480px;
        margin: 0 auto;
        text-align: center;
      }

      .icon-container {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background-color: #ebf8ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
      }

      .info-icon {
        color: #4a7fc1;
        font-weight: bold;
        font-size: 28px;
        border: 2px solid #4a7fc1;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      h1 {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
      }

      p {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 24px;
      }

      .btn {
        background-color: #102340;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 10px 16px;
        font-size: 14px;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s;
        position: relative;
        border-radius: 12px;
      }

      .btn:hover {
        background-color: #374151;
      }

      .btn:disabled {
        background-color: #6b7280;
        cursor: not-allowed;
      }

      .countdown {
        display: inline-block;
        margin-left: 5px;
        font-size: 14px;
      }

      .toast-notification {
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #fafffbfb;
        border: 1px solid #48bb78;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 16px;
        width: 90%;
        max-width: 440px;
        z-index: 100;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .toast-notification.show {
        opacity: 1;
      }

      .toast-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #237f56;
      }

      .toast-icon img {
        width: 24px;
        height: 24px;
      }

      .toast-content {
        flex: 1;
      }

      .toast-title {
        font-weight: 600;
        font-size: 16px;
        color: #333;
        margin-bottom: 4px;
      }

      .toast-message {
        font-size: 14px;
        color: #666;
      }

      .toast-close {
        color: #666;
        font-size: 20px;
        cursor: pointer;
        background: none;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px;
        margin-left: 8px;
      }

      /* Loading indicator styles */
      .btn-loading {
        position: relative;
        pointer-events: none;
      }

      .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-top: -8px;
        margin-left: -8px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 0.8s linear infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div id="toast" class="toast-notification">
      <div class="toast-icon">
        <img
          src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTAgMjBDMCA4Ljk1NDMxIDguOTU0MzEgMCAyMCAwQzMxLjA0NTcgMCA0MCA4Ljk1NDMxIDQwIDIwQzQwIDMxLjA0NTcgMzEuMDQ1NyA0MCAyMCA0MEM4Ljk1NDMxIDQwIDAgMzEuMDQ1NyAwIDIwWiIgZmlsbD0iIzJGODU1QSIvPgo8cGF0aCBkPSJNMjAgMzBDMjUuNSAzMCAzMCAyNS41IDMwIDIwQzMwIDE0LjUgMjUuNSAxMCAyMCAxMEMxNC41IDEwIDEwIDE0LjUgMTAgMjBDMTAgMjUuNSAxNC41IDMwIDIwIDMwWiIgc3Ryb2tlPSIjRkZGRkZDIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xNS43NSAyMC4wMDE5TDE4LjU4IDIyLjgzMTlMMjQuMjUgMTcuMTcxOSIgc3Ryb2tlPSIjRkZGRkZDIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo="
          alt="Success Icon"
        />
      </div>
      <div class="toast-content">
        <div class="toast-title">Verification Link Sent</div>
        <div class="toast-message">
          A new verification link has been sent to your email. Please check your
          inbox to continue.
        </div>
      </div>
      <button class="toast-close" onclick="hideToast()">×</button>
    </div>

    <header class="header">
      <img
        src="https://tuulbox.app/_nuxt/tuulbox-logo.Cmr8DTpg.png"
        alt="Logo"
        class="logo"
      />
    </header>

    <main class="content">
      <div class="icon-container">
        <span class="info-icon">!</span>
      </div>

      <h1>Verification Link Expired</h1>

      <p>
        Oops! It looks like the verification link sent to
        <strong>{{email}}</strong> is no longer valid. No worries — simply
        request for a new link to verify your account and get started.
      </p>

      <button class="btn" id="requestBtn">Request new verification link</button>
    </main>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const requestBtn = document.getElementById("requestBtn");
        const toast = document.getElementById("toast");

        // Countdown timer settings
        const cooldownTime = 60; // 60 seconds cooldown
        let countdownTimer = null;
        let remainingTime = 0;

        // Check if there's a stored cooldown timestamp
        const storedCooldownEnd = localStorage.getItem(
          "verificationCooldownEnd"
        );
        if (storedCooldownEnd) {
          const cooldownEnd = parseInt(storedCooldownEnd, 10);
          const currentTime = Math.floor(Date.now() / 1000);

          if (cooldownEnd > currentTime) {
            // Cooldown is still active
            remainingTime = cooldownEnd - currentTime;
            startCountdown(remainingTime);
          } else {
            // Cooldown has expired, clear it
            localStorage.removeItem("verificationCooldownEnd");
          }
        }

        requestBtn.addEventListener("click", function () {
          if (requestBtn.disabled) {
            return;
          }

          // Disable the button and start loading state
          requestBtn.disabled = true;
          requestBtn.textContent = "Sending...";

          // Get the user ID from the URL
          const pathParts = window.location.pathname.split("/");
          const userId = pathParts[pathParts.length - 2];

          // Make API call to resend verification email
          fetch(`/api/auth/resend-verification-email/${userId}/`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": getCookie("csrftoken"),
            },
          })
            .then((response) => {
              return response.json().then((data) => {
                return { status: response.status, data };
              });
            })
            .then((result) => {
              if (result.status === 200) {
                // Success - show toast
                toast.classList.add("show");

                // Hide after 5 seconds
                setTimeout(function () {
                  hideToast();
                }, 5000);

                // Get cooldown period from server response or use default
                const cooldownSeconds =
                  result.data.cooldown_seconds || cooldownTime;

                // Start the cooldown timer
                const currentTime = Math.floor(Date.now() / 1000);
                const cooldownEnd = currentTime + cooldownSeconds;

                // Store the cooldown end time in localStorage
                localStorage.setItem(
                  "verificationCooldownEnd",
                  cooldownEnd.toString()
                );

                // Start the countdown
                startCountdown(cooldownSeconds);
              } else if (result.status === 429) {
                // Too many requests - start countdown with remaining time
                const remainingSeconds =
                  result.data.remaining_seconds || cooldownTime;
                startCountdown(remainingSeconds);

                // Update localStorage
                const currentTime = Math.floor(Date.now() / 1000);
                const cooldownEnd = currentTime + remainingSeconds;
                localStorage.setItem(
                  "verificationCooldownEnd",
                  cooldownEnd.toString()
                );
              } else {
                // Other error
                requestBtn.disabled = false;
                requestBtn.textContent = "Request new verification link";

                // Show error message
                if (result.data && result.data.error) {
                  alert(result.data.error);
                } else {
                  alert("An error occurred. Please try again later.");
                }
              }
            })
            .catch((error) => {
              // Re-enable the button
              requestBtn.disabled = false;
              requestBtn.textContent = "Request new verification link";

              console.error("Error:", error);
              alert("An error occurred. Please try again later.");
            });
        });

        // Function to start the countdown timer
        function startCountdown(seconds) {
          remainingTime = seconds;
          requestBtn.disabled = true;

          // Update the button text with the initial countdown
          updateCountdownText();

          // Clear any existing timer
          if (countdownTimer) {
            clearInterval(countdownTimer);
          }

          // Start the countdown interval
          countdownTimer = setInterval(function () {
            remainingTime--;

            if (remainingTime <= 0) {
              // Countdown finished
              clearInterval(countdownTimer);
              requestBtn.disabled = false;
              requestBtn.textContent = "Request new verification link";
              localStorage.removeItem("verificationCooldownEnd");
            } else {
              // Update the countdown text
              updateCountdownText();
            }
          }, 1000);
        }

        // Function to update the countdown text
        function updateCountdownText() {
          requestBtn.textContent = `Request new verification link (${remainingTime}s)`;
        }
      });

      function hideToast() {
        const toast = document.getElementById("toast");
        toast.classList.remove("show");
      }

      // Function to get CSRF token from cookies
      function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== "") {
          const cookies = document.cookie.split(";");
          for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === name + "=") {
              cookieValue = decodeURIComponent(
                cookie.substring(name.length + 1)
              );
              break;
            }
          }
        }
        return cookieValue;
      }
    </script>
  </body>
</html>
