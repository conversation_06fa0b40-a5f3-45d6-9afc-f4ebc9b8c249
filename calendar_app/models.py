from accounts.models import User
from core.models import BaseModel
from django.db import models


class CalendarEventManager(models.Manager):
    def get_user_events(self, user):
        return self.filter(created_by=user)


class CalendarEvent(BaseModel):
    """
    Model representing a calendar event
    """
    summary = models.TextField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    start = models.DateTimeField(blank=True, null=True)
    end = models.DateTimeField(blank=True, null=True)
    timezone = models.CharField(max_length=255, blank=True, null=True)
    duration = models.DurationField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    google_event_id = models.CharField(max_length=255, blank=True, null=True)
    calendar_id = models.CharField(max_length=255, blank=True, null=True)
    original_data = models.JSONField(default=dict)

    objects = CalendarEventManager()

    def __str__(self):
        return f"{self.summary} ({self.start} - {self.end})"
