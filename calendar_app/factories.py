import factory
from accounts.factories import UserFactory

from django.utils import timezone

from .models import CalendarEvent




class CalendarEventFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CalendarEvent

    summary = factory.Faker("sentence")
    description = factory.Faker("text")
    start = factory.Faker("date_time", tzinfo=timezone.utc)
    end = factory.Faker("date_time", tzinfo=timezone.utc)
    created_by = factory.SubFactory(UserFactory)
    google_event_id = factory.Faker("uuid4")

