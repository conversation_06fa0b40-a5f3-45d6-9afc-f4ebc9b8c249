from googleapiclient.discovery import build
from integrations.models import GoogleCalendarIntegration
from google.oauth2.credentials import Credentials
import json

class GoogleCalendarService:
    calendar_client = None
    credentials = None

    def __init__(self, integration: "GoogleCalendarIntegration"):
        self.initialize_service(integration)

    def initialize_service(self, integration: "GoogleCalendarIntegration"):
        self.credentials = Credentials.from_authorized_user_info(
            json.loads(integration.credentials)
        )
        self.initialize_calendar_service()

    def initialize_calendar_service(self):
        """Initializes the calendar service using the stored credentials."""
        if self.credentials:
            self.calendar_client = build('calendar', 'v3', credentials=self.credentials)

    def _execute_calendar_api_request(self, method_name, params):
        """
        Executes a Google Calendar API request and handles potential errors.

        Args:
            method_name: The name of the calendar API method to call (e.g. 'list', 'list').
            params: A dictionary containing the parameters for the API request.

        Returns:
            The response from the API request (or None if no service available or error occurs).
        """
        if not self.calendar_client:
            return None

        try:
            result = getattr(self.calendar_client.events(), method_name)(**params).execute().get('items', [])
            return result
        except Exception as e:
            print(f"Error getting calendar {method_name}: {e}")
            return None

    def get_calendar_events(self, calendar_id, **kwargs):
        """
        Retrieves events from the specified Google Calendar.

        Args:
            calendar_id: The ID of the calendar to retrieve events from.

        Returns:
            A list of Google Calendar events (or None if no service available).
        """
        params = {
            'calendarId': calendar_id,
            'singleEvents': True,
            'orderBy': 'startTime',
            'timeMax': kwargs.get('end_date'),
            'timeMin': kwargs.get('start_date'),
        }
        response = self._execute_calendar_api_request('list', params)
        return response or []


    def create_event(self, calendar_id, event_body):
        """
        Creates a new event in Google Calendar.

        Args:
            calendar_id: The ID of the calendar to create the event in.
            event_body: A dictionary containing event details as per Google Calendar API.

        Returns:
            The created event object or None on failure.
        """
    def create_event(self, calendar_id, event_body):
        if not self.calendar_client:
            return None

        event = self.calendar_client.events().insert(
            calendarId=calendar_id,
            body=event_body,
            conferenceDataVersion=1
        ).execute()
        return event

    def update_event(self, calendar_id, event_id, event_body):
        """
        Updates an existing event in Google Calendar.

        Args:
            calendar_id: The ID of the calendar containing the event.
            event_id: The ID of the event to update.
            event_body: A dictionary containing updated event details.

        Returns:
            The updated event object or None on failure.
        """
        if not self.calendar_client:
            return None

        event = self.calendar_client.events().update(
            calendarId=calendar_id,  
            eventId=event_id,
            body=event_body
        ).execute()
        return event

    def delete_event(self, calendar_id, event_id):
        """
        Deletes an event from Google Calendar.

        Args:
            calendar_id: The ID of the calendar containing the event.
            event_id: The ID of the event to delete.

        Returns:
            None
        """
        if not self.calendar_client:
            return None

        self.calendar_client.events().delete(
            calendarId=calendar_id,  
            eventId=event_id
        ).execute()
    
    def list_calendars(self):
        """
        Lists all calendars for the authorized user.
        """
        if not self.calendar_client:
            return None

        try:
            calendars = self.calendar_client.calendarList().list().execute().get('items', [])
            return calendars
        except Exception as e:
            print(f"Error listing calendars: {e}")
            return None