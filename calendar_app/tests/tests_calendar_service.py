import json
from unittest.mock import patch
from unittest.mock import PropertyMock

from accounts.factories import UserFactory
from calendar_app.services.calendar_service import GoogleCalendarService
from integrations.factories import GoogleCalendarIntegrationFactory
from testing.base import BaseAPITest


class GoogleCalendarServiceTests(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()

        self.integration = GoogleCalendarIntegrationFactory(
            user=self.user, credentials=json.dumps({})
        )

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService._execute_calendar_api_request"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    def test_get_calendar_events(
        self,
        mock_initialize_service,
        mock_execute_calendar_api_request,
        mock_initialize_calendar_service,
    ):
        mock_initialize_calendar_service.return_value = None
        mock_execute_calendar_api_request.return_value = {
            "items": [{"summary": "Test Event"}]
        }
        mock_initialize_service.return_value = None

        service_instance = GoogleCalendarService(self.integration)

        events = service_instance.get_calendar_events(calendar_id="primary")

        self.assertEqual(len(events), 1)

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.create_event"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    def test_create_event(
        self,
        mock_initialize_calendar_service,
        mock_initialize_service,
        mock_create_event,
    ):
        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None
        service_instance = GoogleCalendarService(self.integration)

        created_event_id = "created_event_id"
        mock_create_event.return_value = created_event_id

        event_body = {
            "summary": "New Event",
            "start": {"dateTime": "2024-04-02T08:00:00"},
            "end": {"dateTime": "2024-04-02T10:00:00"},
            "description": "This is a description for the new event.",
        }

        new_event_id = service_instance.create_event(event_body)

        self.assertEqual(new_event_id, created_event_id)

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.update_event"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    def test_update_event(
        self,
        mock_initialize_calendar_service,
        mock_initialize_service,
        mock_update_event,
    ):
        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None
        service_instance = GoogleCalendarService(self.integration)

        updated_event_id_mock = "updated_event_id"
        mock_update_event.return_value = updated_event_id_mock

        event_id = "existing_event_id"
        event_body = {
            "summary": "Updated Event",
            "start": {"dateTime": "2024-04-02T08:00:00"},
            "end": {"dateTime": "2024-04-02T10:00:00"},
        }

        returned_event_id = service_instance.update_event(event_id, event_body)

        self.assertEqual(returned_event_id, updated_event_id_mock)

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.delete_event"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    def test_delete_event(
        self,
        mock_initialize_calendar_service,
        mock_initialize_service,
        mock_delete_event,
    ):
        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None
        service_instance = GoogleCalendarService(self.integration)

        mock_delete_event.return_value = None

        event_id = "existing_event_id"

        service_instance.delete_event(eventId=event_id)

        mock_delete_event.assert_called_once_with(eventId=event_id)
