import json
from unittest.mock import patch
from unittest.mock import PropertyMock

from accounts.factories import UserFactory
from calendar_app.models import CalendarEvent
from django.urls import reverse
from integrations.factories import GoogleCalendarIntegrationFactory
from rest_framework import status
from testing.base import BaseAPITest


class CalendarEventAPITests(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()

        self.integration = GoogleCalendarIntegrationFactory(
            user=self.user, credentials=json.dumps({})
        )
        return super().setUp()

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    def test_create_calendar_event(
        self, mock_initialize_service, mock_initialize_calendar_service
    ):
        self.client.force_authenticate(user=self.user)

        url = reverse("calendar-event-list-create")
        payload = {
            "summary": "Test Event",
            "description": "Description of Test Event",
            "start": "2024-04-02T08:00:00",
            "end": "2024-04-02T10:00:00",
            "timezone": "UTC",
            "calendar_id": "primary",
        }
        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None
        with patch(
            "calendar_app.services.calendar_service.GoogleCalendarService.create_event"
        ) as mock_create_event:
            mock_create_event.return_value = {"id": "google_event_id"}

            response = self.client.post(url, payload, format="json")

            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertEqual(CalendarEvent.objects.count(), 1)
            self.assertEqual(
                CalendarEvent.objects.first().google_event_id,
                "google_event_id",
            )

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    def test_retrieve_calendar_event(
        self, mock_initialize_service, mock_initialize_calendar_service
    ):
        self.client.force_authenticate(user=self.user)

        event = CalendarEvent.objects.create(
            summary="Test Event",
            description="Description of Test Event",
            start="2024-04-02T08:00:00",
            end="2024-04-02T10:00:00",
            timezone="UTC",
            created_by=self.user,
            calendar_id="primary",
        )
        url = reverse("event-detail", kwargs={"pk": event.pk})

        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["summary"], event.summary)

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    def test_update_calendar_event(
        self, mock_initialize_service, mock_initialize_calendar_service
    ):
        self.client.force_authenticate(user=self.user)

        event = CalendarEvent.objects.create(
            summary="Test Event",
            description="Description of Test Event",
            start="2024-04-02T08:00:00",
            end="2024-04-02T10:00:00",
            timezone="UTC",
            created_by=self.user,
            calendar_id="primary",
        )
        url = reverse("event-detail", kwargs={"pk": event.pk})
        payload = {
            "summary": "Updated Event",
            "description": "Updated Description",
            "start": "2024-04-02T09:00:00",
            "end": "2024-04-02T11:00:00",
            "timezone": "UTC",
            "calendar_id": "primary",
        }

        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None

        response = self.client.put(url, payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(CalendarEvent.objects.count(), 1)
        self.assertEqual(
            CalendarEvent.objects.first().summary, "Updated Event"
        )

    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_calendar_service",
        new_callable=PropertyMock,
    )
    @patch(
        "calendar_app.services.calendar_service.GoogleCalendarService.initialize_service"
    )
    def test_delete_calendar_event(
        self, mock_initialize_service, mock_initialize_calendar_service
    ):
        self.client.force_authenticate(user=self.user)

        event = CalendarEvent.objects.create(
            summary="Test Event",
            description="Description of Test Event",
            start="2024-04-02T08:00:00",
            end="2024-04-02T10:00:00",
            timezone="UTC",
            created_by=self.user,
        )
        url = reverse("event-detail", kwargs={"pk": event.pk})

        mock_initialize_calendar_service.return_value = None
        mock_initialize_service.return_value = None

        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(CalendarEvent.objects.count(), 0)
