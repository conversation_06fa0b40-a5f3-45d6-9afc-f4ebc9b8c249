from datetime import datetime
from datetime import timedelta

from calendar_app.services.calendar_service import GoogleCalendarService
from company.models import Company
from company.models import ModulePermission
from company.permissions import CompanyModulePermission
from core.constants import Features
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from integrations.models import GoogleCalendarIntegration
from recent_app.mixins import CreateRecentActivityMixin
from rest_framework import filters
from rest_framework import status
from rest_framework.generics import ListCreateAPIView
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from .filters import CalendarEventFilter
from .models import CalendarEvent
from .serializers import CalendarDetailEventSerializer
from .serializers import CalendarEventSerializer
from .serializers import CalendarSerializer


class CalendarViewMixin:
    permission_classes = [IsAuthenticated, CompanyModulePermission]
    module_name = ModulePermission.CALENDAR

    def get_integration(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        integration = GoogleCalendarIntegration.objects.filter(
            user=current_company.user
        ).first()
        if not integration:
            raise GoogleCalendarIntegration.DoesNotExist(
                "Google Calendar integration not found"
            )
        return integration

    @property
    def calendar_service(self):
        integration = self.get_integration()
        return GoogleCalendarService(integration)


class CreateListView(CalendarViewMixin, ListCreateAPIView):
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = CalendarEventFilter
    search_fields = ["summary", "description"]
    pagination_class = None
    serializer_class = CalendarEventSerializer

    start_param = openapi.Parameter(
        "start",
        openapi.IN_QUERY,
        description="Start date of events (YYYY-MM-DD)",
        type=openapi.TYPE_STRING,
        format=openapi.FORMAT_DATE,
        example="2024-01-01",
        required=False,
    )

    end_param = openapi.Parameter(
        "end",
        openapi.IN_QUERY,
        description="End date of events (YYYY-MM-DD)",
        type=openapi.TYPE_STRING,
        format=openapi.FORMAT_DATE,
        example="2024-12-31",
        required=False,
    )

    @swagger_auto_schema(manual_parameters=[start_param, end_param])
    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)
            return self.get_paginated_response(serializer.data)
        except GoogleCalendarIntegration.DoesNotExist as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_404_NOT_FOUND
            )

    def get_paginated_response(self, data):
        return Response(
            {"count": len(data), "results": data}, status=status.HTTP_200_OK
        )

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        has_filters = bool(self.request.query_params)

        if has_filters:
            queryset = CalendarEvent.objects.filter(
                created_by=current_company.user
            )
            # Apply the filter backend
            for backend in list(self.filter_backends):
                queryset = backend().filter_queryset(
                    self.request, queryset, self
                )
            return queryset

        return self.synchronize_events().order_by("-start")

    def filter_queryset(self, queryset):
        # Let each filter backend filter the queryset
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def synchronize_events(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        start = self.request.query_params.get("start")
        end = self.request.query_params.get("end")
        today = datetime.now()
        if not start:
            start = (today.replace(day=1) - timedelta(days=15)).strftime(
                "%Y-%m-%d"
            )
        if not end:
            end = (today.replace(day=1) + timedelta(days=45)).strftime(
                "%Y-%m-%d"
            )

        start_date = datetime.strptime(start, "%Y-%m-%d")
        end_date = datetime.strptime(end, "%Y-%m-%d")
        duration = (end_date - start_date).days

        if duration > 60:
            end_date = start_date + timedelta(days=60)
            end = end_date.strftime("%Y-%m-%d")

        calendars = (
            self.calendar_service.calendar_client.calendarList()
            .list()
            .execute()
            .get("items", [])
        )

        synchronized_event_ids = set()

        start_date = datetime.strptime(start, "%Y-%m-%d").isoformat() + "Z"
        end_date = datetime.strptime(end, "%Y-%m-%d").isoformat() + "Z"

        for calendar in calendars:
            calendar_id = calendar["id"]
            calendar_events = self.calendar_service.get_calendar_events(
                calendar_id=calendar_id,
                start_date=start_date,
                end_date=end_date,
            )
            google_event_dict = {
                event["id"]: event for event in calendar_events
            }
            local_events = CalendarEvent.objects.filter(
                google_event_id__in=google_event_dict.keys(),
                created_by=current_company.user,
            )
            local_event_dict = {
                event.google_event_id: event for event in local_events
            }

            for google_event_id, google_event in google_event_dict.items():
                event_data = self.extract_event_data(google_event, calendar_id)
                if google_event_id in local_event_dict:
                    local_event = local_event_dict[google_event_id]
                    for key, value in event_data.items():
                        setattr(local_event, key, value)
                    local_event.save()
                    synchronized_event_ids.add(local_event.id)
                else:
                    new_event = CalendarEvent.objects.create(**event_data)
                    synchronized_event_ids.add(new_event.id)

        return CalendarEvent.objects.filter(
            id__in=synchronized_event_ids, created_by=current_company.user
        )

    def extract_event_data(self, event, calendar_id):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        start = event.get("start", {}).get("dateTime") or event.get(
            "start", {}
        ).get("date")
        end = event.get("end", {}).get("dateTime") or event.get("end", {}).get(
            "date"
        )
        timezone = event.get("start", {}).get("timeZone")

        return {
            "google_event_id": event.get("id"),
            "summary": event.get("summary"),
            "description": event.get("description"),
            "start": start,
            "end": end,
            "timezone": timezone,
            "created_by": current_company.user,
            "calendar_id": calendar_id,
            "original_data": event,
        }

    def perform_create(self, serializer):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        serializer.save(created_by=current_company.user)


class RetrieveUpdateDestroyView(
    CreateRecentActivityMixin, CalendarViewMixin, RetrieveUpdateDestroyAPIView
):
    """
    View to retrieve, update, and delete individual calendar events.
    """

    serializer_class = CalendarDetailEventSerializer

    queryset = CalendarEvent.objects.all()

    def get_category(self):
        return Features.CALENDAR_EVENTS

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.calendar_service.delete_event(
                instance.calendar_id, instance.google_event_id
            )
            instance.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except GoogleCalendarIntegration.DoesNotExist as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Failed to delete event: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ListCalendarsView(CalendarViewMixin, APIView):
    @swagger_auto_schema(
        responses={200: CalendarSerializer(many=True)},
        operation_description="List all calendars for the current user",
    )
    def get(self, request, *args, **kwargs):
        try:
            calendars = self.calendar_service.list_calendars()
            if calendars is None:
                return Response(
                    {"error": "Unable to fetch calendars"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            serializer = CalendarSerializer(calendars, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except GoogleCalendarIntegration.DoesNotExist as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_404_NOT_FOUND
            )
