import uuid

import pytz
from core.serializers import TimezoneConverterMixin
from django.conf import settings
from general.models import Setting
from rest_framework import serializers

from .models import CalendarEvent

DEFAULT_TIMEZONE = settings.TIME_ZONE


class CalendarEventSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    start = serializers.DateTimeField(required=True)
    end = serializers.DateTimeField(required=True)
    attendees = serializers.ListField(
        child=serializers.EmailField(), required=False
    )
    calendar_id = serializers.CharField(required=False, default="primary")
    timezone = serializers.CharField(required=False, default=DEFAULT_TIMEZONE)

    class Meta:
        model = CalendarEvent
        read_only_fields = ["id", "created_by"]
        exclude = ("is_deleted", "original_data")

    def validate_timezone(self, value):
        if value not in pytz.all_timezones:
            raise serializers.ValidationError(
                f"{value} is not a valid timezone."
            )
        return value

    def validate(self, data):
        attendees = data.pop("attendees", [])
        data["original_data"] = data.get("original_data", {})
        data["original_data"]["attendees"] = attendees

        user = self.context["request"].user
        user_setting = Setting.objects.filter(user=user).first()
        user_timezone = (
            user_setting.timezone if user_setting else DEFAULT_TIMEZONE
        )

        if user_timezone:
            try:
                tz = pytz.timezone(user_timezone)
                data["start"] = data["start"].astimezone(tz)
                data["end"] = data["end"].astimezone(tz)
            except pytz.UnknownTimeZoneError as e:
                raise serializers.ValidationError("Unknown Timezone") from e

        return data

    def create(self, validated_data):
        user = self.context["request"].user
        start = validated_data.get("start")
        end = validated_data.get("end")
        attendees = validated_data["original_data"].get("attendees", [])
        timezone = validated_data.get("timezone", DEFAULT_TIMEZONE)
        event_data = self.get_event_data(
            validated_data, start, end, attendees, timezone
        )

        calendar_id = validated_data.pop("calendar_id", "primary")

        try:
            google_event = self.context["view"].calendar_service.create_event(
                calendar_id, event_data
            )
            validated_data["google_event_id"] = google_event["id"]
            if "conferenceData" in google_event:
                validated_data["original_data"][
                    "conferenceData"
                ] = google_event["conferenceData"]
        except Exception as e:
            raise serializers.ValidationError(
                f"Failed to create event in Google Calendar: {e}"
            ) from e

        validated_data["created_by"] = user
        validated_data["calendar_id"] = calendar_id
        return super().create(validated_data)

    @staticmethod
    def get_event_data(validated_data, start, end, attendees, timezone):
        event_data = {
            "start": {
                "dateTime": start.strftime("%Y-%m-%dT%H:%M:%S"),
                "timeZone": timezone,
            },
            "end": {
                "dateTime": end.strftime("%Y-%m-%dT%H:%M:%S"),
                "timeZone": timezone,
            },
            "attendees": [{"email": attendee} for attendee in attendees],
            "conferenceData": {
                "createRequest": {
                    "requestId": str(uuid.uuid4()),
                    "conferenceSolutionKey": {"type": "hangoutsMeet"},
                }
            },
        }

        if validated_data.get("summary"):
            event_data["summary"] = validated_data["summary"]

        if validated_data.get("description"):
            event_data["description"] = validated_data["description"]

        return event_data


class CalendarDetailEventSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    start = serializers.DateTimeField(required=True)
    end = serializers.DateTimeField(required=True)
    attendees = serializers.ListField(
        child=serializers.EmailField(), required=False
    )
    calendar_id = serializers.CharField(required=True)
    timezone = serializers.CharField(required=True)

    class Meta:
        model = CalendarEvent
        read_only_fields = ["id", "created_by"]
        exclude = ("is_deleted",)

    def validate_timezone(self, value):
        if value not in pytz.all_timezones:
            raise serializers.ValidationError(
                f"{value} is not a valid timezone."
            )
        return value

    def validate(self, data):
        attendees = data.pop("attendees", [])
        data["original_data"] = data.get("original_data", {})
        data["original_data"]["attendees"] = attendees

        user = self.context["request"].user
        user_setting = Setting.objects.filter(user=user).first()
        user_timezone = (
            user_setting.timezone if user_setting else DEFAULT_TIMEZONE
        )

        if user_timezone:
            try:
                tz = pytz.timezone(user_timezone)
                data["start"] = data["start"].astimezone(tz)
                data["end"] = data["end"].astimezone(tz)
            except pytz.UnknownTimeZoneError as e:
                raise serializers.ValidationError("Unknown Timezone") from e

        return data

    def update(self, instance, validated_data):
        instance = super().update(instance, validated_data)
        start = validated_data.get("start")
        end = validated_data.get("end")
        attendees = validated_data["original_data"].get("attendees", [])
        timezone = validated_data.get("timezone", DEFAULT_TIMEZONE)
        event_data = CalendarEventSerializer.get_event_data(
            validated_data, start, end, attendees, timezone
        )

        calendar_id = validated_data.pop("calendar_id")

        try:
            self.context["view"].calendar_service.update_event(
                calendar_id, instance.google_event_id, event_data
            )
        except Exception as e:
            raise serializers.ValidationError(
                f"Failed to update event in Google Calendar: {e}"
            ) from e

        return instance


class CalendarSerializer(serializers.Serializer):
    id = serializers.CharField()
    summary = serializers.CharField()
