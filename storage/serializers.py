from typing import List

from accounts.models import User
from accounts.serializers import UserSerializer
from core.serializers import TimezoneConverterMixin
from django.conf import settings
from django.shortcuts import get_object_or_404
from general.serializers import InFavoriteSerializerMixin
from resources.models import Tag
from resources.serializers import TagSerializer
from rest_framework import serializers
from storage.services import FileDirectUploadService
from storage.tasks import generate_thumbnail
from typing_extensions import TypedDict
from utils.aws.client import create_presigned_url

from .models import File
from .models import FileExtension
from .tasks import delete_file_from_s3


class StorageValidatedData(TypedDict):
    file_name: str
    file_type: str
    upload_finished_at: str
    expire_at: str
    reminder: str
    tags_name: List[str]


class FileSerializer(
    TimezoneConverterMixin,
    InFavoriteSerializerMixin,
    serializers.ModelSerializer,
):
    tags = serializers.SerializerMethodField()
    file = serializers.SerializerMethodField()
    tag_names = serializers.ListField(
        child=serializers.CharField(), write_only=True, required=False
    )
    thumbnail = serializers.SerializerMethodField()
    download_url = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    uploaded_by = UserSerializer(read_only=True)

    class Meta:
        model = File
        exclude = ("is_deleted",)
        read_only_fields = (
            "id",
            "upload_finished_at",
            "uploaded_by",
        )

    def update(self, instance: File, validated_data: dict):
        user: User = self.context["request"].user
        tag_names: List[str] = validated_data.pop("tag_names", [])
        tags = Tag.objects.create_tags(tag_names, user)
        validated_data["tags"] = tags

        # Remove file_name and file_type from validated_data
        validated_data.pop("file_name", None)
        validated_data.pop("file_type", None)

        return super().update(instance, validated_data)

    def get_tags(self, obj: File):
        return TagSerializer(obj.tags.all(), many=True).data

    def get_file(self, obj: File):
        if settings.IS_USING_LOCAL_STORAGE:
            try:
                return obj.file.url if obj.file else None
            except Exception:
                return None

        return create_presigned_url(obj.file.name)

    def get_thumbnail(self, obj: File):

        if obj.thumbnail_url:
            return obj.thumbnail_url

        if obj.thumbnail:
            try:
                return obj.thumbnail.url
            except Exception:
                return None

        extension = obj.file_type

        file_extension = FileExtension.objects.filter(
            extensions__contains=[extension]
        ).first()

        if not file_extension:
            file_extension = FileExtension.objects.filter(
                name__iexact=FileExtension.FILE_CATEGORY.OTHER
            ).first()

        if file_extension and file_extension.defualt_thumbnail:

            return file_extension.defualt_thumbnail.url

        return None

    def get_download_url(self, obj: File):
        ONE_HOUR = 60 * 60 * 60
        return create_presigned_url(
            obj.file.name, allow_download=True, presigned_expiry=ONE_HOUR
        )

    def get_file_size(self, obj: File):
        return obj.get_file_size()


class FileListSerializer(
    TimezoneConverterMixin,
    InFavoriteSerializerMixin,
    serializers.ModelSerializer,
):
    """Serializer for listing files without generating presigned URLs."""

    thumbnail = serializers.CharField(source="thumbnail_url")

    class Meta:
        model = File
        exclude = ("is_deleted",)


class DownloadFileSerializer(serializers.ModelSerializer):
    download_url = serializers.SerializerMethodField()

    class Meta:
        model = File
        fields = ["download_url"]

    def get_download_url(self, obj: File):
        return create_presigned_url(
            obj.original_file_name, allow_download=True
        )


class StartDirectFileUploadSerializer(serializers.Serializer):
    file = serializers.SerializerMethodField()
    presigned_data = serializers.DictField(read_only=True)

    file_name = serializers.CharField(write_only=True)
    file_type = serializers.CharField(write_only=True)
    reminder = serializers.DateTimeField(write_only=True, required=False)
    expire_at = serializers.DateTimeField(write_only=True, required=False)
    tag_names = serializers.ListField(
        child=serializers.CharField(), write_only=True, required=False
    )

    def create(self, validated_data: StorageValidatedData):
        user = self.context["request"].user
        validated_data["user"] = user
        service = FileDirectUploadService(user)
        data = service.start(validated_data)

        return data

    def get_file(self, obj: File):
        file = obj["file"]
        if not file:
            return
        return FileSerializer(file).data


class FinishFileUploadSerializer(serializers.Serializer):
    file_id = serializers.CharField(write_only=True)
    file = serializers.SerializerMethodField()

    def create(self, validated_data):
        user = self.context["request"].user
        file_id = validated_data["file_id"]

        file = get_object_or_404(File, id=file_id)

        service = FileDirectUploadService(user)
        file = service.finish(file=file)
        return {"file": file, "file_id": file_id}

    def get_file(self, obj: File):
        file = obj["file"]
        if not file:
            return
        return FileSerializer(file).data


class StorageSummarySerializer(serializers.Serializer):
    total_storage = serializers.IntegerField(read_only=True)
    total_storage_by_file_type = serializers.DictField(read_only=True)
    total_number_of_files = serializers.IntegerField(read_only=True)
    available_storage = serializers.IntegerField(read_only=True)


class UpdateFileOnlySerializer(serializers.ModelSerializer):
    file = serializers.FileField(
        required=True, allow_null=False, help_text="File to upload"
    )

    class Meta:
        model = File
        fields = ["file"]

    def update(self, instance: File, validated_data: dict):

        old_file = instance.file.name if instance.file else None
        old_thumbnail = instance.thumbnail.name if instance.thumbnail else None

        uploaded_file = validated_data.get("file")

        instance.file = None

        instance.file_type = uploaded_file.content_type or ""
        user = self.context["request"].user
        instance.uploaded_by = user

        service = FileDirectUploadService(user)

        service.upload_local(file=instance, file_obj=uploaded_file)

        service.finish(file=instance)

        instance.file.name = (
            f"{settings.PRIVATE_MEDIA_LOCATION}{instance.file.name}"
        )

        instance.save()

        generate_thumbnail.delay(instance.id)

        if old_file:
            delete_file_from_s3.delay(old_file)
        if old_thumbnail:
            delete_file_from_s3.delay(old_thumbnail)

        return instance

    def to_representation(self, instance):
        return FileSerializer(instance).data
