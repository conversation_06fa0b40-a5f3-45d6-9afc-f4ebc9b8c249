import io
import logging
import os
import subprocess
import tempfile
from pathlib import Path

import fitz  # PyMuPDF
from celery import shared_task
from pdf2image import convert_from_bytes
from pdf2image import convert_from_path
from PIL import Image
from PIL import ImageDraw
from storage.models import File
from utils.aws.client import delete_file
from utils.aws.client import get_file_content
from utils.aws.client import get_file_path
from utils.aws.client import upload_fileobj

logger = logging.getLogger(__name__)


class ThumbnailGeneratorService:
    def __init__(self, size=(300, 400)):
        self.size = size
        self.libreoffice = self.find_libreoffice()
        os.environ["PATH"] = f"/usr/local/bin:{os.environ['PATH']}"

    def generate_thumbnail(self, file: File) -> bool:

        if not file.upload_finished_at:
            return False

        methods = [
            self.pdf_thumbnail,
            self.image_thumbnail,
            self.document_thumbnail,
        ]

        for method in methods:
            try:
                if method(file):
                    return True
            except Exception as e:
                logger.warning(
                    f"Thumbnail method {method.__name__} failed: {str(e)}"
                )

        return False  # Should never reach here due to generic fallback

    def pdf_thumbnail(self, file: File) -> bool:
        try:
            file_content = get_file_content(file.file.name)
            if not file_content:
                return False

            try:
                with fitz.open(stream=file_content) as pdf:
                    if pdf.page_count > 0:
                        page = pdf.load_page(0)
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
                        img = Image.frombytes(
                            "RGB", [pix.width, pix.height], pix.samples
                        )
                        return self.save_thumbnail_image(file, img)
            except Exception as e:
                logger.info(f"PyMuPDF failed, trying fallback: {str(e)}")

            images = convert_from_bytes(
                file_content, first_page=1, last_page=1
            )
            if images:
                return self.save_thumbnail_image(file, images[0])

        except Exception as e:
            logger.warning(f"PDF thumbnail attempt failed: {str(e)}")

        return False

    def image_thumbnail(self, file: File) -> bool:
        try:
            file_content = get_file_content(file.file.name)
            if not file_content:
                return False

            img = Image.open(io.BytesIO(file_content))
            if img.mode == "RGBA":
                img = img.convert("RGB")
            return self.save_thumbnail_image(file, img)

        except Exception as e:
            logger.info(f"Image thumbnail attempt failed: {str(e)}")
            return False

    def document_thumbnail(self, file: File) -> bool:
        if not os.path.exists(self.libreoffice):
            logger.warning("LibreOffice not available for document conversion")
            return False

        try:
            content = get_file_content(file.file.name)
            if not content:
                return False

            with tempfile.TemporaryDirectory() as tmpdir:

                doc_path = os.path.join(tmpdir, "document")
                with open(doc_path, "wb") as f:
                    f.write(content)

                # Convert to PDF
                result = subprocess.run(
                    [
                        self.libreoffice,
                        "--headless",
                        "--convert-to",
                        "pdf",
                        "--outdir",
                        tmpdir,
                        doc_path,
                    ],
                    capture_output=True,
                    timeout=120,
                )

                if result.returncode != 0:
                    logger.error(
                        f"LibreOffice failed: {result.stderr.decode()}"
                    )
                    return False

                # Find generated PDF
                pdf_path = next(
                    (f for f in os.listdir(tmpdir) if f.endswith(".pdf")), None
                )
                if not pdf_path:
                    return False

                # Convert PDF to image
                images = convert_from_path(
                    os.path.join(tmpdir, pdf_path), first_page=1, last_page=1
                )
                if images:
                    return self.save_thumbnail_image(file, images[0])

        except Exception as e:
            logger.warning(f"Document thumbnail attempt failed: {str(e)}")

        return False

    def save_thumbnail_image(self, file: File, img: Image) -> bool:
        try:
            img.thumbnail(self.size)
            buffer = io.BytesIO()
            img.save(buffer, format="JPEG", quality=85)
            buffer.seek(0)

            name = Path(file.original_file_name or file.file.name).stem
            thumbnail_s3_path = (
                f"thumbnails/{file.id}_{name[:50]}_thumbnail.jpg"
            )

            success = upload_fileobj(
                buffer, thumbnail_s3_path, content_type="image/webp"
            )

            file.thumbnail_url = get_file_path(thumbnail_s3_path)
            file.save(update_fields=["thumbnail_url"])

            return success
        except Exception as e:
            logger.error(f"Failed to save thumbnail image: {str(e)}")
            return False

    def save_generic_thumbnail(self, file: File) -> bool:
        try:
            filename = file.original_file_name or file.file.name
            extension = Path(filename).suffix.lower()
            if not extension:
                extension = "file"
            else:
                extension = extension[1:]

            img = Image.new("RGB", self.size, color=(240, 240, 240))
            draw = ImageDraw.Draw(img)

            draw.text(
                (self.size[0] // 2, self.size[1] // 2),
                f"{extension} thumbnail",
                fill=(0, 0, 0),
            )

            return self.save_thumbnail_image(file, img)
        except Exception as e:
            logger.critical(f"Generic thumbnail failed! {str(e)}")
            return False

    @shared_task
    def generate_thumbnail_task(self, file_id):
        """Celery task to generate a thumbnail for a specific file"""
        try:
            file = File.objects.get(id=file_id)
            return self.generate_thumbnail(file)
        except File.DoesNotExist:
            logger.error(f"File with ID {file_id} not found")
            return False
        except Exception as e:
            logger.error(
                f"Error in generate_thumbnail_task for file ID {file_id}: {str(e)}",
                exc_info=True,
            )
            return False

    @shared_task
    def generate_all_thumbnails(self):
        files_without_thumbnails = File.objects.filter(
            upload_finished_at__isnull=False, thumbnail=""
        )
        count = files_without_thumbnails.count()
        logger.info(f"Found {count} files without thumbnails")

        for file in files_without_thumbnails:
            self.generate_thumbnail_task.delay(file.id)

        return count

    def retry_failed_thumbnails(self):

        try:

            files_without_thumbnails = File.objects.filter(
                upload_finished_at__isnull=False, thumbnail=""
            )

            count = files_without_thumbnails.count()

            if count == 0:

                return 0

            logger.info(
                f"Found {count} files without thumbnails, queuing for retry"
            )

            queued_count = 0
            for file in files_without_thumbnails:
                file: File
                try:
                    self.generate_thumbnail(file)
                    queued_count += 1
                    logger.debug(
                        f"Queued thumbnail generation for file {file.id}: {file.original_file_name}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to queue thumbnail generation for file {file.id}: {str(e)}"
                    )

            return queued_count

        except Exception as e:
            logger.error(
                f"Error in retry_failed_thumbnails task: {str(e)}",
                exc_info=True,
            )
            return 0

    def find_libreoffice(self):
        """Find LibreOffice executable path"""
        common_paths = [
            "/usr/bin/soffice",  # Standard Linux/Docker
            "/usr/local/bin/soffice",  # macOS
            "/opt/libreoffice/program/soffice",  # Another possible location
        ]

        for path in common_paths:
            if os.path.exists(path):
                logger.info(f"Found LibreOffice at: {path}")
                return path

        # Default fallback

        logger.warning(
            "LibreOffice not found in common locations, using default path"
        )

        return "/usr/bin/soffice"

    def regenerate_thumbnail(self, file: File) -> bool:

        try:
            # Check if file has upload finished
            if not file.upload_finished_at:
                logger.warning(
                    f"File {file.id} upload not finished, cannot regenerate thumbnail"
                )
                return False

            # Delete old thumbnail from AWS if it exists
            if file.thumbnail and file.thumbnail.name:
                try:
                    delete_success = delete_file(file.thumbnail.name)
                    if delete_success:
                        logger.info(
                            f"Successfully deleted old thumbnail: {file.thumbnail.name}"
                        )
                    else:
                        logger.warning(
                            f"Failed to delete old thumbnail: {file.thumbnail.name}"
                        )
                except Exception as e:
                    logger.warning(
                        f"Error deleting old thumbnail {file.thumbnail.name}: {str(e)}"
                    )

            file.thumbnail.delete(save=False)

            # Generate new thumbnail using existing method
            success = self.generate_thumbnail(file)

            if success:
                logger.info(
                    f"Successfully regenerated thumbnail for file {file.id}: {file.original_file_name}"
                )
            else:
                logger.error(
                    f"Failed to regenerate thumbnail for file {file.id}: {file.original_file_name}"
                )

            return success

        except Exception as e:
            logger.error(
                f"Error in regenerate_thumbnail for file {file.id}: {str(e)}",
                exc_info=True,
            )
            return False
