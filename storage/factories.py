import factory

from .models import File
from .models import FileExtension


class FileExtensionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = FileExtension

    name = FileExtension.FILE_CATEGORY.OTHER

    extensions = factory.List(
        [
            factory.Faker(
                "random_element", elements=FileExtension.FILE_CATEGORY.ALL
            )
        ]
    )
    defualt_thumbnail = factory.django.FileField(filename="test_file.txt")


class FileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = File

    file_name = factory.Faker("text")
    file_type = factory.Faker("text")
    original_file_name = factory.Faker("text")
    uploaded_by = factory.SubFactory("accounts.factories.UserFactory")
    expire_at = factory.Faker("date")
    reminder = factory.Faker("date")
    upload_finished_at = factory.Faker("date")

    @factory.post_generation
    def tags(self, create, extracted, **kwargs):
        if not create or not extracted:
            # Simple build, or nothing to add, do nothing.
            return

        # Add the iterable of groups using bulk addition
        self.tags.add(*extracted)
