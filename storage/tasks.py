import logging

import fitz  # PyMuPDF
from celery import shared_task
from core.dependency_injection import service_locator
from storage.models import File
from utils.aws.client import delete_file

logger = logging.getLogger(__name__)


@shared_task
def generate_thumbnail(file_id):
    file: File = File.objects.get(id=file_id)

    if not file.upload_finished_at:
        return  # File upload not finished, don't generate thumbnail yet

    service_locator.thumbnail_generator_service.generate_thumbnail(file)


@shared_task
def delete_file_from_s3(file_name):
    return delete_file(file_name)


@shared_task
def retry_failed_thumbnails():

    service_locator.thumbnail_generator_service.retry_failed_thumbnails()


@shared_task
def regenerate_all_thumbnails():
    files_with_thumbnails = File.objects.filter(
        upload_finished_at__isnull=False
    ).exclude(thumbnail="")

    count = files_with_thumbnails.count()
    logger.info(f"Found {count} files with thumbnails to regenerate")

    try:
        processed_count = 0
        for file in files_with_thumbnails:
            if service_locator.thumbnail_generator_service.regenerate_thumbnail(
                file
            ):
                processed_count += 1
                logger.debug(
                    f"Regenerated thumbnail for file {file.id}: {file.original_file_name}"
                )
            else:
                logger.warning(
                    f"Failed to regenerate thumbnail for file {file.id}: {file.original_file_name}"
                )

        logger.info(
            f"Successfully regenerated {processed_count} out of {count} thumbnails"
        )
        return processed_count
    except Exception as e:
        logger.error(
            f"Error in regenerate_all_thumbnails: {str(e)}",
            exc_info=True,
        )
        return 0
