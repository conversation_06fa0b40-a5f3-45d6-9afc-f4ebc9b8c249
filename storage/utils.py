import pathlib
import typing
from uuid import uuid4

from django.conf import settings
from django.urls import reverse

if typing.TYPE_CHECKING:
    from storage.models import File


def file_generate_name(original_file_name):
    extension = pathlib.Path(original_file_name).suffix

    return f"{uuid4().hex}{extension}"


def file_generate_upload_path(instance: "File", filename):
    return f"{instance.file.storage.location}files/{instance.file_name}"


def file_generate_local_upload_url(*, file_id: str):
    url = reverse("storage:direct_local_upload", kwargs={"file_id": file_id})

    app_domain: str = settings.APP_DOMAIN  # type: ignore

    return f"{app_domain}{url}"


def bytes_to_mib(value: int) -> float:
    # 1 bytes = 9.5367431640625E-7 mebibytes
    return value * 9.5367431640625e-7
