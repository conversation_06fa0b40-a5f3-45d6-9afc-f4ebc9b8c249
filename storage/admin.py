# Register your models here.
from django.contrib import admin
from django_better_admin_arrayfield.admin.mixins import DynamicArrayMixin
from general.admin import ImportExportModelAdminMixin

from . import models


@admin.action(description="Send expiration notification to user")
def send_expiration_notification(modeladmin, request, queryset):
    from notifications.tasks import notify_user_about_document_expiration

    for file in queryset:
        notify_user_about_document_expiration.delay(file.id)


@admin.action(description="Regenerate All Thumbnails For All Valid Files")
def regenerate_all_thumbnails(modeladmin, request, queryset):
    from storage.tasks import regenerate_all_thumbnails

    return regenerate_all_thumbnails.delay()


@admin.action(description="Generate thumbnail for all")
def generate_thumbnail(modeladmin, request, queryset):
    from storage.tasks import generate_thumbnail

    for file in models.File.objects.all():
        generate_thumbnail.delay(file.id)


@admin.action(description="Generate  thumbnail for selected items")
def generate_thumbnail_for_selected_items(modeladmin, request, queryset):
    from storage.tasks import generate_thumbnail

    for file in queryset:
        generate_thumbnail.delay(file.id)


@admin.action(description="Retry Failed Thumbnails")
def retry_failed_thumbnails_generation(modeladmin, request, queryset):
    from storage.tasks import retry_failed_thumbnails

    retry_failed_thumbnails.delay()


@admin.register(models.File)
class FileAdmin(ImportExportModelAdminMixin):
    list_display = [
        "original_file_name",
        "file_name",
        "file_type",
        "uploaded_by",
        "expire_at",
        "reminder",
        "upload_finished_at",
    ]
    search_fields = ["file_name", "original_file_name", "uploaded_by__email"]
    list_filter = [
        "file_type",
        "expire_at",
        "reminder",
        "uploaded_by",
        "is_deleted",
    ]

    actions = [
        send_expiration_notification,
        generate_thumbnail,
        generate_thumbnail_for_selected_items,
        retry_failed_thumbnails_generation,
        regenerate_all_thumbnails,
    ]
    readonly_fields = ["file_size"]


@admin.register(models.FileExtension)
class FileExtensionAdmin(ImportExportModelAdminMixin, DynamicArrayMixin):
    list_display = ["name", "extensions", "defualt_thumbnail"]
    list_filter = ["name"]
