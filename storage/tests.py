# Create your tests here.
from accounts.factories import UserFactory
from django.urls import reverse
from freezegun import freeze_time
from resources.factories import TagFactory
from rest_framework import status
from rest_framework.response import Response
from testing.base import BaseAPITest

from .factories import FileFactory
from .models import File


class StorageTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.tag = TagFactory(user=self.user)
        self.file = FileFactory(uploaded_by=self.user, tags=[self.tag])

        self.list_file_url = reverse(
            "storage:list",
        )

        self.expired_files_url = reverse(
            "storage:expired-files",
        )

        self.update_file_url = reverse(
            "storage:update",
            kwargs={
                "pk": str(self.file.id),
            },
        )

        self.direct_upload_start_url = reverse("storage:direct_upload_start")
        self.direct_upload_finish_url = reverse("storage:direct_upload_finish")
        super().setUp()

    def test_unauthenticated_user_interracting_with_file(self):
        #  Given an ananymous user
        #  When I try to get my StorageFile
        res: Response = self.client.get(self.list_file_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        payload = {
            "fileName": "string",
            "fileType": "string",
            "reminder": "2023-05-22T11:50:09.831Z",
            "expireAt": "2023-05-22T11:50:09.831Z",
            "tagNames": ["string"],
        }

        # And When I try to create a new Resource
        res: Response = self.client.post(self.direct_upload_start_url, payload)
        # Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an ananymous user
        #  When I try to update my StorageFile
        res: Response = self.client.patch(self.update_file_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        #  Given an anaonymous user
        #  When I try to delete my StorageFile
        res: Response = self.client.delete(self.update_file_url)
        #  Then I should get a 401
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_user_interracting_with_files(self):
        # Given an authenticated user
        self.client.force_authenticate(user=self.user)

        # When I try to get my StorageFile
        res: Response = self.client.get(self.list_file_url)
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # When I try to get my expired StorageFile
        res: Response = self.client.get(self.expired_files_url)
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        payload = {
            "file_name": "string",
            "file_type": "string",
            "reminder": "2023-05-22T11:50:09.831",
            "expire_at": "2023-05-22T11:50:09.831",
            "tag_names": ["string"],
        }

        # When I try to create a new Resource
        res: Response = self.client.post(self.direct_upload_start_url, payload)
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)
        self.assertEqual(res.data["file"]["upload_finished_at"], None)

        # When I try to mark file upload as finish
        fileId = res.data["file"]["id"]
        res: Response = self.client.post(
            self.direct_upload_finish_url, {"file_id": fileId}
        )
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)
        self.assertNotEqual(res.data["file"]["upload_finished_at"], None)

        # When I try to update my StorageFile
        self.file.file_name
        self.file.file_type
        new_tag_name = ["new_tag", "same_tag"]
        new_expire_at = "2023-05-22T11:50:09"
        new_reminder = "2023-05-22T11:50:09"
        new_original_file_name = "new_original_file_name"
        res: Response = self.client.patch(
            self.update_file_url,
            {
                "tag_names": new_tag_name,
                "file_name": "new_file_name",
                "file_type": "new_file_type",
                "original_file_name": new_original_file_name,
                "reminder": new_reminder,
                "expire_at": new_expire_at,
            },
        )
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # Normalizing the datetime values (removing timezone if present)
        expire_at_response = res.data["expire_at"].replace("Z", "")
        reminder_response = res.data["reminder"].replace("Z", "")

        # Validating the results
        self.assertEqual(expire_at_response, new_expire_at)
        self.assertEqual(reminder_response, new_reminder)

        self.assertEqual(
            res.data["original_file_name"], new_original_file_name
        )

        # When I try to delete my StorageFile
        res: Response = self.client.delete(self.update_file_url)
        self.assertEqual(res.status_code, status.HTTP_204_NO_CONTENT)

        # When I try to get a File that not my File
        res: Response = self.client.get(self.update_file_url)
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)

    def test_upload_file_without_expiry_and_reminder(self):
        #  Given an authenticated user
        self.client.force_authenticate(user=self.user)
        #  When I try to get my StorageFile
        res: Response = self.client.get(self.list_file_url)
        #  Then I should get a 200
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        payload = {
            "file_name": "string",
            "file_type": "string",
        }

        # And When I try to create a new Resource
        res: Response = self.client.post(
            self.direct_upload_start_url,
            payload,
        )

        # Then I should get a 201
        self.assertEqual(res.status_code, status.HTTP_201_CREATED)
        self.assertEqual(res.data["file"]["upload_finished_at"], None)

    def test_authenticated_user_download_file(self):
        # GIVEN an authenticated user
        self.client.force_authenticate(user=self.user)

        # Create download URL for the file
        download_url = reverse(
            "storage:download_file",
            kwargs={
                "pk": str(self.file.id),
            },
        )

        # WHEN I try to download my file
        res: Response = self.client.get(download_url)

        # THEN I should get a 200 OK response
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # AND the response should contain the download URL
        self.assertIn("download_url", res.data)

        # WHEN I try to download a file that doesn't exist
        non_existent_file_url = reverse(
            "storage:download_file",
            kwargs={
                "pk": "00000000-0000-0000-0000-000000000000",
            },
        )
        res: Response = self.client.get(non_existent_file_url)

        # THEN I should get a 404 Not Found
        self.assertEqual(res.status_code, status.HTTP_404_NOT_FOUND)

    def test_unauthenticated_user_download_file(self):
        # GIVEN an unauthenticated user
        download_url = reverse(
            "storage:download_file",
            kwargs={
                "pk": str(self.file.id),
            },
        )

        # WHEN I try to download a file
        res: Response = self.client.get(download_url)

        # THEN I should get a 401 Unauthorized response
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)


class FileModelTest(BaseAPITest):
    @freeze_time("2023-01-01")
    def test_get_files_that_need_to_remind_today(self):
        FileFactory(reminder="2023-01-01T00:00:00Z")
        FileFactory(reminder="2023-01-02T00:00:00Z")

        files = File.objects.get_files_that_need_to_remind_today()

        self.assertEqual(files.count(), 1)

    @freeze_time("2023-01-01")
    def test_get_files_that_expire_today(self):
        FileFactory(expire_at="2023-01-01T00:00:00Z")
        FileFactory(expire_at="2023-01-02T00:00:00Z")

        files = File.objects.get_files_that_expire_today()

        self.assertEqual(files.count(), 1)
