from django.urls import path
from storage.views_v2 import GetAllFileViewV2

from .views import DownloadFileView
from .views import ExpiredFileListView
from .views import FileDirectUploadFinishApi
from .views import FileDirectUploadLocalApi
from .views import FileDirectUploadStartApi
from .views import FileUpdateView
from .views import UpdateFileOnlyView

app_name = "storage_v2"
urlpatterns = [
    path(
        "upload/direct/start/",
        FileDirectUploadStartApi.as_view(),
        name="direct_upload_start",
    ),
    path(
        "upload/direct/finish/",
        FileDirectUploadFinishApi.as_view(),
        name="direct_upload_finish",
    ),
    path(
        "upload/direct/local/<str:file_id>/",
        FileDirectUploadLocalApi.as_view(),
        name="direct_local_upload",
    ),
    path("all/", GetAllFileViewV2.as_view(), name="list"),
    path("files/<str:pk>/", FileUpdateView.as_view(), name="update"),
    path(
        "files/<uuid:pk>/update/",
        UpdateFileOnlyView.as_view(),
        name="file_update",
    ),
    path(
        "files/<str:pk>/download/",
        DownloadFileView.as_view(),
        name="download_file",
    ),
    path(
        "expired-files/", ExpiredFileListView.as_view(), name="expired-files"
    ),
]
