import django_filters
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django_filters import FilterSet

from .models import File


class FileFilter(FilterSet):
    file_name = CharFilter(lookup_expr="icontains")
    original_file_name = CharFilter(lookup_expr="icontains")
    file_type = CharFilter(lookup_expr="icontains")
    tags = CharFilter(field_name="tags__name", lookup_expr="icontains")
    status = django_filters.ChoiceFilter(choices=File.Status.CHOICES)

    class Meta:
        model = File
        fields = [
            "file_name",
            "status",
            "original_file_name",
            "file_type",
            "tags",
        ]
