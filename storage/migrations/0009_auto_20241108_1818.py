# Generated by Django 3.2.17 on 2024-11-08 18:18

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('storage', '0008_fileextension'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fileextension',
            name='extensions',
            field=django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=1024), blank=True, default=list, null=True, size=200),
        ),
        migrations.AlterField(
            model_name='fileextension',
            name='name',
            field=models.Char<PERSON><PERSON>(max_length=200, unique=True),
        ),
    ]
