# Generated by Django 3.2.17 on 2024-10-10 20:07
import core.storage_backends
import storage.utils
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("storage", "0004_auto_20240128_1724"),
    ]

    operations = [
        migrations.AlterField(
            model_name="file",
            name="file",
            field=models.FileField(
                blank=True,
                null=True,
                storage=core.storage_backends.get_private_storage_class,
                upload_to=storage.utils.file_generate_upload_path,
            ),
        ),
    ]
