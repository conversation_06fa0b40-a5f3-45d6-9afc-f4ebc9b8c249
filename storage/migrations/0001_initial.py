# Generated by Django 3.2.17 on 2023-05-07 21:53
import uuid

import django.db.models.deletion
import storage.utils
from django.conf import settings
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("resources", "0002_alter_tag_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to=storage.utils.file_generate_upload_path,
                    ),
                ),
                ("original_file_name", models.TextField()),
                ("file_name", models.CharField(max_length=255, unique=True)),
                ("file_type", models.CharField(max_length=255)),
                (
                    "upload_finished_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("expire_at", models.DateTimeField()),
                ("reminder", models.DateTimeField()),
                ("tags", models.ManyToManyField(to="resources.Tag")),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
