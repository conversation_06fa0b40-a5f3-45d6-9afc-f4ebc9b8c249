# Generated by Django 3.2.17 on 2025-04-24 13:46
import core.storage_backends
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("storage", "0011_file_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="fileextension",
            name="defualt_thumbnail",
            field=models.ImageField(
                blank=True,
                null=True,
                storage=core.storage_backends.get_private_storage_class,
                upload_to="defualt_thumbnail/",
            ),
        ),
    ]
