# Generated by Django 3.2.17 on 2024-10-22 16:14

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('storage', '0007_file_file_size'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileExtension',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(choices=[('documents', 'Documents'), ('images', 'Images'), ('videos', 'Videos'), ('audio', 'Audio'), ('pdf', 'PDF'), ('other', 'Other')], max_length=200)),
                ('extensions', django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.Char<PERSON>ield(max_length=1024), default=list, size=200)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
