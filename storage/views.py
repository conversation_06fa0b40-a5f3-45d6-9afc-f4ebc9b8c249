from api.mixins import ApiAuthMixin
from company.models import Company
from core.constants import Features
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from notifications.mixins import NotificationCleanupMixin
from recent_app.mixins import CreateRecentActivityMixin
from rest_framework import filters
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.generics import ListAPIView
from rest_framework.generics import RetrieveAPIView
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.generics import UpdateAPIView
from rest_framework.parsers import FormParser
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from storage.serializers import DownloadFileSerializer
from storage.serializers import FileSerializer
from storage.serializers import FinishFileUploadSerializer
from storage.serializers import StartDirectFileUploadSerializer
from storage.serializers import UpdateFileOnlySerializer

from .filters import FileFilter
from .models import File
from .services import FileDirectUploadService
from .tasks import generate_thumbnail


class FileDirectUploadStartApi(ApiAuthMixin, CreateAPIView):
    serializer_class = StartDirectFileUploadSerializer


class GetAllFileView(ApiAuthMixin, ListAPIView):
    serializer_class = FileSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = FileFilter
    search_fields = [
        "file_name",
        "original_file_name",
        "file_type",
        "tags__name",
    ]

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return File.objects.get_user_files(current_company.user)


class ExpiredFileListView(ApiAuthMixin, ListAPIView):
    serializer_class = FileSerializer

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        # Get the queryset of all files uploaded by the user
        user_documents = File.objects.get_user_files(current_company.user)
        # Filter the queryset to get the expired documents
        return user_documents.filter(expire_at__lte=timezone.now())


class FileDirectUploadFinishApi(ApiAuthMixin, CreateAPIView):
    serializer_class = FinishFileUploadSerializer

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return File.objects.get_user_files(current_company.user)

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        file_id = response.data["file"]["id"]

        generate_thumbnail.delay(file_id)
        return response


class FileDirectUploadLocalApi(ApiAuthMixin, APIView):
    def post(self, request, file_id):
        file = get_object_or_404(File, id=file_id)
        file_obj = request.FILES["file"]

        current_company = Company.objects.get_user_current_company(
            request.user
        )
        service = FileDirectUploadService(current_company.user)
        file = service.upload_local(file=file, file_obj=file_obj)

        return Response({"id": file.id})


class DownloadFileView(ApiAuthMixin, RetrieveAPIView):
    serializer_class = DownloadFileSerializer

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return File.objects.get_user_files(current_company.user)


class FileUpdateView(
    NotificationCleanupMixin,
    CreateRecentActivityMixin,
    ApiAuthMixin,
    RetrieveUpdateDestroyAPIView,
):
    serializer_class = FileSerializer

    def get_queryset(self):
        current_company = Company.objects.get_user_current_company(
            self.request.user
        )
        return File.objects.get_user_files(current_company.user)

    def get_category(self):
        return Features.STORAGES

    def perform_update(self, serializer):
        instance = serializer.save()
        # Get updated fields from serializer
        updated_fields = serializer.validated_data.keys()
        self.cleanup_notifications(instance, updated_fields)

    def delete(self, request, *args, **kwargs):
        file = self.get_object()
        file.is_deleted = True
        file.save()
        return Response(
            {"message": "File deleted successfully."},
            status=status.HTTP_204_NO_CONTENT,
        )


class UpdateFileOnlyView(UpdateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = File.objects.all()
    parser_classes = [MultiPartParser, FormParser]
    serializer_class = UpdateFileOnlySerializer
    http_method_names = ["patch"]

    file_param = openapi.Parameter(
        name="file",
        in_=openapi.IN_FORM,
        description="File to upload",
        type=openapi.TYPE_FILE,
        required=True,
    )

    SWAGGER_AUTO_SCHEMA = {
        "manual_parameters": [file_param],
        "responses": {200: FileSerializer},
        "consumes": ["multipart/form-data"],
        "operation_description": "Upload a new file to replace the existing one",
    }

    @swagger_auto_schema(**SWAGGER_AUTO_SCHEMA)
    def patch(self, request, *args, **kwargs):

        return self.partial_update(request, *args, **kwargs)
