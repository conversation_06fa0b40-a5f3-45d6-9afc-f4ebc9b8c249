import typing

from accounts.models import User
from core.models import BaseModel
from core.storage_backends import get_private_storage_class
from django.conf import settings
from django.db import models
from django.utils import timezone
from django_better_admin_arrayfield.models.fields import ArrayField
from resources.models import Tag
from storage.enums import FileUploadStorage
from storage.utils import file_generate_upload_path


if typing.TYPE_CHECKING:
    from contacts.models import SubContractorEstimate
    from project.models import ProjectDocument


class FileExtension(BaseModel):
    class FILE_CATEGORY:
        DOCUMENTS = "documents"
        IMAGES = "images"
        VIDEOS = "videos"
        AUDIO = "audio"
        PDF = "pdf"
        OTHER = "other"

        ALL = [DOCUMENTS, IMAGES, VIDEOS, AUDIO, PDF, OTHER]

        CHOICES = [
            (DOCUMENTS, "Documents"),
            (IMAGES, "Images"),
            (VIDEOS, "Videos"),
            (AUDIO, "Audio"),
            (PDF, "PDF"),
            (OTHER, "Other"),
        ]

    name = models.CharField(max_length=200, unique=True)

    extensions = ArrayField(
        models.CharField(max_length=1024),
        null=True,
        blank=True,
        size=200,
        default=list,
    )
    defualt_thumbnail = models.ImageField(
        upload_to="defualt_thumbnail/",
        blank=True,
        null=True,
        storage=get_private_storage_class,
    )


class FileManger(models.Manager):
    def get_queryset(self) -> models.QuerySet["File"]:
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False)
            .prefetch_related("tags")
            .order_by("-created_at")
        )

    def get_user_files(self, user: User):
        return self.get_queryset().filter(
            uploaded_by=user, upload_finished_at__isnull=False
        )

    def get_files_that_expire_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_date_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            expire_at__range=[start_of_day, end_date_of_day]
        )

    def get_files_that_need_to_remind_today(self) -> models.QuerySet["File"]:
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_date_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            reminder__range=[start_of_day, end_date_of_day]
        )

    @property
    def get_document_type_name(self):
        file_extension_names = FileExtension.objects.values_list(
            "name", flat=True
        ).distinct()

        total_storage_by_document_type = {
            name.lower(): {"count": 0, "size": 0}
            for name in file_extension_names
        }

        total_storage_by_document_type.setdefault(
            FileExtension.FILE_CATEGORY.OTHER, {"count": 0, "size": 0}
        )

        return total_storage_by_document_type

    def get_storage_summary(self, user: User):

        all_files = self.get_user_files(user)
        total_storage_by_file_type = self.get_document_type_name
        total_storage = 0

        file_type_groups = self.get_file_type_groups()

        for file in all_files:
            size = file.get_file_size()
            total_storage += size
            self.categorize_file(
                file, size, file_type_groups, total_storage_by_file_type
            )

        return self.generate_storage_summary(
            all_files, total_storage, total_storage_by_file_type
        )

    def get_file_type_groups(self):

        file_extensions = FileExtension.objects.all()
        return {ext.name: ext.extensions for ext in file_extensions}

    def categorize_file(
        self, file: "File", size, file_type_groups, total_storage_by_file_type
    ):

        categorized = False
        for category, extensions in file_type_groups.items():
            if file.file_type.lower() in extensions:
                total_storage_by_file_type[category]["count"] += 1
                total_storage_by_file_type[category]["size"] += size
                categorized = True
                break

        if not categorized:
            total_storage_by_file_type[FileExtension.FILE_CATEGORY.OTHER][
                "count"
            ] += 1
            total_storage_by_file_type[FileExtension.FILE_CATEGORY.OTHER][
                "size"
            ] += size

    def generate_storage_summary(
        self, all_files, total_storage, total_storage_by_file_type
    ):

        return {
            "total_storage": total_storage,
            "total_storage_by_file_type": total_storage_by_file_type,
            "total_number_of_files": all_files.count(),
            "available_storage": 0,
        }

    def get_file_size_by_project(self, user: User):

        file_size_by_project = []

        for project in user.project_set.all():
            project_files = self.get_user_files(user).filter(
                project_documents__project=project
            )
            total_file_size = self.get_total_project_file_size(project_files)
            file_size_by_project.append(
                {"name": project.name, "size": total_file_size}
            )

        return file_size_by_project

    def get_total_project_file_size(self, project_files):

        total_file_size = 0
        for file in project_files:
            file: File
            total_file_size += file.get_file_size()
        return total_file_size

    def get_project_summary(self, user: User):

        project_summary = []

        for project in user.project_set.all():
            project_documents = project.documents.all()
            total_storage_by_file_type = self.get_document_type_name.copy()
            total_storage = self.get_total_project_storage(
                project_documents, total_storage_by_file_type
            )
            project_summary.append(
                {
                    "project_name": project.name,
                    "total_documents": project_documents.count(),
                    "total_storage": total_storage,
                    "document_type": total_storage_by_file_type,
                }
            )

        return project_summary

    def get_total_project_storage(
        self, project_documents, total_storage_by_file_type
    ):

        total_storage = 0

        for doc in project_documents:
            doc: "ProjectDocument"
            file_size = doc.file.get_file_size() if doc.file else 0
            total_storage += file_size
            self.categorize_document(
                doc, file_size, total_storage_by_file_type
            )

        return total_storage

    def categorize_document(
        self, doc: "ProjectDocument", file_size, total_storage_by_file_type
    ):

        file_type = doc.file.file_type.lower() if doc.file else None

        if file_type:
            categorized = False
            for category, extensions in FileExtension.objects.values_list(
                "name", "extensions"
            ):
                if file_type in extensions:
                    total_storage_by_file_type[category]["count"] += 1
                    total_storage_by_file_type[category]["size"] += file_size
                    categorized = True
                    break

            if not categorized:
                total_storage_by_file_type[FileExtension.FILE_CATEGORY.OTHER][
                    "count"
                ] += 1
                total_storage_by_file_type[FileExtension.FILE_CATEGORY.OTHER][
                    "size"
                ] += file_size
        else:
            total_storage_by_file_type[FileExtension.FILE_CATEGORY.OTHER][
                "count"
            ] += 1
            total_storage_by_file_type[FileExtension.FILE_CATEGORY.OTHER][
                "size"
            ] += file_size


class File(BaseModel):
    class Status:
        ACTIVE = "active"
        ARCHIVED = "archived"

        CHOICES = (
            (ACTIVE, "Active"),
            (ARCHIVED, "Archived"),
        )

    status = models.CharField(
        max_length=20, choices=Status.CHOICES, default=Status.ACTIVE
    )

    subcontractor_estimates: models.QuerySet["SubContractorEstimate"]
    project_documents: models.QuerySet["ProjectDocument"]

    objects: FileManger = FileManger()
    file = models.FileField(
        upload_to=file_generate_upload_path,
        blank=True,
        null=True,
        storage=get_private_storage_class,
    )
    thumbnail = models.ImageField(
        upload_to="thumbnails/",
        blank=True,
        null=True,
        storage=get_private_storage_class,
    )

    thumbnail_url = models.URLField(blank=True, null=True)
    original_file_name = models.TextField()

    file_name = models.CharField(max_length=1024, unique=True)
    file_type = models.CharField(max_length=255)

    # As a specific behavior,
    # We might want to preserve files after the uploader has been deleted.
    # In case you want to delete the files too, use models.CASCADE & drop the null=True
    uploaded_by = models.ForeignKey(User, null=True, on_delete=models.SET_NULL)
    upload_finished_at = models.DateTimeField(blank=True, null=True)
    expire_at = models.DateTimeField(blank=True, null=True)
    reminder = models.DateTimeField(blank=True, null=True)
    tags = models.ManyToManyField(Tag)
    file_size = models.BigIntegerField(default=0)

    @property
    def created_by(self):
        return self.uploaded_by

    def __str__(self) -> str:
        return f"{self.file_name}"

    @property
    def is_valid(self):
        """
        We consider a file "valid" if the the datetime flag has value.
        """
        return bool(self.upload_finished_at)

    @property
    def url(self):
        if settings.FILE_UPLOAD_STORAGE == FileUploadStorage.S3:
            return self.file.url

        return f"{settings.APP_DOMAIN}{self.file.url}"

    def get_file_size(self):
        try:
            if not self.file:
                return 0

            if self.file_size:
                return self.file_size

            if settings.FILE_UPLOAD_STORAGE == FileUploadStorage.LOCAL:
                return self.file.size

            s3_object = self.get_file_from_s3()

            if not s3_object:
                return 0

            self.file_size = s3_object.content_length
            self.save()

            return self.file_size

        except Exception:
            return 0

    def get_file_from_s3(self):
        if not self.file:
            return None

        if settings.FILE_UPLOAD_STORAGE == FileUploadStorage.S3:
            from utils.aws.client import s3_resource_client

            client = s3_resource_client()
            s3_object = None
            possible_paths = [
                self.file.name,
                f"{self.file.storage.location}{self.file.name}",
            ]

            for path in possible_paths:
                try:
                    s3_object = client.Object(
                        settings.AWS_STORAGE_BUCKET_NAME, path
                    )
                    s3_object.load()
                    return s3_object
                except Exception:
                    pass

        return None

    def archive(self):
        """Archive the document by setting its status to archived"""
        self.status = self.Status.ARCHIVED
        self.save(update_fields=["status"])
