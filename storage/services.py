import mimetypes
from typing import Any
from typing import Dict
from typing import List
from typing import Tu<PERSON>

from accounts.models import User
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from resources.models import Tag
from typing_extensions import TypedDict
from utils.aws.client import s3_generate_presigned_post

from .enums import FileUploadStorage
from .models import File
from .utils import bytes_to_mib
from .utils import file_generate_local_upload_url
from .utils import file_generate_name
from .utils import file_generate_upload_path


def _validate_file_size(file_obj):

    max_size = settings.FILE_MAX_SIZE

    if file_obj.size > max_size:
        raise ValidationError(
            f"File is too large. It should not exceed {bytes_to_mib(max_size)} MiB"
        )


class FileStandardUploadService:
    """
    This also serves as an example of a service class,
    which encapsulates 2 different behaviors (create & update) under a namespace.

    Meaning, we use the class here for:

    1. The namespace
    2. The ability to reuse `_infer_file_name_and_type` (which can also be an util)
    """

    def __init__(self, user: User, file_obj):
        self.user = user
        self.file_obj = file_obj

    def _infer_file_name_and_type(
        self, file_name: str = "", file_type: str = ""
    ) -> Tuple[str, str]:
        if not file_name:
            file_name = self.file_obj.name

        if not file_type:
            guessed_file_type, encoding = mimetypes.guess_type(file_name)

            if guessed_file_type is None:
                file_type = ""
            else:
                file_type = guessed_file_type

        return file_name, file_type

    @transaction.atomic
    def create(self, file_name: str = "", file_type: str = "") -> File:
        _validate_file_size(self.file_obj)

        file_name, file_type = self._infer_file_name_and_type(
            file_name, file_type
        )

        obj = File(
            file=self.file_obj,
            original_file_name=file_name,
            file_name=file_generate_name(file_name),
            file_type=file_type,
            uploaded_by=self.user,
            upload_finished_at=timezone.now(),
        )

        obj.full_clean()
        obj.save()

        return obj

    @transaction.atomic
    def update(
        self, file: File, file_name: str = "", file_type: str = ""
    ) -> File:
        _validate_file_size(self.file_obj)

        file_name, file_type = self._infer_file_name_and_type(
            file_name, file_type
        )

        file.file = self.file_obj
        file.original_file_name = file_name
        file.file_name = file_generate_name(file_name)
        file.file_type = file_type
        file.uploaded_by = self.user
        file.upload_finished_at = timezone.now()

        file.full_clean()
        file.save()

        return file


class StartFileUploadData(TypedDict):
    file: File
    presigned_data: Dict[str, Any]


class FileDirectUploadService:
    """
    This also serves as an example of a service class,
    which encapsulates a flow (start & finish) + one-off action (upload_local) into a namespace.

    Meaning, we use the class here for:

    1. The namespace
    """

    class StorageValidatedData(TypedDict):
        file_name: str
        file_type: str
        upload_finished_at: str
        expire_at: str
        reminder: str
        tag_names: List[str]
        user: User

    def __init__(self, user: User):
        self.user = user

    @transaction.atomic
    def start(self, data: StorageValidatedData) -> StartFileUploadData:
        user = data["user"]
        tags_name = data.get("tag_names", [])
        tags = Tag.objects.create_tags(tags_name, user)

        file = File(
            original_file_name=data["file_name"],
            file_name=file_generate_name(data["file_name"]),
            file_type=data["file_type"],
            uploaded_by=data["user"],
            expire_at=data.get("expire_at"),
            reminder=data.get("reminder"),
            file=None,
        )

        file.tags.set(tags)

        file.full_clean()
        file.save()

        upload_path = file_generate_upload_path(file, file.file_name)

        """
        We are doing this in order to have an associated file for the field.
        """
        file.file = file.file.field.attr_class(
            file, file.file.field, upload_path
        )
        file.save()

        presigned_data: Dict[str, Any] = {}

        if settings.FILE_UPLOAD_STORAGE == FileUploadStorage.S3:
            presigned_data = s3_generate_presigned_post(
                file_path=upload_path, file_type=file.file_type
            )

        else:
            presigned_data = {
                "url": file_generate_local_upload_url(file_id=str(file.id)),
            }

        return {
            "file": file,
            "presigned_data": presigned_data,
        }

    @transaction.atomic
    def finish(self, *, file: File) -> File:
        # Potentially, check against user
        file.upload_finished_at = timezone.now()
        file.full_clean()
        file.save()

        return file

    @transaction.atomic
    def upload_local(self, *, file: File, file_obj) -> File:
        _validate_file_size(file_obj)

        # Potentially, check against user
        file.file = file_obj
        file.full_clean()
        file.save()

        return file
