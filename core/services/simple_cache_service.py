import hashlib
import logging
from abc import ABC
from abc import abstractmethod
from typing import Any
from typing import Dict
from typing import Op<PERSON>
from typing import Tu<PERSON>

from crum import get_current_user
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class BaseCacheService(ABC):
    """
    Simplified caching service for list views.

    Much simpler than the previous implementation - just caches responses
    and provides reliable cache clearing.
    """

    @property
    def current_user(self):
        """Get current user ID as string."""
        user = get_current_user()
        if not user:
            return "anonymous"
        return str(user.id)

    @abstractmethod
    def get_cache_timeout_setting(self) -> str:
        """Return the settings attribute name for cache timeout."""

    @abstractmethod
    def get_cache_prefix(self) -> str:
        """Return the cache key prefix for this service."""

    def get_additional_cache_params(
        self, query_params: Optional[Dict] = None
    ) -> Dict:
        """
        Override in child classes to add service-specific parameters to cache key.

        Args:
            query_params: The original query parameters

        Returns:
            Dict of additional parameters to include in cache key
        """
        return {}

    def generate_cache_key(self, query_params: Optional[Dict] = None) -> str:
        """Generate cache key with consistent logic."""
        prefix = self.get_cache_prefix()
        user_id = self.current_user

        # Combine original query params with service-specific params
        all_params = {}
        if query_params:
            all_params.update(query_params)

        # Add service-specific parameters
        additional_params = self.get_additional_cache_params(query_params)
        if additional_params:
            all_params.update(additional_params)

        # Generate hash from all parameters
        if all_params:
            sorted_params = sorted(all_params.items())
            params_str = "&".join(f"{k}={v}" for k, v in sorted_params)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        else:
            params_hash = "default"

        return f"{prefix}_{user_id}_{params_hash}"

    def generate_cache_pattern(self) -> str:
        """Generate cache pattern for clearing cache."""
        return f"{self.get_cache_prefix()}_{self.current_user}_*"

    def get_cache_timeout(self) -> int:
        """Get cache timeout from settings."""
        setting_name = self.get_cache_timeout_setting()
        return getattr(
            settings, setting_name, 30 * 24 * 60 * 60
        )  # Default 30 days

    def cache_response(self, request, response_data: Any) -> None:
        """Cache response data."""
        try:
            cache_key = self.generate_cache_key(request.query_params)
            timeout = self.get_cache_timeout()
            cache.set(cache_key, response_data, timeout)
            logger.debug(
                f"Cached {self.get_cache_prefix()} response - key: {cache_key}"
            )
        except Exception as e:
            logger.warning(
                f"Failed to cache {self.get_cache_prefix()} response: {e}"
            )

    def get_cached_response(self, request) -> Optional[Any]:
        """Retrieve cached response data."""
        try:
            cache_key = self.generate_cache_key(request.query_params)
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                logger.debug(
                    f"Cache hit for {self.get_cache_prefix()} - key: {cache_key}"
                )
            return cached_data
        except Exception as e:
            logger.warning(
                f"Failed to retrieve {self.get_cache_prefix()} cache: {e}"
            )
            return None

    def get_cached_response_with_fallback(
        self, request
    ) -> Tuple[Optional[Any], str]:
        """
        Retrieve cached response with error handling.

        Returns:
            Tuple[Optional[Any], str]: (cached_data, status)
        """
        try:
            cached_data = self.get_cached_response(request)
            if cached_data is not None:
                return cached_data, "hit"
            else:
                return None, "miss"
        except Exception as e:
            logger.warning(f"Cache error for {self.get_cache_prefix()}: {e}")
            return None, "error"

    def clear_cache(self) -> None:
        """Clear all cache for this service and current user."""
        try:
            pattern = self.generate_cache_pattern()
            deleted_count = cache.delete_pattern(pattern)
            logger.debug(
                f"Cleared {self.get_cache_prefix()} cache - deleted {deleted_count} keys"
            )
        except Exception as e:
            logger.warning(
                f"Failed to clear {self.get_cache_prefix()} cache: {e}"
            )

    # Dynamic method resolution for backward compatibility
    def __getattr__(self, name):
        """Dynamic method resolution for entity-specific methods."""
        entity_type = self.get_cache_prefix()

        method_mappings = {
            f"cache_{entity_type}_response": self.cache_response,
            f"get_cached_{entity_type}_response": self.get_cached_response,
            f"get_cached_{entity_type}_response_with_fallback": self.get_cached_response_with_fallback,
            f"clear_{entity_type}_response_cache": self.clear_cache,
            f"clear_{entity_type}_cache": self.clear_cache,
            "clear_entity_response_cache": self.clear_cache,
        }

        if name in method_mappings:
            return method_mappings[name]

        raise AttributeError(
            f"'{self.__class__.__name__}' object has no attribute '{name}'"
        )
