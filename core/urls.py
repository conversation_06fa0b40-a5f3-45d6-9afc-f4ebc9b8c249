from core import views
from django.conf import settings
from django.contrib import admin
from django.urls import include
from django.urls import path
from django.urls import re_path
from drf_yasg import openapi
from drf_yasg.generators import OpenAPISchemaGenerator
from drf_yasg.views import get_schema_view
from rest_framework import permissions


class BothHttpAndHttpsSchemaGenerator(OpenAPISchemaGenerator):
    def get_schema(self, request=None, public=False):
        schema = super().get_schema(request, public)
        schema.schemes = ["http", "https"]
        return schema


# Create schema view for API v1
schema_view_v1 = get_schema_view(
    openapi.Info(
        title="Tuulbox core API",
        default_version="v1",
        description="Tuulbox core API v1",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.IsAuthenticated],
    generator_class=BothHttpAndHttpsSchemaGenerator,
    patterns=[path("api/", include("api.v1.urls"))],
)

# Create schema view for API v2
schema_view_v2 = get_schema_view(
    openapi.Info(
        title="Tuulbox core API",
        default_version="v2",
        description="Tuulbox core API v2",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.IsAuthenticated],
    generator_class=BothHttpAndHttpsSchemaGenerator,
    patterns=[path("api/v2/", include("api.v2.urls"))],
)

# For backward compatibility
schema_view = schema_view_v1


def trigger_error(request):
    1 / 0


urlpatterns = [
    # API v2 documentation
    re_path(
        r"^swagger/v2(?P<format>\.json|\.yaml)$",
        schema_view_v2.without_ui(cache_timeout=0),
        name="schema-json-v2",
    ),
    re_path(
        r"^swagger/v2/$",
        schema_view_v2.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui-v2",
    ),
    re_path(
        r"^redoc/v2/$",
        schema_view_v2.with_ui("redoc", cache_timeout=0),
        name="schema-redoc-v2",
    ),
    # Default documentation (v1 for backward compatibility)
    re_path(
        r"^swagger(?P<format>\.json|\.yaml)$",
        schema_view.without_ui(cache_timeout=0),
        name="schema-json",
    ),
    re_path(
        r"^swagger/$",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    re_path(
        r"^redoc/$",
        schema_view.with_ui("redoc", cache_timeout=0),
        name="schema-redoc",
    ),
    # Main URLs
    path("admin/", admin.site.urls),
    path("api/", include("api.urls")),
    path("sentry-debug/", trigger_error),
    # well-known
    path(".well-known/assetlinks.json", views.assetlinks_json),
    path(
        ".well-known/apple-app-site-association",
        views.apple_app_site_association,
    ),
]

# Add Silk URLs for profiling
if settings.DEBUG:
    urlpatterns += [
        path("silk/", include("silk.urls", namespace="silk")),
    ]
