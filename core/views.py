import os

from django.conf import settings
from django.http import HttpResponse


# assetlinks.json
def assetlinks_json(request):
    json_file = open(
        os.path.join(settings.BASE_DIR, "core", "static", "assetlinks.json")
    ).read()
    return HttpResponse(json_file, content_type="application/json")


def apple_app_site_association(request):
    json_file = open(
        os.path.join(
            settings.BASE_DIR,
            "core",
            "static",
            "apple-app-site-association.json",
        )
    ).read()
    return HttpResponse(json_file, content_type="application/json")
