import logging
from abc import ABC
from abc import abstractmethod

from core.dependency_injection import service_locator
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class BaseCacheViewMixin(ABC):
    """
    Simplified caching mixin for list views.
    """

    @abstractmethod
    def get_service_name(self) -> str:
        """Return the service name for dependency injection (e.g., 'contact_service')."""

    @abstractmethod
    def get_cache_entity_name(self) -> str:
        """Return the entity name for cache methods (e.g., 'contact', 'subcontractor')."""

    @abstractmethod
    def _get_fresh_queryset_impl(self, user):
        """Implement the actual queryset logic specific to each view."""

    def list(self, request, *args, **kwargs):
        """Override list method to implement response caching."""
        # Check if caching is disabled
        if request.query_params.get("disable_cache"):
            return super().list(request, *args, **kwargs)

        # Get the service dynamically
        service = getattr(service_locator, self.get_service_name())

        # Try to get cached response
        cached_response, _ = service.get_cached_response_with_fallback(request)

        if cached_response is not None:
            return Response(cached_response)

        # Cache miss - get fresh data and cache it
        logger.debug(f"Cache miss for {self.get_cache_entity_name()} list")
        response = super().list(request, *args, **kwargs)

        # Cache the response data if successful
        if response.status_code == 200 and response.data:
            service.cache_response(request, response.data)

        return response
