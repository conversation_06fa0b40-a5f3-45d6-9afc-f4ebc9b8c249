from django.conf import settings
from django.shortcuts import redirect


class SilkStaffAccessMiddleware:
    """
    Middleware to ensure only staff users can access the Silk interface.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if the request is for a Silk URL
        if request.path.startswith("/silk/"):
            # If user is not authenticated or not staff, redirect to login
            if not request.user.is_authenticated or not request.user.is_staff:
                login_url = settings.LOGIN_URL
                return redirect(f"{login_url}?next={request.path}")

        return self.get_response(request)
