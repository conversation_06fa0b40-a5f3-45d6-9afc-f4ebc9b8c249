import base64
import logging

import requests
from core.tasks import send_core_email
from django.conf import settings
from django.template.loader import render_to_string
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Attachment
from sendgrid.helpers.mail import Disposition
from sendgrid.helpers.mail import FileContent
from sendgrid.helpers.mail import FileName
from sendgrid.helpers.mail import FileType
from sendgrid.helpers.mail import Mail

logger = logging.getLogger(__name__)


class ZohoService:
    """Send the email using the ZohoService"""

    def send_email(
        self,
        subject="",
        body="",
        sender_email=settings.ZOHO_EMAIL,
        sender_name="<PERSON><PERSON><PERSON>",
        to_email=list,
    ):
        email_data = {
            "data": [
                {
                    "from": {"user_name": sender_name, "email": sender_email},
                    "to": [
                        {"user_name": to_name, "email": to_email}
                        for to_name, to_email in to_email
                    ],
                    "subject": subject,
                    "content": body,
                    "mail_format": "html",
                }
            ]
        }

        url = f"https://www.zohoapis.com/crm/v5/Leads/{settings.ZOHO_KEY}/actions/send_mail"
        headers = {
            "Authorization": f"Zoho-oauthtoken {settings.ZOHO_AUTH_TOKEN}"
        }

        try:
            response = requests.post(url, headers=headers, json=email_data)
            response.raise_for_status()
        except requests.RequestException as e:
            logger.error(f"Error sending email via Zoho: {e}")


class SendGridService:
    """Send the email using the SendGridService"""

    def send_email(self, subject, body, to_emails, attachments=None):
        message = Mail(
            from_email=settings.EMAIL_HOST_USER,
            to_emails=to_emails,
            subject=subject,
            html_content=body,
        )

        if attachments:
            for attachment in attachments:
                encoded_file = base64.b64encode(
                    attachment["file_content"]
                ).decode()
                attached_file = Attachment(
                    FileContent(encoded_file),
                    FileName(attachment["filename"]),
                    FileType("application/octet-stream"),
                    Disposition("attachment"),
                )
                message.add_attachment(attached_file)

        try:
            sg = SendGridAPIClient(settings.SENDGRID_KEY)
            sg.send(message)
        except Exception as e:
            logger.error(f"Error sending email via SendGrid: {e}")


class CoreService:
    def __init__(self, zoho_service=None, sendgrid_service=None) -> None:
        self.zoho_service = zoho_service
        self.sendgrid_service = sendgrid_service

    def send_email(
        self,
        subject,
        template_path,
        template_context,
        to_emails,
        attachments=None,
    ):
        """SEND EMAIL"""

        if settings.TEST_DEBUG:
            return

        if settings.DEBUG:
            print(f"Sending email to {to_emails}")
            return

        html_content = render_to_string(template_path, template_context)

        if self.zoho_service:
            self.zoho_service.send_email(
                subject=subject, body=html_content, to_email=to_emails
            )

        elif self.sendgrid_service:
            send_core_email.delay(
                subject=subject,
                template_path=template_path,
                template_context=template_context,
                to_emails=to_emails,
                attachments=attachments,
            )
