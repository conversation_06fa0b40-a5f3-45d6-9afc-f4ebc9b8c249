import json
from typing import Dict

from rest_framework.response import Response


def get_status_message(status_code: int) -> str:
    if status_code < 400:
        return "success"
    else:
        return "failed"


def format_response_data(data: Dict, status_code: int = None) -> Dict:
    return {
        "status": get_status_message(status_code) if status_code else "",
        "data": data,
    }
