import logging
from datetime import datetime

import pytz
from django.conf import settings
from django.utils import timezone
from general.models import Setting

logger = logging.getLogger(__name__)


class TimezoneConverterMixin:
    def convert_to_user_timezone(self, data, instance):
        # List of datetime keys to check for in the data
        datetime_keys = [
            "start",
            "end",
            "valid_to",
            "reminder",
            "created_at",
            "updated_at",
            "upload_finished_at",
            "expires_at",
            "last_gmail_sync",
        ]

        # Default timezone to the application's default setting
        user_timezone = settings.TIME_ZONE
        request = self.context.get("request")

        if not self.context.get("request"):
            # Just return the data without conversion if no request
            return data

        if request and request.user.is_authenticated:
            user = request.user

            user_timezone = self.get_or_set_user_timezone(user)

        # Ensure that a timezone is set
        if not user_timezone:
            user_timezone = settings.TIME_ZONE

        # Validate the timezone before setting it as user's timezone
        try:
            user_tz = pytz.timezone(user_timezone)
        except pytz.UnknownTimeZoneError:
            logger.error(
                f"Invalid timezone: {user_timezone}",
                extra={"instance": instance, "serializer": self},
            )
            user_tz = pytz.timezone(
                settings.TIME_ZONE
            )  # Fallback to default timezone if invalid

        # Convert all datetime fields to the user's timezone
        for key in datetime_keys:
            if key in data and data[key]:
                dt_str = data[key]
                dt = datetime.fromisoformat(
                    dt_str.rstrip("Z")
                )  # Remove the "Z" suffix if present
                dt = timezone.make_aware(
                    dt, pytz.UTC
                )  # Convert to UTC timezone first
                converted_dt = dt.astimezone(
                    user_tz
                )  # Convert to user's timezone
                # Remove the offset +HH:MM to store as naive datetime (without timezone)
                converted_dt = converted_dt.replace(tzinfo=None)
                # Convert the datetime to ISO format without timezone information
                data[key] = converted_dt.isoformat()

        return data

    def to_representation(self, instance):
        # Get the default serialized data
        data = super().to_representation(instance)
        # Convert the datetime fields in the data to the user's timezone
        data = self.convert_to_user_timezone(data, instance)
        return data

    def get_or_set_user_timezone(self, user):
        setting = Setting.objects.filter(user=user).first()

        if not setting:
            setting = Setting.objects.create(
                user=user, timezone=settings.TIME_ZONE
            )
        elif not setting.timezone:
            setting.timezone = settings.TIME_ZONE
            setting.save(update_fields=["timezone"])

        return setting.timezone
