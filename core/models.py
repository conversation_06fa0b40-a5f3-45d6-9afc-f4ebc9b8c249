import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _

# create a abstract model with created and updated fields
class BaseModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    is_deleted = models.<PERSON><PERSON>anField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class BaseAddress(models.Model):
    address_line_1 = models.CharField(_("address line 1"), max_length=255, blank=True)
    address_line_2 = models.CharField(_("address line 2"), max_length=255, blank=True)
    city = models.CharField(_("city"), max_length=255, blank=True)
    state = models.Char<PERSON>ield(_("state"), max_length=255, blank=True)
    zip_code = models.Char<PERSON>ield(_("zip code"), max_length=20, blank=True)
    country = models.Char<PERSON><PERSON>(_("country"), max_length=255, blank=True)

    class Meta:
        abstract = True