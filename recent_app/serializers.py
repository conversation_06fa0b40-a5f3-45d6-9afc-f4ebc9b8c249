from core.serializers import TimezoneConverterMixin
from django.utils import timezone
from rest_framework import serializers
from utils.utils import get_model_and_serializer

from .models import RecentActivity


class RecentActivitySerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):

    original_object = serializers.SerializerMethodField()

    class Meta:
        model = RecentActivity
        read_only_fields = ["id", "created_by"]
        exclude = ("is_deleted",)

    def create(self, validated_data):
        validated_data["created_by"] = self.context["request"].user

        item_id = validated_data.get("item_id")
        category = validated_data.get("category")

        recent_activity, created = RecentActivity.objects.update_or_create(
            created_by=self.context["request"].user,
            item_id=item_id,
            category=category,
            defaults={"updated_at": timezone.now()},
        )

        return recent_activity

    def get_original_object(self, item):
        model_class, serializer_class = get_model_and_serializer(item.category)
        instance = model_class.objects.filter(id=item.item_id).first()
        if not instance:
            return ()
        serializer = serializer_class(instance, context=self.context)
        return serializer.data


class ListRecentActivitySerializerV2(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = RecentActivity
        read_only_fields = ["id", "created_by"]
        exclude = ("is_deleted",)
