from django.urls import path
from .views import RecentActivityCreateView, RecentActivityListView, RecentActivityDetailView, RecentActivityDeleteView

urlpatterns = [
    path('', RecentActivityListView.as_view(), name='recent_activity_list'),
    path('create/', RecentActivityCreateView.as_view(), name='recent_activity_create'),
    path('<str:category>/<uuid:item_id>/', RecentActivityDetailView.as_view(), name='recent_activity_detail'),
    path('<uuid:pk>/delete/', RecentActivityDeleteView.as_view(), name='recent_activity_delete'),
]
