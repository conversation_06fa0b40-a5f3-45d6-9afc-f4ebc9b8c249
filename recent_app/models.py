from accounts.models import User
from core.models import BaseModel
from django.db import models
from django.utils.translation import gettext_lazy as _


class RecentActivity(BaseModel):
    class Category:
        RESOURCES = "resources"
        STORAGES = "storages"
        CONTACTS = "contacts"
        SUBCONTRACTORS = "subcontractors"
        PROJECTS = "projects"
        GALLERY = "galleries"
        PLAN_AND_ELEVATIONS = "plan_and_elevations"
        ESTIMATES = "estimates"
        CONTRACTS = "contracts"
        CHANGE_ORDERS = "change_orders"
        PAYMENT_SCHEDULES = "payment_schedules"
        PERFORMANCE_SCHEDULES = "performance_schedules"
        SPECIFICATIONS = "specifications"
        PERMITS = "permits"
        ADDITIONAL_DOCUMENTS = "additional_documents"
        LICENSES = "licenses"
        INSURANCES = "insurances"
        CALENDAR_EVENTS = "calendar_events"

        CHOICES = (
            (RESOURCES, _("Resources")),
            (STORAGES, _("Storages")),
            (CONTACTS, _("Contacts")),
            (SUBCONTRACTORS, _("Subcontractors")),
            (PROJECTS, _("Projects")),
            (GALLERY, _("Galleries")),
            (PLAN_AND_ELEVATIONS, _("Plan and Elevations")),
            (ESTIMATES, _("Estimates")),
            (CONTRACTS, _("Contracts")),
            (CHANGE_ORDERS, _("Change Orders")),
            (PAYMENT_SCHEDULES, _("Payment Schedules")),
            (PERFORMANCE_SCHEDULES, _("Performance Schedules")),
            (SPECIFICATIONS, _("Specifications")),
            (PERMITS, _("Permits")),
            (ADDITIONAL_DOCUMENTS, _("Additional Documents")),
            (LICENSES, _("Licenses")),
            (INSURANCES, _("Insurances")),
            (CALENDAR_EVENTS, _("Calendar Events")),
        )

    item_id = models.UUIDField()
    category = models.CharField(max_length=30, choices=Category.CHOICES)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        ordering = ["-updated_at"]

    def __str__(self):
        return f"{self.category}: {self.item_id} (Last accessed on {self.updated_at})"

    def save(self, *args, **kwargs) -> None:
        from core.dependency_injection import service_locator

        service_locator.recent_activity_service.clear_activities_cache(
            self.created_by
        )
        return super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        from core.dependency_injection import service_locator

        service_locator.recent_activity_service.clear_activities_cache(
            self.created_by
        )
        return super().delete(*args, **kwargs)
