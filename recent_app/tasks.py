import logging
from collections import defaultdict
from datetime import datetime
from datetime import timedelta

from celery import shared_task
from django.contrib.auth import get_user_model
from recent_app.models import RecentActivity
from recent_app.serializers import RecentActivitySerializer

logger = logging.getLogger(__name__)
User = get_user_model()


@shared_task()
def regenerate_recent_activities_cache(user_id):
    """
    Regenerate recent activities cache for a specific user in the background

    Args:
        user_id (int): ID of the user whose recent activities cache needs regeneration
    """
    try:
        from core.dependency_injection import service_locator

        user = User.objects.get(id=user_id)

        # Fetch recent activities
        queryset = RecentActivity.objects.filter(created_by=user).order_by(
            "-updated_at"
        )

        # Time ranges for grouping activities
        time_ranges = {
            "Today": (datetime.now().date(), datetime.now().date()),
            "Yesterday": (
                datetime.now().date() - timedelta(days=1),
                datetime.now().date() - timedelta(days=1),
            ),
            "This Week": (
                datetime.now().date()
                - timedelta(days=datetime.now().weekday()),
                datetime.now().date(),
            ),
            "Last Week": (
                datetime.now().date()
                - timedelta(days=datetime.now().weekday(), weeks=1),
                datetime.now().date()
                - timedelta(days=datetime.now().weekday() + 1),
            ),
            "This Month": (
                datetime.now().replace(day=1).date(),
                datetime.now().date(),
            ),
        }

        # Group activities
        grouped_activities = defaultdict(list)
        for label in time_ranges.keys():
            grouped_activities[label] = []

        for activity in queryset:
            activity_date = activity.updated_at.date()
            for label, (start_date, end_date) in time_ranges.items():
                if start_date <= activity_date <= end_date:
                    grouped_activities[label].append(activity)
                    break

        # Serialize grouped activities
        serialized_grouped_activities = {
            label: RecentActivitySerializer(activities, many=True).data
            for label, activities in grouped_activities.items()
        }

        service_locator.recent_activity_service.cache_activities(
            user, serialized_grouped_activities
        )

    except User.DoesNotExist:
        logger.error(
            f"User with ID {user_id} not found for cache regeneration"
        )
    except Exception as e:
        logger.error(f"Error regenerating recent activities cache: {e}")
