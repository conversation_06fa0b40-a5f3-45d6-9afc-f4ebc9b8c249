from recent_app.serializers import RecentActivitySerializer
from rest_framework.mixins import RetrieveModelMixin


class CreateRecentActivityMixin(RetrieveModelMixin):
    """
    A mixin class that creates a new RecentActivity entry whenever an object is retrieved.
    """

    def retrieve(self, request, *args, **kwargs):
        # Retrieve the instance using the get_object method
        instance = self.get_object()
        category = self.get_category()

        # Create a new recent activity entry
        recent_activity_data = {
            "item_id": instance.id,  # The ID of the retrieved project
            "category": category,  # Category from the get_category method
            "created_by": request.user,  # User who performed the retrieval
        }

        # Create an instance of the RecentActivitySerializer with the provided data
        recent_activity_serializer = RecentActivitySerializer(data=recent_activity_data, context={"request": request})

        # Save the new recent activity instance
        if recent_activity_serializer.is_valid():
            recent_activity_serializer.save()

        # Continue with the default retrieve logic from RetrieveModelMixin
        return super().retrieve(request, *args, **kwargs)

    def get_category(self):
        """
        This method should return the category to be used in recent activity entries.
        Override this method in subclasses to specify the category.
        """
        raise NotImplementedError("Subclasses must implement the get_category method.")
