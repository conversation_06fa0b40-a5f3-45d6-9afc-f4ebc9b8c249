# Generated by Django 3.2.17 on 2024-04-12 22:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RecentActivity',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('item_id', models.UUIDField()),
                ('category', models.CharField(choices=[('resources', 'Resources'), ('storages', 'Storages'), ('contacts', 'Contacts'), ('projects', 'Projects'), ('plan_and_elevations', 'Plan and Elevations'), ('estimates', 'Estimates'), ('contracts', 'Contracts'), ('change_orders', 'Change Orders'), ('payment_schedules', 'Payment Schedules'), ('performance_schedules', 'Performance Schedules'), ('specifications', 'Specifications'), ('permits', 'Permits'), ('additional_documents', 'Additional Documents'), ('licenses', 'Licenses'), ('insurances', 'Insurances'), ('calendar_events', 'Calendar Events')], max_length=30)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
    ]
