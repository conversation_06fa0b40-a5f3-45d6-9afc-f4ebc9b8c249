from calendar_app.models import CalendarEvent
from company.models import Insurance
from company.models import Licenses
from contacts.models import Contact
from contacts.models import Subcontractor
from core.constants import Features
from core.dependency_injection import service_locator
from django.db.models.signals import post_delete
from django.db.models.signals import post_save
from project.models import ChangeOrder
from project.models import Project
from project.models import ProjectDocument
from recent_app.models import RecentActivity
from resources.models import Resource
from storage.models import File


# Helper function to delete RecentActivity entries
def delete_recent_activity(instance, category):
    RecentActivity.objects.filter(
        item_id=instance.id, category=category
    ).delete()


# Common signal handler function
def handle_recent_activity(sender, instance, created=False, **kwargs):
    model_to_feature = {
        Contact: Features.CONTACTS,
        Subcontractor: Features.SUBCONTRACTORS,
        Project: Features.PROJECTS,
        ChangeOrder: Features.CHANGE_ORDERS,
        ProjectDocument: [
            Features.PLAN_AND_ELEVATIONS,
            Features.ESTIMATES,
            Features.CONTRACTS,
            Features.CHANGE_ORDERS,
            Features.PAYMENT_SCHEDULES,
            Features.PERFORMANCE_SCHEDULES,
            Features.SPECIFICATIONS,
            Features.PERMITS,
            Features.ADDITIONAL_DOCUMENTS,
            Features.GALLERY,
        ],
        Resource: Features.RESOURCES,
        File: Features.STORAGES,
        Licenses: Features.LICENSES,
        Insurance: Features.INSURANCES,
        CalendarEvent: Features.CALENDAR_EVENTS,
    }

    category = model_to_feature.get(type(instance))
    if not category:
        return

    if created:
        return

    # Only delete recent activity if instance is soft deleted
    if hasattr(instance, "is_deleted") and instance.is_deleted:
        if isinstance(category, list):
            activities = RecentActivity.objects.filter(
                item_id=instance.id, category__in=category
            )
        else:
            activities = RecentActivity.objects.filter(
                item_id=instance.id, category=category
            )

        # Clear cache for affected users before deleting activities
        for activity in activities:
            service_locator.recent_activity_service.clear_activities_cache(
                activity.created_by
            )

        activities.delete()


# Connect post_save signal for models that support soft delete
post_save_models = [
    Project,
    ProjectDocument,
    Resource,
    File,
    Licenses,
    Insurance,
]
for model in post_save_models:
    post_save.connect(handle_recent_activity, sender=model)

# Connect post_delete signal for all models
for model in [
    *post_save_models,
    Subcontractor,
    Contact,
    CalendarEvent,
    ChangeOrder,
]:
    post_delete.connect(handle_recent_activity, sender=model)
