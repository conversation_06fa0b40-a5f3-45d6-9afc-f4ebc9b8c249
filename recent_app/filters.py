import django_filters
from search_engine.search_engine_service import SearchEngineService
from utils.utils import get_model_and_serializer

from .models import RecentActivity


class RecentActivityFilter(django_filters.FilterSet):
    category = django_filters.ChoiceFilter(
        choices=RecentActivity.Category.CHOICES
    )
    search = django_filters.CharFilter(method="filter_by_search")

    class Meta:
        model = RecentActivity
        fields = ["category"]

    def filter_by_search(self, queryset, name, value):
        if not value:
            return queryset

        # Initialize search engine service
        search_engine = SearchEngineService(self.request.user)

        # Get categories from current queryset
        categories = set(queryset.values_list("category", flat=True))

        # Convert RecentActivity categories to Features using get_model_and_serializer
        search_categories = []
        for category in categories:
            model, serializer = get_model_and_serializer(category)
            if model is not None:  # If we have a valid mapping
                search_categories.append(category)

        # Perform search using SearchEngineService
        search_results = search_engine.search(
            value, categories=search_categories
        )

        # Collect matching IDs from search results
        matching_ids = set()
        for category in search_categories:
            if category in search_results:
                matching_ids.update(
                    str(item.id) for item in search_results[category]
                )

        if not matching_ids:
            return queryset.none()

        return queryset.filter(item_id__in=matching_ids)
