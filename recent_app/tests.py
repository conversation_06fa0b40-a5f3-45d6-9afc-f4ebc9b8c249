import uuid
from datetime import datetime
from datetime import timedelta

from accounts.factories import UserFactory
from company.factories import CompanyFactory
from company.factories import LicensesFactory
from contacts.factories import ContactFactory
from django.urls import reverse
from project.factories import ProjectFactory
from rest_framework import status
from testing.base import BaseAPITest

from .factories import RecentActivityFactory
from .models import RecentActivity


class RecentActivityTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        super().setUp()

    def test_unauthenticated_access(self):
        # Try accessing the list endpoint without authentication
        response = self.client.get(reverse("recent_activity_list"))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Try accessing the create endpoint without authentication
        response = self.client.post(reverse("recent_activity_create"))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Try accessing a specific detail endpoint without authentication
        response = self.client.get(
            reverse(
                "recent_activity_detail",
                kwargs={
                    "category": RecentActivity.Category.ESTIMATES,
                    "item_id": str(uuid.uuid4()),
                },
            )
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_recent_activities(self):
        self.client.force_authenticate(user=self.user)

        # Create instances
        license = LicensesFactory(company=self.company)
        contact = ContactFactory(uploaded_by=self.user)

        instances = [
            (
                license,
                reverse(
                    "company:license",
                    kwargs={"company_id": self.company.id, "pk": license.id},
                ),
            ),
            (contact, reverse("contacts:contact", kwargs={"pk": contact.id})),
        ]

        instance_ids = [str(license.id), str(contact.id)]

        for _instance, url in instances:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        recent_activity_url = reverse("recent_activity_list")
        recent_activity_response = self.client.get(recent_activity_url)
        self.assertEqual(
            recent_activity_response.status_code, status.HTTP_200_OK
        )

        result_ids = []
        for activities in recent_activity_response.data.values():
            for activity in activities:
                result_ids.append(activity["item_id"])
        for instance_id in instance_ids:
            self.assertIn(instance_id, result_ids)

        RecentActivity.objects.all().update(
            updated_at=datetime.now() - timedelta(days=31)
        )

        for activities in recent_activity_response.data.values():
            for result in activities:
                updated_at = datetime.fromisoformat(
                    result["updated_at"]
                ).date()
                self.assertTrue(
                    datetime.now().date() - updated_at < timedelta(days=31)
                )

    def test_create_recent_activity(self):
        self.client.force_authenticate(user=self.user)
        url = reverse("recent_activity_create")
        data = {
            "item_id": str(uuid.uuid4()),
            "category": RecentActivity.Category.PLAN_AND_ELEVATIONS,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(
            RecentActivity.objects.filter(item_id=data["item_id"]).exists()
        )

    def test_retrieve_recent_activity(self):
        project = ProjectFactory(created_by=self.user)

        recent_activity = RecentActivityFactory(
            created_by=self.user,
            item_id=project.pk,
            category=RecentActivity.Category.PROJECTS,
        )

        self.client.force_authenticate(user=self.user)

        url = reverse(
            "recent_activity_detail",
            kwargs={
                "category": recent_activity.category,
                "item_id": recent_activity.item_id,
            },
        )

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], str(recent_activity.item_id))

    def test_delete_recent_activity(self):
        project = ProjectFactory(created_by=self.user)

        recent_activity = RecentActivityFactory(
            created_by=self.user,
            item_id=project.pk,
            category=RecentActivity.Category.PROJECTS,
        )

        self.client.force_authenticate(user=self.user)

        url = reverse(
            "recent_activity_delete", kwargs={"pk": recent_activity.pk}
        )

        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            RecentActivity.objects.filter(pk=recent_activity.pk).exists()
        )

    def test_signal_triggered_on_project_delete(self):
        project = ProjectFactory(created_by=self.user)

        self.client.force_authenticate(user=self.user)

        project_delete_url = reverse(
            "project:project", kwargs={"pk": project.id}
        )

        response = self.client.delete(project_delete_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        self.assertFalse(
            RecentActivity.objects.filter(
                item_id=project.pk, category=RecentActivity.Category.PROJECTS
            ).exists()
        )

    def test_signal_triggered_on_contact_delete(self):
        contact = ContactFactory(uploaded_by=self.user)

        self.client.force_authenticate(user=self.user)

        contact_delete_url = reverse(
            "contacts:contact", kwargs={"pk": contact.id}
        )

        response = self.client.delete(contact_delete_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        self.assertFalse(
            RecentActivity.objects.filter(
                item_id=contact.pk, category=RecentActivity.Category.CONTACTS
            ).exists()
        )
