from accounts.models import User
from django.conf import settings
from django.core.cache import cache
from general.cache_keys import REDIS_CACHE_KEY


class RecentActivityService:
    def cache_activities(self, user: User, activities_data: dict):
        key = REDIS_CACHE_KEY.get_recent_activities_key(str(user.id))
        cache.set(key, activities_data, settings.RECENT_ACTIVITY_CACHE_TIMEOUT)

    def get_cached_activities(self, user: User):
        key = REDIS_CACHE_KEY.get_recent_activities_key(str(user.id))
        return cache.get(key)

    def clear_activities_cache(self, user: User):
        key = REDIS_CACHE_KEY.get_recent_activities_key(str(user.id))
        cache.delete(key)
