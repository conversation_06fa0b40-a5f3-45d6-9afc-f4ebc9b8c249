from collections import defaultdict
from datetime import datetime
from datetime import timed<PERSON><PERSON>

from core.dependency_injection import service_locator
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import generics
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from utils.utils import get_model_and_serializer

from .filters import RecentActivityFilter
from .models import RecentActivity
from .serializers import ListRecentActivitySerializerV2
from .serializers import RecentActivitySerializer


class RecentActivityCreateView(generics.CreateAPIView):
    """
    Create a new recent activity when an item is interacted with in the app.
    """

    queryset = RecentActivity.objects.all()
    serializer_class = RecentActivitySerializer
    permission_classes = [IsAuthenticated]


class RecentActivityListView(generics.ListAPIView):
    """
    Retrieve a list of all recent activities grouped by time with filtering capabilities.
    Uses caching to improve performance.
    """

    serializer_class = RecentActivitySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = RecentActivityFilter

    def get_queryset(self):
        """
        Filter activities by the user and order by last updated time in descending order
        """
        return RecentActivity.objects.filter(
            created_by=self.request.user
        ).order_by("-updated_at")

    def list(self, request, *args, **kwargs):
        """
        Return a list of recent activities grouped by time.
        Uses caching to improve performance.
        """
        # Try to get cached activities first
        cached_activities = (
            service_locator.recent_activity_service.get_cached_activities(
                request.user
            )
        )

        if cached_activities:
            return Response(cached_activities, status=status.HTTP_200_OK)

        # If no cached data, generate and cache it
        queryset = self.get_queryset()
        grouped_activities = self.group_activities_by_time(queryset[:10])

        # Cache the result
        service_locator.recent_activity_service.cache_activities(
            request.user, grouped_activities
        )

        return Response(grouped_activities, status=status.HTTP_200_OK)

    def group_activities_by_time(self, queryset):
        """
        Groups the queryset into time ranges and serializes them.
        """
        time_ranges = {
            "Today": (datetime.now().date(), datetime.now().date()),
            "Yesterday": (
                datetime.now().date() - timedelta(days=1),
                datetime.now().date() - timedelta(days=1),
            ),
            "This Week": (
                datetime.now().date()
                - timedelta(days=datetime.now().weekday()),
                datetime.now().date(),
            ),
            "Last Week": (
                datetime.now().date()
                - timedelta(days=datetime.now().weekday(), weeks=1),
                datetime.now().date()
                - timedelta(days=datetime.now().weekday() + 1),
            ),
            "This Month": (
                datetime.now().replace(day=1).date(),
                datetime.now().date(),
            ),
        }

        grouped_activities = defaultdict(list)

        # Initialize all time ranges with empty lists
        for label in time_ranges.keys():
            grouped_activities[label] = []

        # Categorize activities into time ranges
        for activity in queryset:
            activity_date = activity.updated_at.date()
            for label, (start_date, end_date) in time_ranges.items():
                if start_date <= activity_date <= end_date:
                    grouped_activities[label].append(activity)
                    break

        # Serialize grouped activities, including empty groups
        serialized_grouped_activities = {
            label: self.serializer_class(
                activities, many=True, context=self.get_serializer_context()
            ).data
            for label, activities in grouped_activities.items()
        }

        return serialized_grouped_activities


class RecentActivityDetailView(generics.RetrieveAPIView):
    """
    Retrieve the details of a recent activity based on the category and item ID.
    """

    queryset = RecentActivity.objects.all()
    serializer_class = RecentActivitySerializer
    permission_classes = [IsAuthenticated]

    def get_recent_activity(self):
        """
        Get the recent activity object based on the created_by, item_id, and category.
        """
        item_id = self.kwargs["item_id"]
        category = self.kwargs["category"]
        try:
            recent_activity = RecentActivity.objects.get(
                created_by=self.request.user,
                item_id=item_id,
                category=category,
            )
            return recent_activity
        except RecentActivity.DoesNotExist:
            return None

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve the recent activity and the corresponding item.
        """
        recent_activity = self.get_recent_activity()

        # If recent activity is not found, return a 404 response with a custom message
        if recent_activity is None:
            return Response(
                {"error": "Recent activity not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Update the updated_at field to now
        recent_activity.updated_at = timezone.now()
        recent_activity.save()

        # Determine the model class and serializer class based on the category
        model_class, serializer_class = get_model_and_serializer(
            recent_activity.category
        )

        # If model class or serializer class is not found, return a 404 response with a custom message
        if model_class is None or serializer_class is None:
            return Response(
                {
                    "error": "Model or serializer not found for the given category"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Retrieve the item from the appropriate model based on category and item ID
        try:
            item = model_class.objects.get(pk=recent_activity.item_id)
        except model_class.DoesNotExist:
            return Response(
                {
                    "error": f"Item with ID {recent_activity.item_id} not found in category {recent_activity.category}"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Serialize the item
        item_serializer = serializer_class(item)
        return Response(item_serializer.data, status=status.HTTP_200_OK)


class RecentActivityDeleteView(generics.DestroyAPIView):
    queryset = RecentActivity.objects.all()
    serializer_class = RecentActivitySerializer
    permission_classes = [IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        """Override the delete method to provide custom behavior, if needed."""
        instance = self.get_object()
        self.perform_destroy(instance)

        # Custom response or processing can be added here

        return Response(
            {"message": "Recent activity deleted successfully"}, status=204
        )

    def perform_destroy(self, instance):
        """Custom method to perform deletion. Override if needed."""
        instance.delete()


class ListRecentActivitiesViewV2(generics.ListAPIView):
    def get_queryset(self):
        """
        Filter activities by the user and order by last updated time in descending order
        """
        return RecentActivity.objects.filter(
            created_by=self.request.user
        ).order_by("-updated_at")

    permission_classes = [IsAuthenticated]
    serializer_class = ListRecentActivitySerializerV2
