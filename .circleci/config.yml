version: 2.1

orbs:
  jira: circleci/jira@1.3.1
  browser-tools: circleci/browser-tools@1.2.5

jobs:
  build:
    working_directory: ~/backend
    environment:
      DATABASE_NAME: postgres
      DATABASE_USER: postgres
      DATABASE_PASSWORD: postgres
      DATABASE_PORT: 5432
      DATABASE_HOST: localhost
      AWS_ACCESS_KEY_ID: ab
      AWS_SECRET_ACCESS_KEY: ELE8
      AWS_STORAGE_BUCKET_NAME: "te"
      AWS_S3_SIGNATURE_VERSION: "s3v4"
      AWS_S3_REGION_NAME: "eu-west-3"
      AWS_S3_CUSTOM_DOMAIN: tuulb
      AWS_PRESIGNED_EXPIRY: "10"
      FILE_UPLOAD_STORAGE: local #local | s3
      FILE_MAX_SIZE: "10485760" #10MB
      TUULBOX_FIREBASE_CREDENTIALS: "secrets/tuulbox-firebase_cred.json"
      CELERY_BROKER_URL: "redis://localhost:6379"
      CELERY_RESULT_BACKEND: "redis://localhost:6379"

    docker:
      - image: cimg/python:3.10
        environment:
          CIRCLE_ARTIFACTS: .artifacts
          DATABASE_URL: postgresql://ubuntu@localhost/circle_test
          ENVIRONMENT: circleci
          PYTHONPATH: .

      - image: cimg/postgres:14.0
        environment:
          POSTGRES_USER: "postgres"
          POSTGRES_DB: "booking"
          POSTGRES_PASSWORD: "postgres"
      - image: cimg/redis:6.2.6

    steps:
      - checkout

      - restore_cache:
          keys:
            - tuulboxcore-{{ .Branch }}-{{ checksum "requirements.txt"}}
      - run:
          name: Install libraries
          command: |
            pip uninstall -y typing
            pip install --upgrade -r requirements.txt
      - save_cache:
          paths:
            - ~/.cache/pip
          key: tuulboxcore-{{ .Branch }}-{{ checksum "requirements.txt"}}

      # Tests, style checks
      - run:
          name: Check for code smells (prints, debugger)
          command: scripts/check_code_smells.sh

      - run:
          name: Run code style check
          command: scripts/check_git_style.sh

      - run:
          name: Check for conflicting migrations
          command: scripts/check_migrations.sh

      - run:
          name: Run tests
          command: python manage.py test --verbosity=2

  build_docker_image:
    resource_class: medium
    machine:
      image: default
    steps:
      - checkout

      - run:
          name: Build image
          command: docker build -t tuulboxcore --file Dockerfile.prod .

      - run:
          name: Publish image
          command: ./scripts/publish-image.sh $CIRCLE_BRANCH

  deploy:
    docker:
      - image: cimg/python:3.10

    steps:
      - checkout

      - restore_cache:
          keys:
            - tuulboxcore-{{ .Branch }}-{{ checksum "requirements.txt"}}
            - tuulboxcore-{{ .Branch }}
            - tuulboxcore

      - run:
          name: Install dependencies
          command: |
            pip install awscli

      - deploy:
          name: Deploy to ecs
          command: ./scripts/deploy-ecs.sh $CIRCLE_BRANCH

workflows:
  test_and_deploy:
    jobs:
      - build
      - build_docker_image:
          name: build_docker_image
          requires:
            - build
          filters:
            branches:
              only:
                - master
                - staging
                - production
      - deploy:
          name: deploy
          requires:
            - build_docker_image
          filters:
            branches:
              only:
                - master
                - staging
                - production
