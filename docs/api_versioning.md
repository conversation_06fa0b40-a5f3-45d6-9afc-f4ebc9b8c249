# API Versioning Guide

This document explains how API versioning works in the Tuulbox project.

## Overview

The Tuulbox API supports versioning to allow for backward compatibility while adding new features. The API currently supports two versions:

- **v1**: The original API version
- **v2**: The new API version with additional features and improvements

## How to Use API Versions

### URL-based Versioning

The API uses URL-based versioning. To access a specific version of the API, include the version in the URL path:

```
# v1 API (default)
https://api.example.com/api/v1/resources/

# v2 API
https://api.example.com/api/v2/resources/
```

For backward compatibility, v1 endpoints are also available without the version prefix:

```
# Also v1 API
https://api.example.com/api/resources/
```

### API Documentation

API documentation is available for each version:

- v1 API: `/swagger/v1/` or `/redoc/v1/`
- v2 API: `/swagger/v2/` or `/redoc/v2/`

For backward compatibility, the default documentation at `/swagger/` and `/redoc/` shows the v1 API.

## Creating Version-Specific Endpoints

To create a version-specific endpoint, follow these steps:

1. Create a new serializer for the new version (e.g., `serializers_v2.py`)
2. Create a new view that inherits from the original view and the `VersionedAPIMixin` (e.g., `views_v2.py`)
3. Create a new URL configuration for the new version (e.g., `urls_v2.py`)
4. Update the API v2 URL configuration to include the new version-specific URLs

### Example

```python
# serializers_v2.py
class ResourceSerializerV2(ResourceSerializer):
    """Version 2 of the ResourceSerializer with additional fields."""

    additional_field = serializers.CharField()


# views_v2.py
class ResourceListViewV2(VersionedAPIMixin, ResourceListView):
    """Version 2 of the ResourceListView."""

    versioned_serializer_classes = {
        "v1": ResourceSerializer,
        "v2": ResourceSerializerV2,
    }


# urls_v2.py
urlpatterns = [
    path("", ResourceListViewV2.as_view(), name="resource_list"),
    # ...
]
```

## Testing Versioned APIs

When testing versioned APIs, make sure to test both versions to ensure backward compatibility. See `resources/tests/test_versioned_api.py` for an example of how to test versioned APIs.

## Best Practices

1. **Backward Compatibility**: Always maintain backward compatibility when creating new API versions.
2. **Documentation**: Document changes between API versions.
3. **Testing**: Test both old and new API versions to ensure they work correctly.
4. **Gradual Migration**: Encourage clients to migrate to newer API versions gradually.
5. **Deprecation Policy**: Clearly communicate when older API versions will be deprecated.
