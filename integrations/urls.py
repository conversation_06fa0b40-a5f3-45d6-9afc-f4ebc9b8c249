# create url for the CompanyViewSet
from django.urls import path

from .views import GoogleIntegrationCallBackView
from .views import IntegrationView
from .views import StartGoogleIntegration
from .views import CalendarIntegrationView
from .views import StartGoogleCalendarIntegration
from .views import NewEmailView

app_name = "integrations"
urlpatterns = [
    path(
        "google/",
        GoogleIntegrationCallBackView.as_view(),
        name="google",
    ),
    path(
        "start-google-integration/",
        StartGoogleIntegration.as_view(),
        name="start_google_integration",
    ),
    path(
        "",
        IntegrationView.as_view(),
        name="integration",
    ),
    path(
        "start-google-calendar-integration/",
        StartGoogleCalendarIntegration.as_view(),
        name="start_google_calendar_integration",
    ),
    path(
        "calendar-integration/",
        CalendarIntegrationView.as_view(),
        name="calendar_integration",
    ),
    path(
        "new-email/",
        NewEmailView.as_view(),
        name="new_email"
    )
]
