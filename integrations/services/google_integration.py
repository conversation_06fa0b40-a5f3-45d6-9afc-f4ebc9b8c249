import json
import logging

import google_auth_oauthlib.flow
import jwt
import requests
from accounts.models import User
from django.conf import settings
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials
from rest_framework import status
from rest_framework.exceptions import APIException

logger = logging.getLogger(__name__)


class TokenExpirationException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = "The Gmail integration token has expired or been revoked. Please reconnect your account."
    default_code = "token_expired"


class GoogleIntegrationService:
    flow = None
    scopes = [
        "openid",
        "https://www.googleapis.com/auth/gmail.readonly",
        "https://www.googleapis.com/auth/gmail.send",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile",
    ]

    def __init__(self):
        self.flow = google_auth_oauthlib.flow.Flow.from_client_secrets_file(
            "client_secret.json", scopes=self.scopes
        )

    def validate_token(self, integration):
        """Validate token and handle expired/revoked cases."""
        try:
            if not integration or not integration.credentials:
                raise TokenExpirationException()

            credentials_dict = json.loads(integration.credentials)
            credentials = Credentials.from_authorized_user_info(
                credentials_dict
            )

            if not credentials or not credentials.valid:
                if (
                    credentials
                    and credentials.expired
                    and credentials.refresh_token
                ):
                    try:
                        credentials.refresh(requests.Request())
                        integration.credentials = credentials.to_json()
                        integration.save()
                    except RefreshError as err:
                        self._cleanup_invalid_integration(integration)
                        raise TokenExpirationException() from err
                else:
                    self._cleanup_invalid_integration(integration)
                    raise TokenExpirationException()

            return credentials

        except Exception as err:
            logger.error(
                "Error validating Gmail token",
                exc_info=True,
                extra={
                    "integration_id": getattr(integration, "id", None),
                    "error": str(err),
                },
            )
            self._cleanup_invalid_integration(integration)
            raise TokenExpirationException() from err

    def _cleanup_invalid_integration(self, integration):
        """Clean up invalid integration by revoking token and deleting."""
        if integration:
            try:
                self.revoke_token(integration)
            except Exception:
                logger.warning(
                    "Failed to revoke Gmail token during cleanup",
                    exc_info=True,
                    extra={"integration_id": integration.id},
                )
            finally:
                integration.delete()

    def revoke_token(self, integration):
        """Revoke the token using the integration object."""
        try:
            credentials = (
                json.loads(integration.credentials)
                if integration.credentials
                else None
            )
            token = credentials.get("token") if credentials else None

            if not token:
                logger.warning(
                    "No token found in Gmail credentials during revocation",
                    extra={"integration_id": integration.id},
                )
                return

            response = requests.post(
                "https://oauth2.googleapis.com/revoke",
                params={"token": token},
                headers={"content-type": "application/x-www-form-urlencoded"},
            )

            if response.status_code not in (
                200,
                400,
            ):  # 400 means token already invalid
                response.raise_for_status()

        except Exception as err:
            logger.error(
                "Error revoking Gmail token",
                exc_info=True,
                extra={"integration_id": integration.id, "error": str(err)},
            )
            raise ValueError(f"Failed to revoke token: {str(err)}") from err

    def start_integration(self, user: User, redirect_url=None):
        if not redirect_url:
            redirect_url = settings.DEFAULT_REDIRECT_URL
        url = settings.GOOGLE_INTEGRATION_REDIRECT_URI
        self.flow.redirect_uri = f"{url}"
        authorization_url, state = self.flow.authorization_url(
            access_type="offline",
            prompt="consent",
            include_granted_scopes="false",
            state=f"{user.id},gmail,{redirect_url}",
        )
        return authorization_url

    def get_refresh_and_access_token(self, authorization_response):
        code = authorization_response["code"]
        self.flow.fetch_token(code=code)
        credentials = self.flow.credentials
        credentials_to_json = credentials.to_json()
        user_info = self.get_user_info(credentials)

        return {
            "credentials": credentials_to_json,
            **user_info,
        }

    def get_user_info(self, credentials):
        id_token = credentials.id_token
        decoded_id_token = jwt.decode(
            id_token, options={"verify_signature": False}
        )

        return {
            "email": decoded_id_token.get("email", ""),
            "name": decoded_id_token.get("name", ""),
            "given_name": decoded_id_token.get("given_name", ""),
            "family_name": decoded_id_token.get("family_name", ""),
        }
