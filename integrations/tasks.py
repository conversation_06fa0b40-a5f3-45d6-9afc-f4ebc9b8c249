import base64
import json
import logging
from datetime import timedelta

import sentry_sdk
from celery import shared_task
from communication.services.gmail_service import GmailService
from core.constants import Features
from dateutil import parser
from firebase_admin import messaging
from integrations.models import GoogleIntegration
from notifications.models import NotificationPreference
from project.models import Project
from utils.firebase.messaging import FirebaseMessageService
from utils.utils import create_or_update_notification

logger = logging.getLogger(__name__)


@shared_task()
def notify_user_about_new_email(data):
    with sentry_sdk.push_scope() as scope:
        messaging_service = FirebaseMessageService()
        data = data.get("message")
        json_message = base64.b64decode(data.get("data"))
        decoded_message = json.loads(json_message)
        email = decoded_message.get("emailAddress")
        integration = GoogleIntegration.objects.filter(email=email).first()

        scope.set_extra("data", decoded_message)
        if not integration:
            sentry_sdk.capture_message(
                f"Gmail Integration not found for email: {email}"
            )
            return

        user = integration.user

        # Check if the user has email notifications enabled
        (
            notification_preference,
            _,
        ) = NotificationPreference.objects.get_or_create(user=user)
        if notification_preference.emails:
            gmail_service = GmailService(integration)

            # Convert publishTime to datetime
            publish_time = parser.parse(data.get("publish_time"))
            after = (publish_time - timedelta(minutes=5)).timestamp()
            before = (publish_time + timedelta(minutes=2)).timestamp()

            kwargs = {
                "after": f"{after}".split(".")[0],
                "before": f"{before}".split(".")[0],
                "prefetch_details": False,
                "additional_query": "in:inbox",
            }
            new_messages = gmail_service.get_all_messages(**kwargs)
            sender_email = None

            for message in new_messages:
                message_id = message.get("id")
                message_detail = gmail_service.get_message_details(
                    message_id=message_id
                )

                sender_email = message_detail.get("sender")
                # Normalize the email address
                sender_email = (
                    sender_email.strip()
                    .split()[-1]
                    .replace("<", "")
                    .replace(">", "")
                )
                subject = message_detail.get("subject")

                # Fetch user projects to check sender against owner, primary contacts, and subcontractors
                matching_projects = []
                user_projects = Project.objects.filter(created_by=user)

                for project in user_projects:
                    is_sender_a_project_owner = sender_email == project.email
                    is_sender_a_primary_contact = (
                        sender_email
                        in project.additional_contacts.values_list(
                            "email", flat=True
                        )
                    )
                    is_sender_a_subcontractor = (
                        sender_email
                        in project.subcontractors.values_list(
                            "contact__email", flat=True
                        )
                    )

                    if (
                        not is_sender_a_project_owner
                        and not is_sender_a_primary_contact
                        and not is_sender_a_subcontractor
                    ):
                        continue
                    matching_projects.append(project)

                if not matching_projects:
                    continue

                topic = str(user.id)
                title = f"New Email from {sender_email}"
                msg = f"{subject}"

                [project.name for project in matching_projects]
                project_ids = [
                    str(project.id) for project in matching_projects
                ]
                data_payload = {
                    "id": message_id,
                    "category": Features.EMAILS,
                    # "project_id": str(project.id),
                    "project_ids": str(project_ids),
                }

                # Save the notification to the database
                create_or_update_notification(
                    user, Features.EMAILS, message_id
                )

                message = messaging.Message(
                    topic=topic,
                    data=data_payload,
                    notification=messaging.Notification(
                        title=title,
                        body=msg,
                    ),
                )
                sentry_sdk.capture_message(
                    f"Sending message push notification to {email}: {message}"
                )
                messaging_service.send_message_to_topic(message)
