from accounts.models import User
from core.models import BaseModel
from django.db import models


class BaseIntegration(BaseModel):
    """
    Integration model
    """

    user = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.user.email} - intergration"

    class Meta:
        verbose_name = "Integration"
        verbose_name_plural = "Integrations"
        abstract = True


class GoogleIntegration(BaseIntegration):
    """
    Google integration model
    """

    credentials = models.JSONField()
    email = models.EmailField()

    def __str__(self):
        return f"{self.user.email} - google intergration"

    class Meta:
        verbose_name = "Google Integration"
        verbose_name_plural = "Google Integrations"


class GoogleCalendarIntegration(BaseIntegration):
    """
    Google Calendar integration model
    """

    credentials = models.J<PERSON><PERSON>ield()  # Stores access/refresh tokens
    email = models.EmailField()

    def __str__(self):
        return f"{self.user.email} - Google Calendar integration"

    class Meta:
        verbose_name = "Google Calendar Integration"
        verbose_name_plural = "Google Calendar Integrations"
