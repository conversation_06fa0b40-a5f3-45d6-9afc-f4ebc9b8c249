import logging

from accounts.models import User
from django.conf import settings
from django.shortcuts import render
from integrations.models import GoogleCalendarIntegration
from integrations.models import GoogleIntegration
from integrations.serializers import CalendarIntegrationSerializer
from integrations.serializers import IntegrationSerializer
from integrations.services.google_calendar_integration import (
    GoogleCalendarIntegrationService,
)
from integrations.services.google_integration import GoogleIntegrationService
from integrations.tasks import notify_user_about_new_email
from rest_framework import serializers
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import RetrieveDestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView


googleIntegrationService = GoogleIntegrationService()
googleCalendarIntegrationService = GoogleCalendarIntegrationService()


logger = logging.getLogger(__name__)


class GoogleIntegrationException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "An error occurred with the Google integration."
    default_code = "google_integration_error"


class TokenExpirationException(GoogleIntegrationException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = "The Google integration token has expired or been revoked. Please reconnect your account."
    default_code = "token_expired"


class GoogleIntegrationCallBackView(APIView):
    """
    View to handle the Google integration callback.
    """

    def get(self, request: Request, *args, **kwargs):
        """
        Handle the Google integration callback.
        """
        try:
            authorization_response = request.GET

            # Safely get state parameter
            state = authorization_response.get("state")
            if not state:
                return Response(
                    {"error": "Invalid callback: missing state parameter"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Parse state parameter with defaults
            state_parts = state.split(",")
            user_id = state_parts[0].strip() if len(state_parts) > 0 else None
            integration_type = (
                state_parts[1].strip() if len(state_parts) > 1 else None
            )
            redirect_url = (
                state_parts[2].strip()
                if len(state_parts) > 2
                else settings.DEFAULT_REDIRECT_URL
            )

            if not user_id or not integration_type:
                return Response(
                    {"error": "Invalid state parameter format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user = User.objects.filter(id=user_id).first()

            if not user:
                context = {
                    "error_message": "The integration failed due to an invalid user.",
                    "redirect_url": f"{redirect_url}?status=failure"
                    if redirect_url
                    else f"{settings.DEFAULT_REDIRECT_URL}?status=failure",
                }
                return render(request, "integration_failed.html", context)

            try:
                if integration_type == "gmail":
                    integration = (
                        googleIntegrationService.get_refresh_and_access_token(
                            authorization_response
                        )
                    )
                    GoogleIntegration.objects.update_or_create(
                        user=user,
                        defaults={
                            "credentials": integration.get("credentials"),
                            "email": integration.get("email"),
                        },
                    )

                elif integration_type == "calendar":
                    integration = googleCalendarIntegrationService.get_refresh_and_access_token(
                        authorization_response
                    )
                    GoogleCalendarIntegration.objects.update_or_create(
                        user=user,
                        defaults={
                            "credentials": integration.get("credentials"),
                            "email": integration.get("email"),
                        },
                    )
                else:
                    return Response(
                        {
                            "error": f"Invalid integration type: {integration_type}"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            except Exception:
                context = {
                    "error_message": "An error occurred during the integration process.",
                    "redirect_url": f"{redirect_url}?status=failure"
                    if redirect_url
                    else f"{settings.DEFAULT_REDIRECT_URL}?status=failure",
                }
                return render(request, "integration_failed.html", context)

            return render(
                request,
                "integration_success.html",
                {
                    "redirect_url": f"{redirect_url}?status=success"
                    if redirect_url
                    else f"{settings.DEFAULT_REDIRECT_URL}?status=success"
                },
            )

        except Exception as e:
            return Response(
                {"error": f"Integration failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class StartGoogleIntegration(APIView):
    """
    View to start the Google integration.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Get redirect_url from query params or use default
        redirect_url = request.query_params.get(
            "redirect_url", settings.DEFAULT_REDIRECT_URL
        )

        # Get the integration object
        integration = GoogleCalendarIntegration.objects.filter(
            user=self.request.user
        ).first()

        if integration:
            # Revoke the token using the integration object directly
            googleIntegrationService.revoke_token(integration)

        authorization_url = googleIntegrationService.start_integration(
            self.request.user, redirect_url
        )
        return Response(
            {"redirect_url": authorization_url, "user": self.request.user.id}
        )


class IntegrationView(RetrieveDestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = IntegrationSerializer

    def get_object(self):
        """Get the Gmail integration object."""
        integration = GoogleIntegration.objects.filter(
            user=self.request.user
        ).first()

        if integration:
            try:
                googleIntegrationService.validate_token(integration)
            except TokenExpirationException:
                return None

        return integration

    def get(self, request, *args, **kwargs):
        """Check if a user has a valid Gmail integration."""
        try:
            return super().get(request, *args, **kwargs)
        except TokenExpirationException as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED
            )

    def delete(self, request, *args, **kwargs):
        integration = self.get_object()
        if integration:
            try:
                googleIntegrationService.revoke_token(integration)
                integration.delete()
                return Response(status=status.HTTP_204_NO_CONTENT)
            except ValueError as e:
                logger.error(
                    "Failed to revoke Gmail token",
                    exc_info=True,
                    extra={
                        "user_id": request.user.id,
                        "integration_id": integration.id,
                    },
                )
                return Response(
                    {"error": str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(
            {"error": "Integration not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


class StartGoogleCalendarIntegration(APIView):
    """
    View to start the Google Calendar integration.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Get redirect_url from query params or use default
        redirect_url = request.query_params.get(
            "redirect_url", settings.DEFAULT_REDIRECT_URL
        )

        # Get the integration object
        integration = GoogleCalendarIntegration.objects.filter(
            user=self.request.user
        ).first()

        if integration:
            # Revoke the token using the integration object directly
            googleIntegrationService.revoke_token(integration)

        authorization_url = googleCalendarIntegrationService.start_integration(
            self.request.user, redirect_url
        )
        return Response(
            {"redirect_url": authorization_url, "user": self.request.user.id}
        )


class CalendarIntegrationView(RetrieveDestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = CalendarIntegrationSerializer

    def get_object(self):
        """Get the Google Calendar integration object."""
        integration = GoogleCalendarIntegration.objects.filter(
            user=self.request.user
        ).first()

        if integration:
            try:
                googleCalendarIntegrationService.validate_token(integration)
            except TokenExpirationException:
                return None

        return integration

    def get(self, request, *args, **kwargs):
        """Check if a user has a valid Google Calendar integration."""
        try:
            return super().get(request, *args, **kwargs)
        except TokenExpirationException as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED
            )

    def delete(self, request, *args, **kwargs):
        integration = self.get_object()
        if integration:
            try:
                googleCalendarIntegrationService.revoke_token(integration)
                integration.delete()
                return Response(status=status.HTTP_204_NO_CONTENT)
            except ValueError as e:
                logger.error(
                    "Failed to revoke calendar token",
                    exc_info=True,
                    extra={
                        "user_id": request.user.id,
                        "integration_id": integration.id,
                    },
                )
                return Response(
                    {"error": str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(
            {"error": "Integration not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


class NewEmailSerializer(serializers.Serializer):
    message = serializers.DictField()


class NewEmailView(APIView):

    serializers_class = NewEmailSerializer

    def post(self, request):
        try:
            notify_user_about_new_email.delay(request.data)
        except Exception:
            pass

        return Response(status=status.HTTP_200_OK)
