# Create your tests here.
import json
from unittest.mock import patch

from accounts.factories import UserFactory
from django.urls import reverse
from integrations.factories import GoogleCalendarIntegrationFactory
from integrations.factories import GoogleIntegrationFactory
from rest_framework import status
from testing.base import BaseAPITest


class GoogleIntegrationTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.start_integration_url = reverse(
            "integrations:start_google_integration",
        )
        return super().setUp()

    def test_unauthorized_access(self):
        # Given the user is not authenticated
        self.client.force_authenticate(user=None)
        # When the user tries to start the integration
        response = self.client.get(self.start_integration_url)
        # Then the user should get a 401 response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        retreive_integration_url = reverse(
            "integrations:integration",
        )

        # When the user tries to retreive the integration
        response = self.client.get(retreive_integration_url)
        # Then the user should get a 401 response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch(
        "integrations.services.google_integration.GoogleIntegrationService.start_integration"
    )
    def test_start_integration(self, mock_start_integration):
        # Given the user is authenticated
        self.client.force_authenticate(user=self.user)
        mock_start_integration.return_value = "https://google.com"

        # When the user tries to start the integration
        response = self.client.get(self.start_integration_url)

        # Then the user should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And the response should contain the url to start the integration
        self.assertEqual(response.data["redirect_url"], "https://google.com")

    @patch(
        "integrations.services.google_integration.GoogleIntegrationService.get_refresh_and_access_token"
    )
    def test_google_integration_callback(
        self, mock_get_refresh_and_access_token
    ):
        # Given we receive a callback from Google
        url = reverse("integrations:google")

        state_data = f"{self.user.id},gmail"

        mock_get_refresh_and_access_token.return_value = {
            "credentials": json.dumps({}),
            "email": "<EMAIL>",
        }

        # When the callback is processed
        response = self.client.get(url, data={"state": state_data})

        # Then the user should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And the integration should be created
        self.assertEqual(self.user.googleintegration_set.count(), 1)
        self.assertEqual(
            self.user.googleintegration_set.first().email, "<EMAIL>"
        )

    def test_delete_integration(self):
        self.client.force_authenticate(user=self.user)

        GoogleIntegrationFactory(
            user=self.user, credentials=json.dumps({"token": "test_token"})
        )

        with patch(
            "integrations.services.google_integration.GoogleIntegrationService.validate_token"
        ) as mock_validate:
            mock_validate.return_value = True

            delete_integration_url = reverse("integrations:integration")
            self.assertEqual(self.user.googleintegration_set.count(), 1)

            response = self.client.delete(delete_integration_url)
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            self.assertEqual(self.user.googleintegration_set.count(), 0)

    def test_retrevie_integration(self):
        self.client.force_authenticate(user=self.user)

        integration = GoogleIntegrationFactory(
            user=self.user, credentials=json.dumps({"token": "test_token"})
        )

        with patch(
            "integrations.services.google_integration.GoogleIntegrationService.validate_token"
        ) as mock_validate:
            mock_validate.return_value = True

            retrieve_integration_url = reverse("integrations:integration")
            response = self.client.get(retrieve_integration_url)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data["id"], str(integration.id))
            self.assertEqual(response.data["email"], integration.email)
            self.assertEqual(response.data["has_integration"], True)


class GoogleCalendarIntegrationTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.start_integration_url = reverse(
            "integrations:start_google_calendar_integration",
        )
        return super().setUp()

    def test_unauthorized_access(self):
        self.client.force_authenticate(user=None)
        response = self.client.get(self.start_integration_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        retreive_integration_url = reverse(
            "integrations:calendar_integration",
        )

        response = self.client.get(retreive_integration_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch(
        "integrations.services.google_calendar_integration.GoogleCalendarIntegrationService.start_integration"
    )
    def test_start_integration(self, mock_start_integration):
        self.client.force_authenticate(user=self.user)
        mock_start_integration.return_value = "https://google.com"

        response = self.client.get(self.start_integration_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["redirect_url"], "https://google.com")

    @patch(
        "integrations.services.google_calendar_integration.GoogleCalendarIntegrationService.get_refresh_and_access_token"
    )
    def test_google_calendar_integration_callback(
        self, mock_get_refresh_and_access_token
    ):
        url = reverse("integrations:google")

        state_data = f"{self.user.id},calendar"

        mock_get_refresh_and_access_token.return_value = {
            "credentials": json.dumps({}),
            "email": "<EMAIL>",
        }

        response = self.client.get(url, data={"state": state_data})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(self.user.googlecalendarintegration_set.count(), 1)
        self.assertEqual(
            self.user.googlecalendarintegration_set.first().email,
            "<EMAIL>",
        )

    def test_delete_integration(self):
        self.client.force_authenticate(user=self.user)

        GoogleCalendarIntegrationFactory(
            user=self.user, credentials=json.dumps({"token": "test_token"})
        )

        with patch(
            "integrations.services.google_calendar_integration.GoogleCalendarIntegrationService.validate_token"
        ) as mock_validate:
            mock_validate.return_value = True

            delete_integration_url = reverse(
                "integrations:calendar_integration"
            )
            self.assertEqual(
                self.user.googlecalendarintegration_set.count(), 1
            )

            response = self.client.delete(delete_integration_url)
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            self.assertEqual(
                self.user.googlecalendarintegration_set.count(), 0
            )

    def test_retrieve_integration(self):
        self.client.force_authenticate(user=self.user)

        integration = GoogleCalendarIntegrationFactory(
            user=self.user, credentials=json.dumps({"token": "test_token"})
        )

        with patch(
            "integrations.services.google_calendar_integration.GoogleCalendarIntegrationService.validate_token"
        ) as mock_validate:
            mock_validate.return_value = True

            retrieve_integration_url = reverse(
                "integrations:calendar_integration"
            )
            response = self.client.get(retrieve_integration_url)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data["id"], str(integration.id))
            self.assertEqual(response.data["email"], integration.email)
            self.assertEqual(response.data["has_integration"], True)
