import factory

from .models import GoogleIntegration, GoogleCalendarIntegration


class GoogleIntegrationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = GoogleIntegration

    user = factory.SubFactory("users.factories.UserFactory")
    credentials = factory.Faker("pydict", value_types=["str", "int", "bool"])
    email = factory.Faker("email")


class GoogleCalendarIntegrationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = GoogleCalendarIntegration

    user = factory.SubFactory("users.factories.UserFactory")
    credentials = factory.Faker("pydict", value_types=["str", "int", "bool"])
    email = factory.Faker("email")