from core.serializers import TimezoneConverterMixin
from rest_framework import serializers

from .models import GoogleCalendarIntegration
from .models import GoogleIntegration


class IntegrationSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    has_integration = serializers.SerializerMethodField()

    class Meta:
        model = GoogleIntegration
        exclude = ("is_deleted", "credentials", "user")

    def get_has_integration(self, obj):
        return obj is not None


class CalendarIntegrationSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    email = serializers.EmailField(read_only=True)  # Include, but as read-only
    has_integration = serializers.SerializerMethodField()

    class Meta:
        model = GoogleCalendarIntegration
        exclude = (
            "is_deleted",
            "credentials",
            "user",
        )  # Exclude sensitive fields

    def get_has_integration(self, obj):
        return obj is not None
