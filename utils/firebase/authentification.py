from datetime import datetime

import firebase_admin
from accounts.models import User
from core.dependency_injection import service_locator
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.core.exceptions import ValidationError
from django.db import transaction
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from firebase_admin import auth as firebase_auth
from general.cache_keys import REDIS_CACHE_KEY
from google.auth import jwt
from rest_framework.authentication import BaseAuthentication
from rest_framework.authentication import get_authorization_header
from rest_framework.exceptions import AuthenticationFailed
from utils.firebase import tuulbox_firebase_auth


class SimpleTokenAuthentication(BaseAuthentication):
    """
    Simple token based authentication.

    Clients should authenticate by passing the JWT token in the "Authorization"
    HTTP header, prepended with the string "TOKEN_NAME ".  For example:

    Authorization: TOKEN_NAME 401f7ac837da42b97f613d789819ff93537bee6a
    """

    keyword = ""
    msg = _("The Authentication is required.")

    """
    A custom token model may be used, but must have the following properties.

    * key -- The string identifying the token
    * user -- The user to which the token belongs
    """

    @classmethod
    def authenticate(cls, request):
        auth = get_authorization_header(request).split()
        auth = cls.extract_token(auth)

        if not auth:
            raise AuthenticationFailed(cls.msg)

        return cls.authenticate_credentials(auth[1])

    @classmethod
    def extract_token(cls, auth):
        if not auth or auth[0].lower() != cls.keyword.lower().encode():
            return None

        if len(auth) == 1:
            msg = _("Invalid token header. No credentials provided.")
            raise AuthenticationFailed(msg)
        elif len(auth) > 2:
            msg = _(
                "Invalid token header. Token string should not contain spaces."
            )
            raise AuthenticationFailed(msg)

        return auth

    @classmethod
    def authenticate_header(cls, request):
        return cls.keyword

    @classmethod
    def authenticate_credentials(cls, token):
        raise NotImplementedError

    @classmethod
    def get_django_user(cls, user_record):
        """
        Returns a django user associated with the requesting firebase user.
        As we don't know how you intent on associating a firebase user with
        your local django users, this method must be implemented in a subclass.
        This method takes a firebase UserRecord object as parameter. You may
        use its properties to find or create a local django user.
        Return None in case you want the authentication process to fail.
        eg. when the requesting users email address is not verified.
        """
        raise NotImplementedError


class FirebaseAuthentication(SimpleTokenAuthentication):
    """
    Simple token based authentication.

    Clients should authenticate by passing the JWT token in the "Authorization"
    HTTP header, prepended with the string "Firebase ".  For example:

    Authorization: Firebase 401f7ac837da42b97f613d789819ff93537bee6a
    """

    keyword = "Firebase"
    check_revoked = True
    msg = _("The Firebase Authentication is required.")

    """
    A custom token model may be used, but must have the following properties.

    * key -- The string identifying the token
    * user -- The user to which the token belongs
    """

    @classmethod
    def authenticate_credentials(cls, firebase_token):
        try:
            decoded_token = tuulbox_firebase_auth.verify_id_token(
                firebase_token,
                check_revoked=cls.check_revoked,
            )

        except (ValueError, firebase_auth.InvalidIdTokenError) as err:
            # Token was either not a string or empty or not an valid Firebase ID token

            msg = _("The Firebase token was invalid.")
            raise AuthenticationFailed(msg) from err

        except firebase_auth.CertificateFetchError:
            msg = _("Temporarily unable to verify the ID token.")
            raise AuthenticationFailed(
                msg
            ) from firebase_auth.CertificateFetchError

        firebase_uid = decoded_token["uid"]
        firebase_user_record = tuulbox_firebase_auth.get_user(
            firebase_uid,
        )

        # This template method must be implemented in a subclass.
        user = cls.get_django_user(firebase_user_record)

        if user is None:
            msg = _("No matching local user found.")
            raise AuthenticationFailed(msg) from None

        return (user, firebase_token)

    @classmethod
    def get_firebase_app(cls, firebase_token):
        """
        This method must be implemented in a subclass of this class. It must
        return a valid Firebase App instance.
        Firebase App docs:
        https://firebase.google.com/docs/reference/admin/python/firebase_admin#app
        """
        payload = jwt.decode(firebase_token, verify=False)
        return firebase_admin.get_app(payload["aud"])

    @classmethod
    def get_django_user(cls, firebase_user_record: firebase_auth.UserRecord):
        """
        Firebase UserRecord docs:
        https://firebase.google.com/docs/reference/admin/python/firebase_admin.auth#userrecord
        """
        if firebase_user_record.email is None:
            msg = "The Firebase user has no Email address."
            raise AuthenticationFailed(msg)
        first_name = ""
        last_name = ""
        if firebase_user_record.display_name:
            name_parts = firebase_user_record.display_name.split(" ", 1)
            first_name = name_parts[0]
            last_name = name_parts[1] if len(name_parts) > 1 else ""
        try:

            user = get_user_model().objects.get(
                email=firebase_user_record.email
            )

        except ObjectDoesNotExist:
            user = get_user_model().objects.create(
                email=firebase_user_record.email,
                first_name=first_name,
                last_name=last_name,
                is_active=False,
            )
        # Ensure the names are updated in case they were empty before
        updated = False
        if not user.first_name and first_name:
            user.first_name = first_name

            updated = True
        if not user.last_name and last_name:
            user.last_name = last_name
            updated = True
        if updated:
            user.save()
        cls.update_django_user_last_login(user.email)
        return user

    @classmethod
    def get_firebase_user_by_email(cls, email):

        try:
            return tuulbox_firebase_auth.get_user_by_email(email)
        except Exception:
            return None

    @classmethod
    def get_last_login_from_firebase_by_email(cls, email, default=None):

        try:
            user = cls.get_firebase_user_by_email(email)
            if not user or not user.user_metadata:
                return default

            # Prefer last_sign_in_timestamp first
            if ts := user.user_metadata.last_sign_in_timestamp:
                return datetime.fromtimestamp(
                    ts / 1000
                )  # Convert milliseconds to seconds

            # Fall back to other timestamps
            for ts in [
                user.user_metadata.last_refresh_timestamp,
                user.user_metadata.creation_timestamp,
            ]:
                if ts:
                    return datetime.fromtimestamp(ts / 1000)

            return default

        except Exception:
            return default

    @classmethod
    def activate_user_if_firebase_is_verified(cls, user):

        firebase_user = cls.get_firebase_user_by_email(user.email)
        if (
            not firebase_user
            or not firebase_user.email_verified
            or firebase_user.disabled
        ):
            return

        try:

            if not user.is_active:
                user.is_active = True
                user.save(update_fields=["is_active"])
        except User.DoesNotExist:
            pass

    @classmethod
    def update_django_user_last_login(cls, email):

        try:

            try:
                django_user = get_user_model().objects.get(email=email)
            except get_user_model().DoesNotExist:

                return False

            django_user.last_login = timezone.now()
            django_user.save(update_fields=["last_login"])

            return True
        except Exception:

            return False

    @classmethod
    @transaction.atomic
    def create_firebase_user(
        cls,
        email: str,
        password: str,
        request: str,
        display_name: str = "",
        phone_number: str = None,
    ):
        user_data = {
            "email": email,
            "password": password,
            "email_verified": False,
        }
        if display_name:
            user_data["display_name"] = display_name

        try:
            firebase_user = tuulbox_firebase_auth.create_user(**user_data)
            first_name = ""
            last_name = ""
            if display_name:
                name_parts = display_name.split(" ", 1)
                first_name = name_parts[0]
                last_name = name_parts[1] if len(name_parts) > 1 else ""

            django_user: User = User.objects.create(
                email=email,
                first_name=first_name,
                last_name=last_name,
                mobile=phone_number,
                is_active=False,
            )

            user_dict = {
                "email": firebase_user.email,
                "first_name": first_name,
                "last_name": last_name,
                "phone_number": firebase_user.phone_number,
            }

            import uuid
            from django.core.cache import cache

            verification_token = str(uuid.uuid4())

            token_cache_key = REDIS_CACHE_KEY.get_email_verification_key(
                django_user.id
            )
            user_id_cache_key = f"token_to_user_id_{verification_token}"

            timeout = (
                service_locator.general_service.app_setting.account_verification_timeout_seconds
                or 86400
            )

            cache.set(token_cache_key, verification_token, timeout=timeout)
            cache.set(user_id_cache_key, django_user.id, timeout=timeout)

            verify_email_url = (
                reverse("verify_email_view")
                + f"?token={verification_token}&user_id={django_user.id}"
            )

            account_verification_url = request.build_absolute_uri(
                verify_email_url
            )

            service_locator.core_service.send_email(
                template_path="emails/email_verification.html",
                subject="Verify Your Email Address",
                template_context={
                    "account_verification_url": account_verification_url,
                    "user": django_user.full_name,
                    "user_id": django_user.id,
                    "play_store_app_url": settings.PLAY_STORE_APP_URL,
                    "app_store_app_url": settings.APP_STORE_APP_URL,
                    "current_year": datetime.now().year,
                },
                to_emails=[django_user.email],
            )

            return user_dict, django_user

        except firebase_auth.EmailAlreadyExistsError as e:
            raise ValidationError(_("Email already exists")) from e

        except Exception as e:
            raise AuthenticationFailed(
                _(f"Firebase user creation failed: {str(e)}")
            ) from e

    @classmethod
    def activate_firebase_user(cls, email: str):
        try:

            user = cls.get_firebase_user_by_email(email)

            firebase_user = tuulbox_firebase_auth.update_user(
                uid=user.uid, email_verified=True
            )

            return firebase_user

        except firebase_auth.UserNotFoundError as e:
            raise ValidationError(
                _("User with the specified email does not exist")
            ) from e
        except Exception as e:
            raise AuthenticationFailed(
                _(f"Failed to verify email: {str(e)}")
            ) from e
