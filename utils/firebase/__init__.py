from dataclasses import dataclass
from google.oauth2 import service_account
from google.analytics.data import BetaAnalyticsDataClient
import json
from pathlib import PosixPath

import firebase_admin
from django.conf import settings
from firebase_admin import auth
from firebase_admin import credentials

firebase_cred = settings.FIREBASE_CRED_FILE


def initialize_app_from_config(config: PosixPath):
    config = json.loads(config.read_text())

    firebase_app = firebase_admin.initialize_app(
        credential=credentials.Certificate(firebase_cred),
        name=config["project_id"],
        options={
            "projectId": config["project_id"],
        },
    )

    firebase_auth = auth.Client(app=firebase_app)
    return firebase_app, firebase_auth


tuulbo_firebase_app, tuulbox_firebase_auth = initialize_app_from_config(
    firebase_cred
)


@dataclass
class AnalyticsConfig:
    property_id: str = settings.GOOGLE_ANALYTICS_PROPERTY_ID
    credentials_path: str = firebase_cred


class AnalyticsReporter:
    def __init__(self, config: AnalyticsConfig):
        self.property_id = config.property_id
        self.credentials = service_account.Credentials.from_service_account_file(
            config.credentials_path
        )
        self.client = BetaAnalyticsDataClient(credentials=self.credentials)
