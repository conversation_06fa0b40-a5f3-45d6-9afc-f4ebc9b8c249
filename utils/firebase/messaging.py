import firebase_admin
from firebase_admin import messaging


class FirebaseMessageService:
    def __init__(self):
        self.messaging = messaging

    def send_message_to_topic(self, message: messaging.Message):
        from utils.firebase import tuulbo_firebase_app

        try:
            firebase_admin.initialize_app(tuulbo_firebase_app.credential)
        except ValueError:
            pass

        response = self.messaging.send(message, dry_run=False)
        return response
