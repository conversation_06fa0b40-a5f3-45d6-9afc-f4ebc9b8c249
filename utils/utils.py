import typing

from calendar_app.models import CalendarEvent
from calendar_app.serializers import CalendarEventSerializer
from chat.services.ktg_chat_client import ChatClient
from chat.services.ktg_chat_client import ChatClientConfig
from company.models import Insurance
from company.models import Licenses
from company.serializers import InsuranceSerializer
from company.serializers import LicensesSerializer
from contacts.models import Contact
from contacts.models import Subcontractor
from contacts.serializers import ContactSerializer
from contacts.serializers import SubcontractSerializer
from core.constants import Features
from django.conf import settings
from notifications.models import Notification
from project.models import ChangeOrder
from project.models import Project
from project.models import ProjectDocument
from project.serializers import ChangeOrderSerializer
from project.serializers import ProjectDocumentSerializer
from project.serializers import ProjectListSerializer
from resources.models import Resource
from resources.serializers import ResourceSerializer
from storage.models import File
from storage.serializers import FileSerializer

if typing.TYPE_CHECKING:
    from django.db import models
    from rest_framework import serializers


# Function to get model and serializer based on category


def get_model_and_serializer(
    category,
) -> tuple["models.Model", "serializers.ModelSerializer"]:
    """
    Return the model class and serializer class based on the category.
    """
    category_mapping = {
        Features.CONTACTS: (Contact, ContactSerializer),
        Features.SUBCONTRACTORS: (Subcontractor, SubcontractSerializer),
        Features.RESOURCES: (Resource, ResourceSerializer),
        Features.STORAGES: (File, FileSerializer),
        Features.PROJECTS: (Project, ProjectListSerializer),
        Features.PLAN_AND_ELEVATIONS: (
            ProjectDocument,
            ProjectDocumentSerializer,
        ),
        Features.ESTIMATES: (ProjectDocument, ProjectDocumentSerializer),
        Features.CONTRACTS: (ProjectDocument, ProjectDocumentSerializer),
        Features.CHANGE_ORDERS: (ChangeOrder, ChangeOrderSerializer),
        Features.PAYMENT_SCHEDULES: (
            ProjectDocument,
            ProjectDocumentSerializer,
        ),
        Features.PERFORMANCE_SCHEDULES: (
            ProjectDocument,
            ProjectDocumentSerializer,
        ),
        Features.SPECIFICATIONS: (ProjectDocument, ProjectDocumentSerializer),
        Features.PERMITS: (ProjectDocument, ProjectDocumentSerializer),
        Features.ADDITIONAL_DOCUMENTS: (
            ProjectDocument,
            ProjectDocumentSerializer,
        ),
        Features.GALLERY: (ProjectDocument, ProjectDocumentSerializer),
        Features.LICENSES: (Licenses, LicensesSerializer),
        Features.INSURANCES: (Insurance, InsuranceSerializer),
        Features.CALENDAR_EVENTS: (CalendarEvent, CalendarEventSerializer),
    }

    return category_mapping.get(category, (None, None))


def create_or_update_notification(
    user, category, item_id, message: str = None
):
    """
    Creates a new notification or updates an existing one.
    Returns a tuple of (notification, created) where created is a boolean.
    """

    notification, created = Notification.objects.get_or_create(
        user=user,
        category=category,
        item_id=item_id,
        message=message,
    )

    if not created:
        # Update the existing notification
        notification.is_read = False
        notification.save()

    return notification, created


chat_client = ChatClient(
    ChatClientConfig(
        base_url=settings.CHAT_API_BASE_URL,
        organisation_token=settings.CHAT_ORGANISATION_TOKEN,
    )
)
