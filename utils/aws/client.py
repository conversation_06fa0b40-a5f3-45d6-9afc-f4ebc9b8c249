import logging
from dataclasses import dataclass
from io import By<PERSON><PERSON>
from typing import Any
from typing import Dict
from typing import Optional

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError
from django.conf import settings

from ..common import assert_settings


@dataclass()
class S3Credentials:
    access_key_id: str
    secret_access_key: str
    region_name: str
    bucket_name: str
    default_acl: str
    presigned_expiry: int
    max_size: int


def s3_get_credentials() -> S3Credentials:
    required_config = assert_settings(
        [
            "AWS_ACCESS_KEY_ID",
            "AWS_SECRET_ACCESS_KEY",
            "AWS_S3_REGION_NAME",
            "AWS_STORAGE_BUCKET_NAME",
            "AWS_DEFAULT_ACL",
            "AWS_PRESIGNED_EXPIRY",
            "FILE_MAX_SIZE",
        ],
        "S3 credentials not found.",
    )

    return S3Credentials(
        access_key_id=required_config["AWS_ACCESS_KEY_ID"],
        secret_access_key=required_config["AWS_SECRET_ACCESS_KEY"],
        region_name=required_config["AWS_S3_REGION_NAME"],
        bucket_name=required_config["AWS_STORAGE_BUCKET_NAME"],
        default_acl=required_config["AWS_DEFAULT_ACL"],
        presigned_expiry=required_config["AWS_PRESIGNED_EXPIRY"],
        max_size=required_config["FILE_MAX_SIZE"],
    )


def s3_get_client():
    credentials = s3_get_credentials()

    return boto3.client(
        service_name="s3",
        aws_access_key_id=credentials.access_key_id,
        aws_secret_access_key=credentials.secret_access_key,
        region_name=credentials.region_name,
    )


def s3_resource_client():
    credentials = s3_get_credentials()

    return boto3.resource(
        service_name="s3",
        aws_access_key_id=credentials.access_key_id,
        aws_secret_access_key=credentials.secret_access_key,
        region_name=credentials.region_name,
    )


def s3_generate_presigned_post(
    *, file_path: str, file_type: str
) -> Dict[str, Any]:
    credentials = s3_get_credentials()
    s3_client = s3_get_client()

    acl = credentials.default_acl
    expires_in = credentials.presigned_expiry

    presigned_data = s3_client.generate_presigned_post(
        credentials.bucket_name,
        file_path,
        Fields={"acl": acl, "Content-Type": file_type},
        Conditions=[
            {"acl": acl},
            {"Content-Type": file_type},
            # As an example, allow file size up to 10 MiB
            # More on conditions, here:
            # https://docs.aws.amazon.com/AmazonS3/latest/API/sigv4-HTTPPOSTConstructPolicy.html
            ["content-length-range", 1, credentials.max_size],
        ],
        ExpiresIn=expires_in,
    )

    return presigned_data


def create_presigned_url(
    object_name,
    bucket_name=None,
    allow_download=False,
    presigned_expiry: int = None,
):
    """Generate a presigned URL to share an S3 object

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string. If error, returns None.
    """

    if settings.TEST_DEBUG:
        return

    credentials = s3_get_credentials()

    # update expiry if provided
    if presigned_expiry:
        credentials.presigned_expiry = presigned_expiry

    s3_client = boto3.client(
        config=Config(
            s3={"addressing_style": "path"}, signature_version="s3v4"
        ),
        service_name="s3",
        aws_access_key_id=credentials.access_key_id,
        aws_secret_access_key=credentials.secret_access_key,
        region_name=credentials.region_name,
    )

    credentials = s3_get_credentials()

    # Generate a presigned URL for the S3 object
    params = {"Bucket": credentials.bucket_name, "Key": object_name}
    # Add Content-Disposition header to force download
    if allow_download:
        params[
            "ResponseContentDisposition"
        ] = f'attachment; filename="{object_name.split("/")[-1]}"'
    try:
        response = s3_client.generate_presigned_url(
            "get_object",
            Params=params,
            ExpiresIn=credentials.presigned_expiry,
        )
    except ClientError as e:
        logging.error(e)
        return None
    return response


def get_file_extension(object_name: str, default=None) -> str:

    if settings.TEST_DEBUG:
        return

    credentials = s3_get_credentials()
    s3_client = s3_get_client()
    try:
        response = s3_client.head_object(
            Bucket=credentials.bucket_name, Key=object_name
        )
        content_type = response["ContentType"]
        return content_type.split("/")[-1]
    except ClientError:
        return default


def get_file_path(object_name: str) -> Optional[str]:
    credentials = s3_get_credentials()
    if file_exists(object_name):
        return (
            f"https://{credentials.bucket_name}.s3.amazonaws.com/{object_name}"
        )
    return None


def get_file_url(object_name: str) -> Optional[str]:
    try:
        credentials = s3_get_credentials()
        return (
            f"https://{credentials.bucket_name}.s3.amazonaws.com/{object_name}"
        )
    except Exception as e:
        logging.error(f"Failed to generate URL for {object_name}: {e}")
        return None


def get_file_content(object_name: str) -> Optional[bytes]:
    credentials = s3_get_credentials()
    s3_client = s3_get_client()
    try:
        response = s3_client.get_object(
            Bucket=credentials.bucket_name, Key=object_name
        )
        return response["Body"].read()
    except ClientError as e:
        logging.error(
            "Failed to fetch file %s: %s",
            object_name,
            e.response["Error"]["Message"],
        )
        return None


def file_exists(key: str) -> bool:
    credentials = s3_get_credentials()
    s3_client = s3_get_client()
    try:
        s3_client.head_object(Bucket=credentials.bucket_name, Key=key)
        return True
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False
        raise


def delete_file(file_path: str) -> bool:
    try:
        s3_client = s3_get_client()
        credentials = s3_get_credentials()
        s3_client.delete_object(Bucket=credentials.bucket_name, Key=file_path)
        return True
    except ClientError as e:
        logging.error(f"Failed to delete file from S3: {e}")
        return False


def upload_fileobj(
    fileobj: BytesIO,
    object_name: str,
    content_type: str,
    acl: Optional[str] = None,
) -> bool:
    try:

        s3_client = s3_get_client()
        credentials = s3_get_credentials()

        acl = acl or credentials.default_acl

        s3_client.upload_fileobj(
            fileobj,
            credentials.bucket_name,
            object_name,
            ExtraArgs={"ContentType": content_type, "ACL": acl},
        )

        logging.info(f"Uploaded file-like object to {object_name}")
        return True
    except ClientError as e:
        logging.error(
            "Failed to upload file-like object to %s: %s",
            object_name,
            e.response["Error"]["Message"],
        )

        return False
