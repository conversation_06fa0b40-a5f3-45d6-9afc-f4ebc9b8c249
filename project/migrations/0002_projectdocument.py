# Generated by Django 3.2.17 on 2023-07-04 15:15
import uuid

import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("storage", "0001_initial"),
        ("resources", "0003_alter_tag_name"),
        ("project", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("plan_and_elevation", "Plan and Elevation"),
                            ("estimate", "Estimate"),
                            ("contract", "Contract"),
                            ("change_order", "Change Order"),
                            ("payment_schedule", "Payment Schedule"),
                            ("performance_schedule", "Performance Schedule"),
                            ("specification", "Specification"),
                            ("additional_documents", "Additional Documents"),
                        ],
                        max_length=255,
                    ),
                ),
                (
                    "file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="documents",
                        to="storage.file",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="project.project",
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(blank=True, to="resources.Tag"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
