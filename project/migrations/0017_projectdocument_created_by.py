# Generated by Django 3.2.17 on 2024-10-03 13:51
import django.db.models.deletion
from django.conf import settings
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):
    def update_project_document_created_by(apps, schema_editor):
        ProjectDocument = apps.get_model("project", "ProjectDocument")

        for document in ProjectDocument.objects.all():
            if document.created_by is None:
                document.created_by = document.project.created_by
                document.save()

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("project", "0016_projectinvite_status_expiration"),
    ]

    operations = [
        migrations.AddField(
            model_name="projectdocument",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.RunPython(update_project_document_created_by),
    ]
