# Generated by Django 3.2.17 on 2024-10-09 10:43
import uuid

import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("project", "0017_projectdocument_created_by"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="projectdocument",
            name="created_by",
        ),
        migrations.CreateModel(
            name="ChangeOrder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("scope_of_work", models.TextField()),
                (
                    "amount",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "visibility",
                    models.Char<PERSON>ield(
                        choices=[("public", "Public"), ("private", "Private")],
                        default="public",
                        max_length=50,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="project.project",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
