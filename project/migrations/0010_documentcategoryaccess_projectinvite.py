# Generated by Django 3.2.17 on 2024-07-10 01:26
import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("project", "0009_projectimage"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectInvite",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=255,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="project.project",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DocumentCategoryAccess",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "document_category",
                    models.CharField(
                        choices=[
                            ("plan_and_elevation", "Plan and Elevation"),
                            ("estimate", "Estimate"),
                            ("contract", "Contract"),
                            ("change_order", "Change Order"),
                            ("payment_schedule", "Payment Schedule"),
                            ("performance_schedule", "Performance Schedule"),
                            ("specification", "Specification"),
                            ("permit", "Permit"),
                            ("additional_documents", "Additional Documents"),
                        ],
                        max_length=255,
                    ),
                ),
                (
                    "access_level",
                    models.CharField(
                        choices=[
                            ("read", "Read"),
                            ("write", "Write"),
                            ("no_access", "No Access"),
                        ],
                        default="no_access",
                        max_length=255,
                    ),
                ),
                (
                    "project_invite",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_category_accesses",
                        to="project.projectinvite",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
