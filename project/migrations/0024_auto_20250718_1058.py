# Generated by Django 3.2.17 on 2025-07-18 10:58
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("project", "0023_auto_20250718_1033"),
    ]

    operations = [
        migrations.AlterField(
            model_name="documentcategoryaccess",
            name="document_category",
            field=models.CharField(
                choices=[
                    ("plan_and_elevation", "Plan and Elevation"),
                    ("estimate", "Estimate"),
                    ("contract", "Contract"),
                    ("change_order", "Change Order"),
                    ("payment_schedule", "Payment Schedule"),
                    ("performance_schedule", "Performance Schedule"),
                    ("specification", "Specification"),
                    ("permit", "Permit"),
                    ("additional_documents", "Additional Documents"),
                    ("gallery", "Gallery"),
                    ("communication", "Communication"),
                    ("invoice", "Invoice"),
                ],
                max_length=255,
            ),
        ),
        migrations.AlterField(
            model_name="projectdocument",
            name="category",
            field=models.Char<PERSON>ield(
                choices=[
                    ("plan_and_elevation", "Plan and Elevation"),
                    ("estimate", "Estimate"),
                    ("contract", "Contract"),
                    ("change_order", "Change Order"),
                    ("payment_schedule", "Payment Schedule"),
                    ("performance_schedule", "Performance Schedule"),
                    ("specification", "Specification"),
                    ("permit", "Permit"),
                    ("additional_documents", "Additional Documents"),
                    ("gallery", "Gallery"),
                    ("communication", "Communication"),
                    ("invoice", "Invoice"),
                ],
                max_length=255,
            ),
        ),
    ]
