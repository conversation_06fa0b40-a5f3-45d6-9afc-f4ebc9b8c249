# Generated by Django 3.2.17 on 2024-07-23 12:33
import uuid

import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("project", "0013_project_project_logo"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectShare",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("user_emails", models.J<PERSON><PERSON>ield(default=list)),
                (
                    "permission_id",
                    models.CharField(
                        blank=True,
                        editable=False,
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="project.project",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="documentcategoryaccess",
            name="project_share",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="document_category_accesses",
                to="project.projectshare",
            ),
        ),
    ]
