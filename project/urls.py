from django.urls import path

from .views import BulkDeleteProjectDocumentsView
from .views import CreateListChangeOrderView
from .views import CreateListProjectDocumentView
from .views import CreateListProjectView
from .views import CreateProjectInviteView
from .views import CreateProjectShareView
from .views import GenerateChangeOrderPDFView
from .views import ListInvitationsReceivedView
from .views import ListInvitationsSentView
from .views import ListShareSentView
from .views import ProjectCollaboratorsView
from .views import ProjectInviteStatusUpdateView
from .views import ProjectSubcontractorView
from .views import RetrieveProjectByPermissionIdView
from .views import RetrieveUpdateDestroyChangeOrderView
from .views import RetrieveUpdateDestroyInvitationView
from .views import RetrieveUpdateDestroyProjectDocumentView
from .views import RetrieveUpdateDestroyProjectShareView
from .views import RetrieveUpdateDestroyProjectView

app_name = "project"
urlpatterns = [
    path(
        "",
        CreateListProjectView.as_view(),
        name="project",
    ),
    path(
        "<pk>/",
        RetrieveUpdateDestroyProjectView.as_view(),
        name="project",
    ),
    path(
        "<pk>/collaborators/",
        ProjectCollaboratorsView.as_view(),
        name="project-collaborators",
    ),
    path(
        "<str:project_id>/documents/",
        CreateListProjectDocumentView.as_view(),
        name="project_document",
    ),
    path(
        "<str:project_id>/documents/<pk>/",
        RetrieveUpdateDestroyProjectDocumentView.as_view(),
        name="project_document",
    ),
    path(
        "documents/bulk-delete/",
        BulkDeleteProjectDocumentsView.as_view(),
        name="bulk_delete_documents",
    ),
    path(
        "<uuid:pk>/subcontractors/",
        ProjectSubcontractorView.as_view(),
        name="project_subcontractors",
    ),
    path(
        "<str:project_id>/invite/",
        CreateProjectInviteView.as_view(),
        name="create_project_invite",
    ),
    path(
        "invitations/received/",
        ListInvitationsReceivedView.as_view(),
        name="list_invitations_received",
    ),
    path(
        "<str:project_id>/invitations/sent/",
        ListInvitationsSentView.as_view(),
        name="list_invitations_sent",
    ),
    path(
        "invitation/<uuid:invite_id>/",
        RetrieveUpdateDestroyInvitationView.as_view(),
        name="retrieve_update_destroy_invitation",
    ),
    path(
        "status/<uuid:pk>/",
        ProjectInviteStatusUpdateView.as_view(),
        name="invite_status",
    ),
    path(
        "<str:project_id>/share/",
        CreateProjectShareView.as_view(),
        name="create_project_share",
    ),
    path(
        "share/<uuid:share_id>/",
        RetrieveUpdateDestroyProjectShareView.as_view(),
        name="retrieve_update_destroy_share",
    ),
    path(
        "<str:project_id>/share/sent/",
        ListShareSentView.as_view(),
        name="list_share_sent",
    ),
    path(
        "project-by-permission/<str:permission_id>/",
        RetrieveProjectByPermissionIdView.as_view(),
        name="project-by-permission",
    ),
    path(
        "projects/<uuid:project_id>/change-orders/",
        CreateListChangeOrderView.as_view(),
        name="change-order-list-create",
    ),
    path(
        "projects/<uuid:project_id>/change-orders/<uuid:pk>/",
        RetrieveUpdateDestroyChangeOrderView.as_view(),
        name="change-order-detail",
    ),
    path(
        "projects/<uuid:project_id>/change-orders/<uuid:change_order_id>/pdf/",
        GenerateChangeOrderPDFView.as_view(),
        name="generate-change-order-pdf",
    ),
]
