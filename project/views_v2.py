import logging

from accounts.models import User
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from project.filters import ProjectDocumentFilter
from project.models import Project
from project.serializers import InvitedUserSerializer
from project.serializers import ListProjectDocumentSerializer
from project.views import BaseProjectDocumentView
from rest_framework import filters
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated


logger = logging.getLogger(__name__)


class ListProjectDocumentViewV2(BaseProjectDocumentView, ListAPIView):
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = ProjectDocumentFilter
    search_fields = ["category", "name", "visibility"]
    cache_version = "v2"

    def get_serializer_class(self):
        return ListProjectDocumentSerializer

    def list(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        # TODO: Find a better way to alway regenerate cache in the background. Disable the caching for now.

        # Get project_id from URL kwargs
        # project_id = kwargs.get("project_id")
        # (
        #     cached_response,
        #     _,
        # ) = service_locator.project_service.get_cached_project_document_response_with_fallback(
        #     request, project_id, cache_version=self.cache_version
        # )
        # if cached_response is not None:
        #     return Response(cached_response)

        # # Cache miss or cache disabled - get fresh data
        # logger.debug(
        #     f"Cache miss for project document list - user: {request.user.id}"
        # )

        return self._get_and_cache_response(
            request, service_locator, self.cache_version, *args, **kwargs
        )

    def _get_and_cache_response(
        self, request, service_locator, cache_version, *args, **kwargs
    ):
        """Get response and cache it."""
        from core.dependency_injection import service_locator

        response = super().list(request, *args, **kwargs)

        # Only cache successful responses
        if response.status_code == 200:
            # Get project_id from URL kwargs
            project_id = kwargs.get("project_id")
            service_locator.project_service.cache_project_document_response_safe(
                request, response.data, project_id, cache_version=cache_version
            )

        return response


class ProjectCollaboratorsViewV2(ListAPIView):
    serializer_class = InvitedUserSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    ordering_fields = [
        "first_name",
        "last_name",
        "email",
        "mobile",
        "created_at",
    ]

    def get_queryset(self):
        project_id = self.kwargs.get("pk")

        project = get_object_or_404(Project, pk=project_id)
        # Get all invited users who accepted
        invited_users = User.objects.filter(
            projectinvite__project=project, projectinvite__status="accepted"
        )

        return invited_users
