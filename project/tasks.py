import logging

from celery import shared_task
from django.contrib.auth import get_user_model
from project.models import Project

logger = logging.getLogger(__name__)


User = get_user_model()


@shared_task()
def regenerate_project_cache(user_id):
    """
    Regenerate project cache for a specific user in the background

    Args:
        user_id (int): ID of the user whose project cache needs regeneration
    """
    try:
        from core.dependency_injection import service_locator

        user = User.objects.get(id=user_id)

        created_projects = Project.objects.filter(
            created_by=user, is_deleted=False
        ).order_by("created_at")

        invited_projects = Project.objects.filter(
            projectinvite__user=user,
            projectinvite__status="accepted",
            is_deleted=False,
        ).order_by("created_at")

        queryset = created_projects | invited_projects
        queryset = queryset.distinct()

        service_locator.project_service.cache_projects(user, queryset)

    except User.DoesNotExist:
        logger.error(
            f"User with ID {user_id} not found for cache regeneration"
        )
    except Exception as e:
        logger.error(f"Error regenerating project cache: {e}")
