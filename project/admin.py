# Register your models here.
from django.contrib import admin
from general.admin import ImportExportModelAdminMixin
from .models import Project, ProjectDocument, ProjectInvite


class ProjectAdmin(ImportExportModelAdminMixin):
    list_display = ("id", "name", "created_at", "updated_at", "created_by")
    list_display_links = ("id", "name")
    search_fields = ("name", "owner", 'created_by__email')
    list_filter = ("name", "owner", "created_by")
    list_per_page = 25


admin.site.register(Project, ProjectAdmin)


@admin.register(ProjectDocument)
class ProjectDocumentAdmin(ImportExportModelAdminMixin):
    search_fields = ['name', 'project__email']
    list_filter = ['project', 'subcontractor', 'subcontractor__contact__uploaded_by']


@admin.register(ProjectInvite)
class ProjectInviteAdmin(ImportExportModelAdminMixin):
    search_fields = ['user__email']
    list_filter = ['user', 'project']
