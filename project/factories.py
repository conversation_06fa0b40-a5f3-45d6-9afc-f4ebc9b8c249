import hashlib

import factory

from .models import ChangeOrder
from .models import DocumentCategoryAccess
from .models import Project
from .models import ProjectDocument
from .models import ProjectInvite
from .models import ProjectShare


class ProjectFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Project

    name = factory.Faker("name")
    owner = factory.Faker("name")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("secondary_address")
    city = factory.Faker("city")
    state = factory.Faker("state_abbr")
    zip_code = factory.Faker("zipcode")
    country = factory.Faker("country")
    contact = factory.Faker("phone_number")
    email = factory.Faker("email")
    created_by = factory.SubFactory("accounts.factories.UserFactory")
    status = factory.Iterator(
        Project.Status.ALL,
        getter=lambda c: c,
    )


class ProjectDocumentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ProjectDocument

    project = factory.SubFactory(ProjectFactory)
    name = factory.Faker("name")
    file = factory.SubFactory("storage.factories.FileFactory")
    category = factory.Iterator(
        ProjectDocument.Category.ALL,
        getter=lambda c: c,
    )


class ProjectInviteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ProjectInvite

    user = factory.SubFactory("accounts.factories.UserFactory")
    project = factory.SubFactory(ProjectFactory)
    status = "pending"


class ChangeOrderFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ChangeOrder

    project = factory.SubFactory(ProjectFactory)
    scope_of_work = factory.Faker("paragraph")
    amount = factory.Faker(
        "pydecimal", left_digits=5, right_digits=2, positive=True
    )
    visibility = factory.Iterator(
        ChangeOrder.Visibility.CHOICES,
        getter=lambda c: c[0],
    )


class ProjectShareFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ProjectShare

    project = factory.SubFactory(ProjectFactory)
    user_emails = factory.List([factory.Faker("email") for _ in range(3)])

    @factory.lazy_attribute
    def permission_id(self):
        document_category_accesses = (
            DocumentCategoryAccessFactory.create_batch(3)
        )
        access_data = [
            {
                "document_category": access.document_category,
                "access_level": access.access_level,
            }
            for access in document_category_accesses
        ]
        hash_input = f"{self.project.id}:" + ",".join(
            sorted(
                f"{access['document_category']}:{access['access_level']}"
                for access in access_data
            )
        )
        return hashlib.md5(hash_input.encode()).hexdigest()


class DocumentCategoryAccessFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = DocumentCategoryAccess

    document_category = factory.Iterator(
        DocumentCategoryAccess.DocumentCategory.CHOICES, getter=lambda c: c[0]
    )
    access_level = factory.Iterator(
        DocumentCategoryAccess.AccessLevel.CHOICES, getter=lambda c: c[0]
    )
    project_invite = factory.SubFactory(ProjectInviteFactory)
