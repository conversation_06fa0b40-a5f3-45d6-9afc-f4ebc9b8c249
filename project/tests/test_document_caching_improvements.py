"""
Tests for improved project document caching implementation.
"""
from unittest.mock import patch

from accounts.factories import UserFactory
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.test import override_settings
from django.test import TestCase
from project.factories import ProjectDocumentFactory
from project.factories import ProjectFactory
from project.services import ProjectService
from rest_framework import status
from rest_framework.test import APIClient

User = get_user_model()


class ProjectDocumentCachingImprovementsTestCase(TestCase):
    """Test cases for improved project document caching."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = UserFactory()
        self.other_user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)
        self.document = ProjectDocumentFactory(project=self.project)
        self.service = ProjectService()

        # Clear cache before each test
        cache.clear()

    def tearDown(self):
        """Clean up after each test."""
        cache.clear()

    def test_cache_key_generation_consistency(self):
        """Test that cache key generation is consistent and collision-resistant."""
        # Test with same parameters in different order
        params1 = {"search": "test", "ordering": "name"}
        params2 = {"ordering": "name", "search": "test"}

        key1 = self.service._generate_cache_key(
            self.user.id, params1, key_type="document_list"
        )
        key2 = self.service._generate_cache_key(
            self.user.id, params2, key_type="document_list"
        )

        # Keys should be identical regardless of parameter order
        self.assertEqual(key1, key2)

        # Test with different parameters
        params3 = {"search": "different"}

        key3 = self.service._generate_cache_key(
            self.user.id, params3, key_type="document_list"
        )

        # Keys should be different for different parameters
        self.assertNotEqual(key1, key3)

    @override_settings(
        CACHES={
            "default": {
                "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            }
        }
    )
    def test_cache_response_with_error_handling(self):
        """Test caching response with improved error handling."""
        from django.test import RequestFactory
        from rest_framework.request import Request

        factory = RequestFactory()
        django_request = factory.get("/documents/")
        django_request.user = self.user
        request = Request(django_request)

        # Explicitly set the user on the DRF request
        request._user = self.user

        # Test successful caching using service method
        response_data = {"results": [{"id": 1, "name": "test"}]}
        self.service.cache_project_document_response(
            request, response_data, self.project.id
        )

        # Retrieve cached data using service method
        cached_data = self.service.get_cached_project_document_response(
            request, self.project.id
        )
        self.assertEqual(cached_data, response_data)

        # Test that empty data is not cached by our service method
        # This should not overwrite existing cache
        self.service.cache_project_document_response(request, self.project.id)

        # Original cache should still exist
        cached_data = self.service.get_cached_project_document_response(
            request, self.project.id
        )
        self.assertEqual(cached_data, response_data)

    @patch("project.services.cache")
    def test_cache_error_handling(self, mock_cache):
        """Test error handling in cache operations."""
        from django.test import RequestFactory
        from rest_framework.request import Request

        factory = RequestFactory()
        django_request = factory.get("/documents/")
        django_request.user = self.user
        request = Request(django_request)

        # Mock cache to raise exception
        mock_cache.set.side_effect = Exception("Cache error")
        mock_cache.get.side_effect = Exception("Cache error")

        # Should not raise exception, should handle gracefully
        response_data = {"results": [{"id": 1, "name": "test"}]}
        self.service.cache_project_document_response(
            request, response_data, self.project.id
        )

        # Should return None on error
        result = self.service.get_cached_project_document_response(
            request, self.project.id
        )
        self.assertIsNone(result)

    @override_settings(
        CACHES={
            "default": {
                "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            }
        }
    )
    def test_cache_invalidation_for_project_members(self):
        """Test that cache is invalidated for all project members."""
        from django.test import RequestFactory
        from rest_framework.request import Request

        factory = RequestFactory()

        # Create requests with project-specific query parameters to ensure proper cache key generation
        django_request1 = factory.get(f"/project/{self.project.id}/documents/")
        django_request1.user = self.user
        request1 = Request(django_request1)
        request1._user = self.user

        django_request2 = factory.get(f"/project/{self.project.id}/documents/")
        django_request2.user = self.other_user
        request2 = Request(django_request2)
        request2._user = self.other_user

        response_data = {"results": [{"id": 1, "name": "test"}]}
        self.service.cache_project_document_response(
            request1, response_data, self.project.id
        )
        self.service.cache_project_document_response(
            request2, response_data, self.project.id
        )

        # Verify both caches exist
        cached_data1 = self.service.get_cached_project_document_response(
            request1, self.project.id
        )
        cached_data2 = self.service.get_cached_project_document_response(
            request2, self.project.id
        )

        self.assertIsNotNone(cached_data1)
        self.assertIsNotNone(cached_data2)

        # Invalidate cache for project
        self.service.invalidate_project_document_caches_for_project(
            self.project.id
        )

        # Both caches should be cleared (if Redis with pattern deletion is available)
        if hasattr(cache, "delete_pattern"):
            self.assertIsNone(
                self.service.get_cached_project_document_response(
                    request1, self.project.id
                )
            )
            self.assertIsNone(
                self.service.get_cached_project_document_response(
                    request2, self.project.id
                )
            )

    def test_api_endpoint_caching_behavior(self):
        """Test the API endpoint caching behavior."""
        self.client.force_authenticate(user=self.user)

        url = f"/api/project/{self.project.id}/documents/"

        # First request should hit database and cache result
        with patch(
            "core.dependency_injection.service_locator.project_service.cache_project_document_response"
        ) as mock_cache:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            # The cache method should be called when response is successful
            mock_cache.assert_called_once()

        # Second request should hit cache
        with patch(
            "core.dependency_injection.service_locator.project_service.get_cached_project_document_response"
        ) as mock_get_cache:
            mock_get_cache.return_value = {"results": []}
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mock_get_cache.assert_called_once()

    def test_cache_bypass_parameter(self):
        """Test cache bypass with disable_cache parameter."""
        self.client.force_authenticate(user=self.user)

        url = f"/api/project/{self.project.id}/documents/"

        # Request with cache disabled should not use cache
        with patch.object(
            self.service, "get_cached_project_document_response"
        ) as mock_get_cache:
            response = self.client.get(url, {"disable_cache": "true"})
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mock_get_cache.assert_not_called()

    def test_create_document_cache_invalidation(self):
        """Test that creating a document invalidates relevant caches."""
        self.client.force_authenticate(user=self.user)

        url = f"/api/project/{self.project.id}/documents/"

        # Create a file for the document
        from storage.factories import FileFactory

        test_file = FileFactory()

        with patch(
            "core.dependency_injection.service_locator.project_service.invalidate_project_document_caches_for_project"
        ) as mock_invalidate:
            response = self.client.post(
                url,
                {
                    "name": "Test Document",
                    "category": "plan_and_elevation",  # Use valid category
                    "visibility": "public",
                    "file_id": str(
                        test_file.id
                    ),  # Use file_id instead of file
                },
                format="json",
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            mock_invalidate.assert_called_once_with(str(self.project.id))

    def test_update_document_cache_invalidation(self):
        """Test that updating a document invalidates relevant caches."""
        self.client.force_authenticate(user=self.user)

        url = f"/api/project/{self.project.id}/documents/{self.document.id}/"

        with patch(
            "core.dependency_injection.service_locator.project_service.invalidate_project_document_caches_for_project"
        ) as mock_invalidate:
            response = self.client.patch(url, {"name": "Updated Name"})
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mock_invalidate.assert_called_once_with(self.project.id)

    def test_delete_document_cache_invalidation(self):
        """Test that deleting a document invalidates relevant caches."""
        self.client.force_authenticate(user=self.user)

        url = f"/api/project/{self.project.id}/documents/{self.document.id}/"

        with patch(
            "core.dependency_injection.service_locator.project_service.invalidate_project_document_caches_for_project"
        ) as mock_invalidate:
            response = self.client.delete(url)
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            mock_invalidate.assert_called_once_with(self.project.id)
