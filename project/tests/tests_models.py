from accounts.factories import UserFactory
from contacts.factories import SubContractorEstimateFactory
from contacts.factories import SubcontractorFactory
from project.factories import ProjectFactory
from project.models import Project
from testing.base import BaseAPITest


class ProjectTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.project = ProjectFactory()
        super().setUp()

    def test_get_all_subcontractors_estimates_projects(self):
        # Given that we have a project with a subcontractor estimate
        subContractor = SubcontractorFactory()
        SubContractorEstimateFactory(
            project=self.project, subcontractor=subContractor
        )

        project_id = self.project.id

        subcontractor_estimates = (
            Project.objects.filter(id=project_id)
            .first()
            .subcontractor_estimates.all()
        )

        self.assertEqual(subcontractor_estimates.count(), 1)

    def test_get_all_subcontractors_related_projects(self):
        subContractor = SubcontractorFactory()
        SubContractorEstimateFactory(
            project=self.project, subcontractor=subContractor
        )

        subcontactors = Project.objects.get_subcontractors_by_project_id(
            self.project.id
        )

        self.assertEqual(subcontactors.count(), 1)

    def test_get_subcontractors_projects(self):
        subContractor = SubcontractorFactory()
        SubContractorEstimateFactory(
            project=self.project, subcontractor=subContractor
        )
        SubContractorEstimateFactory(
            project=ProjectFactory(), subcontractor=subContractor
        )

        subcontactors = Project.objects.get_all_subcontractor_project(
            subContractor.id
        )

        self.assertEqual(subcontactors.count(), 2)
