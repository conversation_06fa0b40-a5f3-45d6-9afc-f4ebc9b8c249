from decimal import Decimal

from accounts.factories import UserFactory
from company.models import Licenses
from contacts.factories import ContactFactory
from contacts.factories import SubcontractorFactory
from django.urls import reverse
from project.factories import ChangeOrderFactory
from project.factories import DocumentCategoryAccessFactory
from project.factories import ProjectDocumentFactory
from project.factories import ProjectFactory
from project.factories import ProjectInviteFactory
from project.factories import ProjectShareFactory
from project.models import ChangeOrder
from project.models import Project
from project.models import ProjectDocument
from project.models import ProjectInvite
from project.models import ProjectShare
from rest_framework import status
from storage.factories import FileFactory
from testing.base import BaseAPITest


class ProjectTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        super().setUp()

    def test_unauthenticated_user(self):
        # Given that I have an unauthenticated user
        # When I try to access the project endpoint
        url = reverse("project:project")
        self.client.force_authenticate(user=None)

        response = self.client.get(url)

        # Then I should get a 401 response
        self.assertEqual(response.status_code, 401)

        response = self.client.post(url, {})
        self.assertEqual(response.status_code, 401)

        response = self.client.put(url, {})
        self.assertEqual(response.status_code, 401)

        response = self.client.patch(url)
        self.assertEqual(response.status_code, 401)

        response = self.client.delete(url)
        self.assertEqual(response.status_code, 401)

    def test_create_project(self):
        # Given that I have an authenticated user
        self.client.force_authenticate(user=self.user)

        # When I create a project
        url = reverse("project:project")
        data = {
            "name": "Test Project",
            "owner": "Test Owner",
            "address_line_1": "Test Address Line 1",
            "address_line_2": "Test Address Line 2",
            "city": "Test City",
            "state": "Test State",
            "zip_code": "Test Zip Code",
            "country": "Test Country",
            "contact": "Test Contact",
            "email": "<EMAIL>",
            "created_by": self.user.id,
            "additional_contacts_ids": [
                ContactFactory().id,
                ContactFactory().id,
            ],
            "new_additional_contacts": [
                {
                    "first_name": "New Contact 1",
                    "email": "<EMAIL>",
                },
                {
                    "first_name": "New Contact 2",
                    "email": "<EMAIL>",
                },
            ],
        }
        response = self.client.post(url, data, format="json")
        # Then I should get a 201 response
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["owner"], data["owner"])
        self.assertEqual(
            response.data["address_line_1"], data["address_line_1"]
        )
        self.assertEqual(
            response.data["address_line_2"], data["address_line_2"]
        )
        self.assertEqual(response.data["city"], data["city"])
        self.assertEqual(response.data["state"], data["state"])
        self.assertEqual(response.data["zip_code"], data["zip_code"])
        self.assertEqual(response.data["country"], data["country"])
        self.assertEqual(response.data["contact"], data["contact"])
        self.assertEqual(response.data["email"], data["email"])
        self.assertEqual(
            response.data["created_by"]["id"], str(data["created_by"])
        )
        project_instance = Project.objects.get(id=response.data["id"])
        additional_contacts = project_instance.additional_contacts.all()
        self.assertEqual(
            additional_contacts.count(), 4
        )  # Both existing and new contacts

    def test_list_project(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project = ProjectFactory(created_by=self.user)
        ProjectFactory(created_by=UserFactory())
        # When I list projects
        url = reverse("project:project")
        response = self.client.get(url)

        count_of_user_projects = Project.objects.get_project_for_user(
            self.user
        ).count()
        # Then I should get a 200 response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), count_of_user_projects)

        project = response.data["results"][0]
        self.assertEqual(project["id"], str(user_project.id))
        self.assertEqual(response.data["count"], 1)

    def test_retrieve_project(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project = ProjectFactory(created_by=self.user)
        # When I retrieve a project
        url = reverse("project:project", kwargs={"pk": user_project.id})
        response = self.client.get(url)

        # Then I should get a 200 response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["id"], response.data["id"])

    def test_update_project(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project = ProjectFactory(created_by=self.user)
        # When I update a project
        url = reverse("project:project", kwargs={"pk": user_project.id})
        data = {
            "name": "Test Project",
            "owner": "Test Owner",
            "address_line_1": "Test Address Line 1",
            "address_line_2": "Test Address Line 2",
            "city": "Test City",
            "state": "Test State",
            "zip_code": "Test Zip Code",
            "country": "Test Country",
            "contact": "Test Contact",
            "email": "<EMAIL>",
        }

        response = self.client.patch(url, data, format="json")

        # Then I should get a 200 response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["id"], response.data["id"])
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["owner"], data["owner"])
        self.assertEqual(
            response.data["address_line_1"], data["address_line_1"]
        )
        self.assertEqual(
            response.data["address_line_2"], data["address_line_2"]
        )
        self.assertEqual(response.data["city"], data["city"])
        self.assertEqual(response.data["state"], data["state"])
        self.assertEqual(response.data["zip_code"], data["zip_code"])
        self.assertEqual(response.data["country"], data["country"])
        self.assertEqual(response.data["contact"], data["contact"])
        self.assertEqual(response.data["email"], data["email"])
        self.assertEqual(
            response.data["created_by"]["id"], str(user_project.created_by.id)
        )

        # When I try update a project that does not belong to me
        other_user_project = ProjectFactory(created_by=UserFactory())
        url = reverse("project:project", kwargs={"pk": other_user_project.id})
        data = {
            "name": "Test Project",
            "owner": "Test Owner",
            "address_line_1": "Test Address Line 1",
            "address_line_2": "Test Address Line 2",
            "city": "Test City",
            "state": "Test State",
            "zip_code": "Test Zip Code",
            "country": "Test Country",
            "contact": "Test Contact",
        }

        response = self.client.patch(url, data)

        # Then I should get a 403 response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_project(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project = ProjectFactory(created_by=self.user)
        # When I delete a project
        url = reverse("project:project", kwargs={"pk": user_project.id})
        response = self.client.delete(url)

        # Then I should get a 204 response
        self.assertEqual(response.status_code, 204)
        self.assertEqual(
            Project.objects.get_project_for_user(self.user).count(), 0
        )

        # When I try delete a project that does not belong to me
        other_user_project = ProjectFactory(created_by=UserFactory())
        url = reverse("project:project", kwargs={"pk": other_user_project.id})
        response = self.client.delete(url)

        # Then I should get a 403 response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_add_subcontractor_to_project(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project = ProjectFactory(created_by=self.user)
        # When I add a subcontractor to a project
        url = reverse(
            "project:project_subcontractors",
            kwargs={"pk": user_project.id},
        )
        data = {
            "subcontractor_ids": [
                SubcontractorFactory().id,
                SubcontractorFactory().id,
            ],
            "operation": "add",
        }

        response = self.client.patch(url, data, format="json")

        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # reload the project
        # assert that the project has 2 subcontractors
        self.assertEqual(user_project.subcontractors.count(), 2)

        url = reverse(
            "project:project",
            kwargs={"pk": user_project.id},
        )
        res = self.client.get(url)
        # Then I should get a 200 response
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # ensure that the 2 subcontractors are returned
        self.assertEqual(len(res.data["subcontractors"]), 2)

    def test_remove_subcontractor_from_project(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project: Project = ProjectFactory(created_by=self.user)
        # When I add a subcontractor to a project

        subcontractor_1 = SubcontractorFactory()
        subcontractor_2 = SubcontractorFactory()
        user_project.subcontractors.add(subcontractor_1)
        user_project.subcontractors.add(subcontractor_2)

        self.assertEqual(user_project.subcontractors.count(), 2)

        url = reverse(
            "project:project_subcontractors",
            kwargs={"pk": user_project.id},
        )

        data = {
            "subcontractor_ids": [
                subcontractor_2.id,
            ],
            "operation": "remove",
        }

        response = self.client.patch(url, data, format="json")

        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(user_project.subcontractors.count(), 1)

        # ensure that the subcontractor_2 is removed from the project
        self.assertFalse(
            user_project.subcontractors.filter(id=subcontractor_2.id).exists()
        )

        # ensure that the subcontractor_1 is still in the project
        self.assertTrue(
            user_project.subcontractors.filter(id=subcontractor_1.id).exists()
        )

    def test_get_project_with_subcontractor(self):
        # Given that I have an authenticated user with 1 project
        self.client.force_authenticate(user=self.user)

        user_project: Project = ProjectFactory(created_by=self.user)
        # When I add a subcontractor to a project

        subcontractor_1 = SubcontractorFactory()
        subcontractor_2 = SubcontractorFactory()
        user_project.subcontractors.add(subcontractor_1)
        user_project.subcontractors.add(subcontractor_2)

        self.assertEqual(user_project.subcontractors.count(), 2)

        url = reverse(
            "project:project",
            kwargs={"pk": user_project.id},
        )

        res = self.client.get(url)

        # Then I should get a 200 response
        self.assertEqual(res.status_code, status.HTTP_200_OK)

        # ensure that the 2 subcontractors are returned
        self.assertEqual(len(res.data["subcontractors"]), 2)

    def test_add_subcontractor_to_project_with_contact_data(self):
        self.client.force_authenticate(user=self.user)
        user_project = ProjectFactory(created_by=self.user)

        url = reverse(
            "project:project_subcontractors", kwargs={"pk": user_project.id}
        )
        data = {
            "subcontractorData": [
                {
                    "contact": {
                        "firstName": "John",
                        "lastName": "Contractor",
                        "email": "<EMAIL>",
                        "address_line_1": "123 Main St",
                        "address_line_2": "Apt 101",
                        "city": "Anytown",
                        "state": "California",
                        "zip_code": "12345",
                        "country": "USA",
                        "phone_number": "**********",
                        "extension": "12345",
                        "company": "ABC Inc",
                    },
                    "licenses": [
                        {
                            "license_type": Licenses.LicenseType.BUSINESS_LICENSE,
                            "name": "License 1 description",
                            "license_number": "1234",
                            "valid_from": "2021-12-12",
                            "valid_to": "2021-12-12T00:00:00Z",
                            "reminder": "2021-05-22 11:50:09.831000+00:00",
                        }
                    ],
                    "hasTaxDocuments": True,
                    "taxId": "string",
                }
            ],
            "operation": "add",
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(user_project.subcontractors.count(), 1)

        added_subcontractor = user_project.subcontractors.first()
        self.assertEqual(added_subcontractor.contact.first_name, "John")
        self.assertEqual(added_subcontractor.contact.last_name, "Contractor")
        self.assertEqual(added_subcontractor.licenses.count(), 1)

    def test_remove_subcontractor_from_project_with_contact(self):
        self.client.force_authenticate(user=self.user)
        user_project = ProjectFactory(created_by=self.user)

        # Create contact and subcontractor
        contact = ContactFactory(
            first_name="Jane", last_name="Contractor", uploaded_by=self.user
        )
        subcontractor = SubcontractorFactory(contact=contact)
        user_project.subcontractors.add(subcontractor)

        self.assertEqual(user_project.subcontractors.count(), 1)

        url = reverse(
            "project:project_subcontractors", kwargs={"pk": user_project.id}
        )
        data = {
            "subcontractor_data": [{"contact_id": str(contact.id)}],
            "operation": "remove",
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(user_project.subcontractors.count(), 0)
        self.assertFalse(
            user_project.subcontractors.filter(contact=contact).exists()
        )


class ProjectDocumentTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.other_user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)
        self.other_project = ProjectFactory(created_by=self.other_user)
        return super().setUp()

    def test_unauthenticated_user(self):
        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        res = self.client.post(url, {})
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": 1},
        )
        res = self.client.patch(url, {})
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

        res = self.client.delete(url)
        self.assertEqual(res.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_project_document(self):
        ProjectDocumentFactory(
            project=self.project,
            category=ProjectDocument.Category.PERMIT,
        )
        ProjectDocumentFactory(
            project=ProjectFactory(),
            category=ProjectDocument.Category.PERMIT,
        )

        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        self.client.force_authenticate(user=self.user)

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)

        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_list_project_document_filter_by_category(self):
        ProjectDocumentFactory(
            project=self.project,
            category=ProjectDocument.Category.PERMIT,
        )
        ProjectDocumentFactory(
            project=ProjectFactory(),
            category=ProjectDocument.Category.PERMIT,
        )

        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        self.client.force_authenticate(user=self.user)

        response = self.client.get(
            url, {"category": ProjectDocument.Category.PERMIT}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)

        # Test filter by category
        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        response = self.client.get(
            url, {"category": ProjectDocument.Category.CHANGE_ORDER}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 0)

    def test_create_project_document(self):
        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        self.client.force_authenticate(user=self.user)

        file = FileFactory()
        data = {
            "name": "Test Project Document",
            "file_id": file.id,
            "category": ProjectDocument.Category.PERMIT,
            "tag_names": ["Test", "Document"],
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["category"], data["category"])
        self.assertEqual(len(response.data["tags"]), len(data["tag_names"]))

        #  Test request with  invalid file id
        bad_data = {
            "name": "Test Project Document",
            "file_id": "invalid-file-id",
            "category": ProjectDocument.Category.PERMIT,
            "tag_names": ["Test", "Document"],
        }
        response = self.client.post(url, bad_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        #  Test request with invalid project id
        url = reverse(
            "project:project_document", kwargs={"project_id": "invalid-23556"}
        )
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_retrieve_project_document(self):
        document = ProjectDocumentFactory(project=self.project)
        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": document.id},
        )

        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], document.name)
        self.assertEqual(response.data["category"], document.category)
        self.assertEqual(len(response.data["tags"]), document.tags.count())

    def test_update_project_document(self):
        document = ProjectDocumentFactory(
            project=self.project, file=FileFactory()
        )
        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": document.id},
        )

        self.client.force_authenticate(user=self.user)
        data = {
            "name": "New Project Document",
            "category": ProjectDocument.Category.CHANGE_ORDER,
            "tag_names": ["Test", "Document"],
            "file_id": document.file.id,
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["category"], data["category"])
        self.assertEqual(len(response.data["tags"]), len(data["tag_names"]))

        self.assertEqual(response.data["file"]["id"], str(document.file.id))

    def test_delete_project_document(self):
        file = FileFactory(is_deleted=False)
        document = ProjectDocumentFactory(project=self.project, file=file)
        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": document.id},
        )

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ProjectDocument.objects.count(), 0)

        file.refresh_from_db()
        self.assertTrue(file.is_deleted)

    def test_create_private_project_document(self):
        url = reverse(
            "project:project_document", kwargs={"project_id": self.project.id}
        )
        self.client.force_authenticate(user=self.user)

        file = FileFactory()
        data = {
            "name": "Private Project Document",
            "file_id": file.id,
            "category": ProjectDocument.Category.PERMIT,
            "visibility": "private",
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["visibility"], data["visibility"])

    def test_access_private_project_document_by_non_creator(self):
        document = ProjectDocumentFactory(
            project=self.project, visibility="private"
        )
        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": document.id},
        )

        # Authenticate as a different user
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Attempt to update the document
        data = {"name": "Updated Name"}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Attempt to delete the document
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_access_public_project_document(self):
        document = ProjectDocumentFactory(
            project=self.project,
            visibility=ProjectDocument.Visibility.PUBLIC,
            category=ProjectDocument.Category.PERMIT,
        )

        # Invite other_user to the project and ensure they accept the invite
        other_user = UserFactory()
        invite = ProjectInviteFactory(
            user=other_user,
            project=self.project,
            status="accepted",
        )
        DocumentCategoryAccessFactory(
            project_invite=invite,
            document_category=document.category,
            access_level="read",
        )

        self.client.force_authenticate(user=other_user)
        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": document.id},
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_creator_can_access_and_update_private_document(self):
        document = ProjectDocumentFactory(
            project=self.project, visibility="private"
        )
        url = reverse(
            "project:project_document",
            kwargs={"project_id": self.project.id, "pk": document.id},
        )

        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], document.name)

        # Update the document
        data = {"name": "Updated Name"}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], data["name"])

        # Delete the document
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ProjectDocument.objects.count(), 0)

    def test_bulk_delete_project_documents(self):
        # Create multiple documents
        file1 = FileFactory(is_deleted=False)
        file2 = FileFactory(is_deleted=False)
        doc1 = ProjectDocumentFactory(project=self.project, file=file1)
        doc2 = ProjectDocumentFactory(project=self.project, file=file2)

        url = reverse("project:bulk_delete_documents")
        self.client.force_authenticate(user=self.user)

        data = {"document_ids": [str(doc1.id), str(doc2.id)]}
        response = self.client.delete(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            ProjectDocument.objects.filter(is_deleted=False).count(), 0
        )

        # Verify associated files are deleted
        file1.refresh_from_db()
        file2.refresh_from_db()
        self.assertTrue(file1.is_deleted)
        self.assertTrue(file2.is_deleted)


class ChangeOrderTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)
        self.change_order = ChangeOrderFactory(project=self.project)
        super().setUp()

    def test_list_change_orders(self):
        url = reverse(
            "project:change-order-list-create",
            kwargs={"project_id": self.project.id},
        )
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)

    def test_create_change_order(self):
        url = reverse(
            "project:change-order-list-create",
            kwargs={"project_id": self.project.id},
        )
        self.client.force_authenticate(user=self.user)
        data = {
            "scope_of_work": "New scope of work",
            "amount": "1000.00",
            "visibility": ChangeOrder.Visibility.PUBLIC,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(ChangeOrder.objects.count(), 2)
        self.assertEqual(response.data["scope_of_work"], data["scope_of_work"])
        self.assertEqual(
            Decimal(response.data["amount"]), Decimal(data["amount"])
        )

    def test_retrieve_change_order(self):
        url = reverse(
            "project:change-order-detail",
            kwargs={"project_id": self.project.id, "pk": self.change_order.id},
        )
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], str(self.change_order.id))

    def test_update_change_order(self):
        url = reverse(
            "project:change-order-detail",
            kwargs={"project_id": self.project.id, "pk": self.change_order.id},
        )
        self.client.force_authenticate(user=self.user)
        data = {
            "scope_of_work": "Updated scope of work",
            "amount": "2000.00",
        }
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.change_order.refresh_from_db()
        self.assertEqual(
            self.change_order.scope_of_work, data["scope_of_work"]
        )
        self.assertEqual(self.change_order.amount, Decimal(data["amount"]))

    def test_delete_change_order(self):
        url = reverse(
            "project:change-order-detail",
            kwargs={"project_id": self.project.id, "pk": self.change_order.id},
        )
        self.client.force_authenticate(user=self.user)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ChangeOrder.objects.count(), 0)

    def test_generate_change_order_pdf(self):
        url = reverse(
            "project:generate-change-order-pdf",
            kwargs={
                "project_id": self.project.id,
                "change_order_id": self.change_order.id,
            },
        )
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/pdf")


class CompanyProjectDocumentViewTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.other_user = UserFactory()
        self.invited_user = UserFactory()

        # Project created by the user
        self.project_1 = ProjectFactory(created_by=self.user)
        ProjectDocumentFactory(
            project=self.project_1,
            category=ProjectDocument.Category.ESTIMATE,
            visibility="public",
        )
        ProjectDocumentFactory(
            project=self.project_1,
            category=ProjectDocument.Category.PERMIT,
            visibility="private",
        )

        # Another project created by the user
        self.project_2 = ProjectFactory(created_by=self.user)
        ProjectDocumentFactory(
            project=self.project_2,
            category=ProjectDocument.Category.ESTIMATE,
            visibility="public",
        )

        # Project created by other_user, invited_user is invited to this project
        self.invited_project = ProjectFactory(created_by=self.other_user)
        ProjectDocumentFactory(
            project=self.invited_project,
            category=ProjectDocument.Category.ESTIMATE,
            visibility="public",
        )
        project_invite = ProjectInviteFactory(
            user=self.invited_user,
            project=self.invited_project,
            status="accepted",
        )
        DocumentCategoryAccessFactory(
            project_invite=project_invite,
            document_category="estimate",
            access_level="read",
        )

    def test_get_public_project_documents(self):
        url = reverse("project-all-documents") + "?category=estimate"
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

    def test_get_private_project_documents(self):
        url = reverse("project-all-documents") + "?category=permit"
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

        # Now authenticate as the project creator
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_get_project_documents_with_invalid_category(self):
        url = reverse("project-all-documents") + "?category=invalid"
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_get_project_documents_as_invited_user(self):
        url = reverse("project-all-documents") + "?category=estimate"
        self.client.force_authenticate(user=self.invited_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["id"], self.invited_project.id)
        self.assertEqual(len(response.data[0]["files"]), 1)


class ListSubcontractorsByProjectViewTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)
        self.subcontractor1 = SubcontractorFactory()
        self.subcontractor2 = SubcontractorFactory()
        self.project.subcontractors.add(
            self.subcontractor1, self.subcontractor2
        )
        return super().setUp()

    def test_list_subcontractors_by_project(self):
        url = reverse("subcontractors-by-project")
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["project_id"], self.project.id)
        self.assertEqual(response.data[0]["project_name"], self.project.name)
        self.assertEqual(
            len(response.data[0]["subcontractors"]), 2
        )  # Expecting 2 subcontractors
        subcontractor_ids = [
            str(self.subcontractor1.id),
            str(self.subcontractor2.id),
        ]
        response_subcontractor_ids = [
            subcontractor["id"]
            for subcontractor in response.data[0]["subcontractors"]
        ]
        self.assertCountEqual(
            subcontractor_ids, response_subcontractor_ids
        )  # Checking if both subcontractors are in the response

    def test_list_subcontractors_by_project_different_user(self):
        other_user = UserFactory()
        self.client.force_authenticate(user=other_user)
        url = reverse("subcontractors-by-project")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)  # No projects for this user


class ProjectInviteTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)
        self.invitee = UserFactory()
        self.invite_url = reverse(
            "project:create_project_invite",
            kwargs={"project_id": str(self.project.id)},
        )
        self.list_received_invites_url = reverse(
            "project:list_invitations_received"
        )
        self.list_sent_invites_url = reverse(
            "project:list_invitations_sent",
            kwargs={"project_id": str(self.project.id)},
        )

    def test_create_project_invite(self):
        self.client.force_authenticate(user=self.user)
        data = {
            "invitee_emails": ["<EMAIL>"],
            "document_category_accesses": [
                {
                    "document_category": "plan_and_elevation",
                    "access_level": "read",
                },
                {"document_category": "estimate", "access_level": "write"},
            ],
        }
        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(ProjectInvite.objects.count(), 1)

    def test_list_received_invitations(self):
        self.client.force_authenticate(user=self.invitee)
        response = self.client.get(self.list_received_invites_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_list_sent_invitations(self):
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.list_sent_invites_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_access_project_as_invitee(self):
        invite = ProjectInviteFactory(
            user=self.invitee, project=self.project, status="accepted"
        )
        DocumentCategoryAccessFactory(
            project_invite=invite,
            document_category="plan_and_elevation",
            access_level="read",
        )

        self.client.force_authenticate(user=self.invitee)
        url = reverse("project:project", kwargs={"pk": str(self.project.id)})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_access_project_document_as_invitee(self):
        invite = ProjectInviteFactory(
            user=self.invitee, project=self.project, status="accepted"
        )
        DocumentCategoryAccessFactory(
            project_invite=invite,
            document_category="estimate",
            access_level="write",
        )

        self.client.force_authenticate(user=self.invitee)
        url = reverse(
            "project:project_document",
            kwargs={"project_id": str(self.project.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_accept_invitation(self):
        invite = ProjectInviteFactory(user=self.invitee, project=self.project)
        DocumentCategoryAccessFactory(
            project_invite=invite,
            document_category="estimate",
            access_level="read",
        )

        url = reverse(
            "project:invite_status",
            kwargs={"pk": str(invite.id)},
        )

        self.client.force_authenticate(user=self.invitee)  # Change to invitee

        updated_data = {"status": "accepted"}
        response = self.client.patch(url, updated_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "accepted")

    def test_retrieve_update_destroy_invitation(self):
        invite = ProjectInviteFactory(user=self.invitee, project=self.project)
        DocumentCategoryAccessFactory(
            project_invite=invite,
            document_category="estimate",
            access_level="read",
        )
        url = reverse(
            "project:retrieve_update_destroy_invitation",
            kwargs={"invite_id": str(invite.id)},
        )

        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        updated_data = {
            "document_category_accesses": [
                {
                    "document_category": "estimate",
                    "access_level": "write",
                },
                {
                    "document_category": "plan_and_elevation",
                    "access_level": "read",
                },
            ],
        }
        response = self.client.patch(url, updated_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["document_category_accesses"][0]["access_level"],
            "write",
        )
        self.assertEqual(
            response.data["document_category_accesses"][1][
                "document_category"
            ],
            "plan_and_elevation",
        )

        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ProjectInvite.objects.count(), 0)


class ProjectShareTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)
        self.share_url = reverse(
            "project:create_project_share",
            kwargs={"project_id": str(self.project.id)},
        )
        self.list_sent_shares_url = reverse(
            "project:list_share_sent",
            kwargs={"project_id": str(self.project.id)},
        )

    def test_create_project_share(self):
        self.client.force_authenticate(user=self.user)
        data = {
            "user_emails": ["<EMAIL>"],
            "document_category_accesses": [
                {
                    "document_category": "plan_and_elevation",
                    "access_level": "read",
                },
                {"document_category": "estimate", "access_level": "read"},
            ],
        }
        response = self.client.post(self.share_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(ProjectShare.objects.count(), 1)

    def test_list_sent_shares(self):
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.list_sent_shares_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_update_destroy_share(self):
        share = ProjectShareFactory(
            project=self.project, user_emails=[self.user.email]
        )
        url = reverse(
            "project:retrieve_update_destroy_share",
            kwargs={"share_id": str(share.id)},
        )

        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        updated_data = {
            "document_category_accesses": [
                {
                    "document_category": "plan_and_elevation",
                    "access_level": "read",
                },
                {"document_category": "estimate", "access_level": "read"},
            ],
        }
        response = self.client.patch(url, updated_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ProjectShare.objects.count(), 0)

    def test_retrieve_project_by_permission_id(self):
        share = ProjectShareFactory(
            project=self.project, user_emails=[self.user.email]
        )
        url = reverse(
            "project:project-by-permission",
            kwargs={"permission_id": share.permission_id},
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], str(self.project.id))
