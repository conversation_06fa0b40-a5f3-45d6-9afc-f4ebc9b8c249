import typing

from accounts.models import User
from core.models import BaseAddress
from core.models import BaseModel
from django.db import models
from django.db.models.functions import Lower
from django.utils import timezone
from resources.models import Tag
from storage.models import File

if typing.TYPE_CHECKING:
    from contacts.models import SubContractorEstimate
    from django.db.models.query import QuerySet


class ProjectManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_project_for_user(self, user: User):
        return (
            super()
            .get_queryset()
            .filter(created_by=user, is_deleted=False)
            .order_by(Lower("name"))
        )

    def get_subcontractors_by_project_id(self, project_id: str):
        from contacts.models import Subcontractor

        subcontractor_estimates: "QuerySet['SubContractorEstimate']" = (
            self.get_queryset()
            .filter(id=project_id)
            .first()
            .subcontractor_estimates.all()
            .order_by("created_at")
        )
        subcontractors = Subcontractor.objects.filter(
            id__in=subcontractor_estimates.values_list(
                "subcontractor_id", flat=True
            )
        )
        return subcontractors

    def get_all_subcontractor_project(self, subcontractor_id: str):
        return (
            self.get_queryset()
            .filter(subcontractor_estimates__subcontractor_id=subcontractor_id)
            .order_by(Lower("name"))
        )


class ProjectDocumentManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_document_for_project(self, project: "Project"):
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False, project=project)
            .order_by("-created_at")
        )

    def get_user_documents(self, user: User):
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False, project__created_by=user)
            .order_by(Lower("name"))
        )


class Project(BaseModel, BaseAddress):
    class Status:
        ACTIVE = "active"
        ARCHIVED = "archived"
        ALL = [
            ACTIVE,
            ARCHIVED,
        ]
        CHOICES = (
            (ACTIVE, "Active"),
            (ARCHIVED, "Inactive"),
        )

    subcontractor_estimates: models.QuerySet["SubContractorEstimate"]
    subcontractorestimate_set: models.QuerySet["SubContractorEstimate"]
    documents: models.QuerySet["ProjectDocument"]
    objects = ProjectManager()
    name = models.CharField(max_length=255)
    project_logo = models.ImageField(
        upload_to="project_logos", blank=True, null=True
    )
    owner = models.CharField(max_length=255)
    contact = models.CharField(max_length=255)
    email = models.EmailField()
    additional_contacts = models.ManyToManyField(
        "contacts.Contact", blank=True
    )
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=255, choices=Status.CHOICES, default=Status.ACTIVE
    )
    subcontractors = models.ManyToManyField(
        "contacts.Subcontractor", blank=True
    )

    def user_has_access(self, user):
        return (
            self.created_by == user
            or ProjectInvite.objects.filter(
                user=user, project=self, status="accepted"
            ).exists()
        )

    def __str__(self):
        return f"{self.name} - {self.created_by}"

    def save(self, *args, **kwargs) -> None:
        self.clear_cache()
        return super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        result = super().delete(*args, **kwargs)
        self.clear_cache()
        return result

    def clear_cache(self):
        from core.dependency_injection import service_locator

        service_locator.project_service.clear_project_cache(self.created_by)


class ProjectDocument(BaseModel):
    class Category:
        PLAN_AND_ELEVATION = "plan_and_elevation"
        ESTIMATE = "estimate"
        CONTRACT = "contract"
        CHANGE_ORDER = "change_order"
        PAYMENT_SCHEDULE = "payment_schedule"
        PERFORMANCE_SCHEDULE = "performance_schedule"
        SPECIFICATION = "specification"
        PERMIT = "permit"
        ADDITIONAL_DOCUMENTS = "additional_documents"
        GALLERY = "gallery"
        COMMUNICATION = "communication"
        INVOICE = "invoice"
        ALL = [
            PLAN_AND_ELEVATION,
            ESTIMATE,
            CONTRACT,
            CHANGE_ORDER,
            PAYMENT_SCHEDULE,
            PERFORMANCE_SCHEDULE,
            SPECIFICATION,
            PERMIT,
            ADDITIONAL_DOCUMENTS,
            GALLERY,
            COMMUNICATION,
            INVOICE,
        ]
        CHOICES = (
            (PLAN_AND_ELEVATION, "Plan and Elevation"),
            (ESTIMATE, "Estimate"),
            (CONTRACT, "Contract"),
            (CHANGE_ORDER, "Change Order"),
            (PAYMENT_SCHEDULE, "Payment Schedule"),
            (PERFORMANCE_SCHEDULE, "Performance Schedule"),
            (SPECIFICATION, "Specification"),
            (PERMIT, "Permit"),
            (ADDITIONAL_DOCUMENTS, "Additional Documents"),
            (GALLERY, "Gallery"),
            (COMMUNICATION, "Communication"),
            (INVOICE, "Invoice"),
        )

    class Visibility:
        PUBLIC = "public"
        PRIVATE = "private"
        CHOICES = (
            (PUBLIC, "Public"),
            (PRIVATE, "Private"),
        )

    objects: ProjectDocumentManager = ProjectDocumentManager()
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name="documents",
    )
    name = models.CharField(max_length=1024)
    file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="project_documents",
    )
    thumbnail = models.URLField(null=True, blank=True)
    category = models.CharField(max_length=255, choices=Category.CHOICES)
    visibility = models.CharField(
        max_length=50, choices=Visibility.CHOICES, default=Visibility.PUBLIC
    )
    tags = models.ManyToManyField(Tag, blank=True)
    subcontractor = models.ForeignKey(
        "contacts.Subcontractor",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="project_documents",
    )

    def get_user_access_level(self, user):
        if (
            self.visibility == self.Visibility.PRIVATE
            and self.project.created_by != user
        ):
            return "no_access"

        if self.project.created_by == user:
            return "write"

        invite = ProjectInvite.objects.filter(
            user=user, project=self.project, status="accepted"
        ).first()

        if invite:
            access = DocumentCategoryAccess.objects.filter(
                project_invite=invite, document_category=self.category
            ).first()
            return access.access_level if access else "no_access"

        return "no_access"

    def __str__(self):
        return f"{self.name} - ${self.category} {self.project}"


class ChangeOrder(BaseModel):
    class Visibility:
        PUBLIC = "public"
        PRIVATE = "private"
        CHOICES = (
            (PUBLIC, "Public"),
            (PRIVATE, "Private"),
        )

    project = models.ForeignKey(Project, on_delete=models.CASCADE)

    scope_of_work = models.TextField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    visibility = models.CharField(
        max_length=50, choices=Visibility.CHOICES, default=Visibility.PUBLIC
    )


class DocumentCategoryAccess(BaseModel):
    class DocumentCategory:
        PLAN_AND_ELEVATION = "plan_and_elevation"
        ESTIMATE = "estimate"
        CONTRACT = "contract"
        CHANGE_ORDER = "change_order"
        PAYMENT_SCHEDULE = "payment_schedule"
        PERFORMANCE_SCHEDULE = "performance_schedule"
        SPECIFICATION = "specification"
        PERMIT = "permit"
        ADDITIONAL_DOCUMENTS = "additional_documents"
        GALLERY = "gallery"
        COMMUNICATION = "communication"
        INVOICE = "invoice"

        ALL = [
            PLAN_AND_ELEVATION,
            ESTIMATE,
            CONTRACT,
            CHANGE_ORDER,
            PAYMENT_SCHEDULE,
            PERFORMANCE_SCHEDULE,
            SPECIFICATION,
            PERMIT,
            ADDITIONAL_DOCUMENTS,
            GALLERY,
            COMMUNICATION,
            INVOICE,
        ]
        CHOICES = (
            (PLAN_AND_ELEVATION, "Plan and Elevation"),
            (ESTIMATE, "Estimate"),
            (CONTRACT, "Contract"),
            (CHANGE_ORDER, "Change Order"),
            (PAYMENT_SCHEDULE, "Payment Schedule"),
            (PERFORMANCE_SCHEDULE, "Performance Schedule"),
            (SPECIFICATION, "Specification"),
            (PERMIT, "Permit"),
            (ADDITIONAL_DOCUMENTS, "Additional Documents"),
            (GALLERY, "Gallery"),
            (COMMUNICATION, "Communication"),
            (INVOICE, "Invoice"),
        )

    class AccessLevel:
        READ = "read"
        WRITE = "write"
        NO_ACCESS = "no_access"
        CHOICES = (
            (READ, "Read"),
            (WRITE, "Write"),
            (NO_ACCESS, "No Access"),
        )

    document_category = models.CharField(
        max_length=255, choices=DocumentCategory.CHOICES
    )
    access_level = models.CharField(
        max_length=255,
        choices=AccessLevel.CHOICES,
        default=AccessLevel.NO_ACCESS,
    )
    project_invite = models.ForeignKey(
        "ProjectInvite",
        on_delete=models.CASCADE,
        related_name="document_category_accesses",
        null=True,
        blank=True,
    )
    project_share = models.ForeignKey(
        "ProjectShare",
        on_delete=models.CASCADE,
        related_name="document_category_accesses",
        null=True,
        blank=True,
    )

    def __str__(self):
        return f"{self.document_category}: {self.access_level}"


class ProjectInviteManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False)
            .order_by("-created_at")
        )

    def get_invites_that_expire_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            status="pending",
            created_at__range=[
                start_of_day - timezone.timedelta(days=7),
                end_of_day - timezone.timedelta(days=7),
            ],
        )

    def get_invites_that_need_to_remind_today(self):
        start_of_day = timezone.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_of_day = timezone.now().replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        return self.get_queryset().filter(
            status="pending",
            created_at__range=[
                start_of_day - timezone.timedelta(days=6),
                end_of_day - timezone.timedelta(days=6),
            ],
        )

    def get_invites_to_delete(self):
        fourteen_days_ago = timezone.now() - timezone.timedelta(days=14)
        return self.get_queryset().filter(
            status="expired", created_at__lte=fourteen_days_ago
        )


class ProjectInvite(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=255,
        choices=[
            ("pending", "Pending"),
            ("accepted", "Accepted"),
            ("rejected", "Rejected"),
            ("expired", "Expired"),
        ],
        default="pending",
    )

    objects = ProjectInviteManager()

    def save(self, *args, **kwargs) -> None:
        self.clear_cache()
        return super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        result = super().delete(*args, **kwargs)
        self.clear_cache()
        return result

    def clear_cache(self):
        from core.dependency_injection import service_locator

        service_locator.project_service.clear_project_cache(self.user)

    def __str__(self):
        return f"Invite {self.user.email} to {self.project.name}"


class ProjectShare(BaseModel):
    user_emails = models.JSONField(default=list)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    permission_id = models.CharField(
        max_length=255, unique=True, editable=False, blank=True, null=True
    )

    def __str__(self):
        return f"Share {self.project.name} with {', '.join(self.user_emails)}"
