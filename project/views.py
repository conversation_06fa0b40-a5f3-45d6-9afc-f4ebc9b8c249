import logging
import uuid
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from io import Bytes<PERSON>

from accounts.models import User
from company.models import Company
from contacts.serializers import SubcontractSerializer
from core.constants import Features
from core.core_services.sendgrid_service import CoreService
from core.core_services.sendgrid_service import SendGridService
from django.conf import settings
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import get_template
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from notifications.mixins import NotificationCleanupMixin
from notifications.models import Notification
from notifications.tasks import notify_user_about_new_invite
from project.filters import ChangeOrderFilter
from project.filters import ProjectDocumentFilter
from project.filters import ProjectFilter
from project.filters import ProjectInviteFilter
from project.models import Project
from project.models import ProjectDocument
from project.serializers import ProjectDocument<PERSON>erializer
from project.serializers import ProjectSerializer
from project.serializers import ProjectSubcontractorSerializer
from recent_app.mixins import CreateRecentActivityMixin
from rest_framework import filters
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.exceptions import ParseError
from rest_framework.exceptions import PermissionDenied
from rest_framework.exceptions import ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.generics import ListAPIView
from rest_framework.generics import ListCreateAPIView
from rest_framework.generics import RetrieveAPIView
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import AllowAny
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from silk.profiling.profiler import silk_profile
from xhtml2pdf import pisa

from .models import ChangeOrder
from .models import DocumentCategoryAccess
from .models import ProjectInvite
from .models import ProjectShare
from .serializers import ChangeOrderSerializer
from .serializers import InvitedUserSerializer
from .serializers import ProjectInviteSerializer
from .serializers import ProjectInviteStatusSerializer
from .serializers import ProjectListSerializer
from .serializers import ProjectPermissionSerializer
from .serializers import ProjectShareSerializer

logger = logging.getLogger(__name__)


class BaseProjectView:

    serializer_class = ProjectSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        user = self.request.user

        # Check if caching is disabled
        if self.request.query_params.get("disable_cache"):
            return self._get_fresh_queryset(user)

        try:
            # Try to get cached projects first
            from core.dependency_injection import service_locator

            cached_projects = (
                service_locator.project_service.get_cached_projects(user)
            )
            if cached_projects is not None:
                logger.debug(
                    f"Cache hit for project queryset - user: {user.id}"
                )
                return cached_projects
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                from core.dependency_injection import service_locator

                service_locator.project_service.clear_project_cache(user)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted project cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached projects, falling back to DB: {e}"
            )

        # Cache miss - get fresh data
        logger.debug(f"Cache miss for project queryset - user: {user.id}")
        return self._get_and_cache_queryset(user)

    def _get_fresh_queryset(self, user):
        """Get fresh queryset without caching."""
        created_projects = (
            Project.objects.filter(created_by=user, is_deleted=False)
            .select_related("created_by")
            .prefetch_related("projectinvite_set")
            .order_by("created_at")
        )

        invited_projects = (
            Project.objects.filter(
                projectinvite__user=user,
                projectinvite__status="accepted",
                is_deleted=False,
            )
            .select_related("created_by")
            .prefetch_related("projectinvite_set")
            .order_by("created_at")
        )

        queryset = created_projects | invited_projects
        return queryset.distinct()

    def _get_and_cache_queryset(self, user):
        """Get fresh queryset and cache it."""
        queryset = self._get_fresh_queryset(user)

        # Cache the results for future use
        try:
            from core.dependency_injection import service_locator

            service_locator.project_service.cache_projects(user, queryset)
            logger.debug(f"Cached project queryset - user: {user.id}")
        except Exception as e:
            logger.warning(f"Failed to cache project queryset: {e}")

        return queryset


class CreateListProjectView(BaseProjectView, ListCreateAPIView):
    """
    Create and list projects with caching for improved performance.
    """

    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = ProjectFilter
    search_fields = ["name", "status"]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return ProjectListSerializer
        return super().get_serializer_class()

    @silk_profile(name="ProjectView Create")
    def create(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        response = super().create(request, *args, **kwargs)

        # Clear all project-related caches for the user
        if response.status_code == status.HTTP_201_CREATED:
            project_id = response.data.get("id")
            if project_id:
                service_locator.project_service.clear_project_caches_after_write_operation(
                    request.user, project_id
                )

        return response

    @silk_profile(name="ProjectView List")
    def list(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        # Check if caching is disabled
        if request.query_params.get("disable_cache"):
            return self._get_uncached_response(request, *args, **kwargs)

        # Try to get cached response with comprehensive error handling
        (
            cached_response,
            _,
        ) = service_locator.project_service.get_cached_project_response_with_fallback(
            request
        )
        if cached_response is not None:
            return Response(cached_response)

        # Cache miss - get fresh data
        logger.debug(f"Cache miss for project list - user: {request.user.id}")
        return self._get_and_cache_response(
            request, service_locator, *args, **kwargs
        )

    def _get_uncached_response(self, request, *args, **kwargs):
        """Get response without caching."""
        return super().list(request, *args, **kwargs)

    def _get_and_cache_response(
        self, request, service_locator, *args, **kwargs
    ):
        """Get response and cache it."""

        from core.dependency_injection import service_locator

        response = super().list(request, *args, **kwargs)

        # Only cache successful responses
        if response.status_code == 200:
            service_locator.project_service.cache_project_response_with_logging(
                request, response.data
            )

        return response


class RetrieveUpdateDestroyProjectView(
    CreateRecentActivityMixin, BaseProjectView, RetrieveUpdateDestroyAPIView
):
    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context

    def get_serializer(self, *args, **kwargs):
        kwargs["context"] = self.get_serializer_context()
        return super().get_serializer(*args, **kwargs)

    def get_category(self):
        return Features.PROJECTS

    def retrieve(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        project_id = kwargs.get("pk")
        disable_cache = request.query_params.get("disable_cache")

        # Skip cache if explicitly disabled
        if disable_cache:
            logger.debug(
                f"Cache disabled for project detail - project: {project_id}"
            )
            return super().retrieve(request, *args, **kwargs)

        # Try to get cached response with comprehensive error handling
        (
            cached_response,
            _,
        ) = service_locator.project_service.get_cached_project_detail_with_fallback(
            request, project_id
        )
        if cached_response is not None:
            return Response(cached_response)

        # Cache miss - get fresh data
        response = super().retrieve(request, *args, **kwargs)

        # Cache the response data only for successful responses
        if response.status_code == status.HTTP_200_OK:
            service_locator.project_service.cache_project_detail_with_logging(
                request, response.data, project_id
            )

        return response

    def update(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        project_id = kwargs.get("pk")

        # Get the response from the parent method first
        response = super().update(request, *args, **kwargs)

        # Clear caches for successful updates
        if response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_204_NO_CONTENT,
        ]:
            service_locator.project_service.clear_project_caches_after_write_operation(
                request.user, project_id
            )

        return response

    def delete(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        project_id = kwargs.get("pk")

        # Perform the soft delete
        instance = self.get_object()
        instance.is_deleted = True
        instance.save()

        # Clear caches after successful deletion
        service_locator.project_service.clear_project_caches_after_write_operation(
            request.user, project_id
        )

        return Response(status=status.HTTP_204_NO_CONTENT)


class ProjectCollaboratorsView(BaseProjectView, ListAPIView):
    serializer_class = InvitedUserSerializer

    def get(self, request, *args, **kwargs):
        project = get_object_or_404(Project, pk=kwargs.get("pk"))
        current_user = request.user

        # Get project creator
        users = [project.created_by]

        # Get all invited users who accepted
        invited_users = User.objects.filter(
            projectinvite__project=project, projectinvite__status="accepted"
        )
        users.extend(invited_users)

        # Remove duplicates and current user
        unique_users = list(dict.fromkeys(users))
        if current_user in unique_users:
            unique_users.remove(current_user)

        serializer = self.serializer_class(
            unique_users, many=True, context={"request": request}
        )
        return Response(serializer.data)


class BaseProjectDocumentView:

    serializer_class = ProjectDocumentSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self, *args, **kwargs):
        project_id = self.kwargs.get("project_id")
        user = self.request.user
        try:
            project = Project.objects.get(pk=project_id)
        except (ValidationError, Project.DoesNotExist):
            return ProjectDocument.objects.none()

        if not project.user_has_access(user):
            return ProjectDocument.objects.none()

        # Filter documents by project
        documents = ProjectDocument.objects.get_document_for_project(project)

        # If the user is not the project creator, filter documents based on access level
        if project.created_by != user:
            # Get categories the user has access to
            accessible_categories = (
                DocumentCategoryAccess.objects.filter(
                    project_invite__user=user,
                    project_invite__project=project,
                    project_invite__status="accepted",
                )
                .exclude(access_level="no_access")
                .values_list("document_category", flat=True)
            )

            # Filter documents based on access level and visibility (public)
            documents = documents.filter(
                Q(category__in=accessible_categories, visibility="public")
            )

        return documents


class CreateListProjectDocumentView(
    BaseProjectDocumentView, ListCreateAPIView
):
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = ProjectDocumentFilter
    search_fields = ["category", "name", "visibility"]

    def create(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        response = super().create(request, *args, **kwargs)

        # Only clear cache if creation was successful
        if response.status_code == 201:
            # Get project_id from URL kwargs since the document belongs to a project
            project_id = kwargs.get("project_id")
            service_locator.project_service.handle_project_document_cache_invalidation(
                project_id, operation="create", request=request
            )

        return response

    def list(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        # Check for cache bypass parameter
        disable_cache = (
            request.query_params.get("disable_cache", "false").lower()
            == "true"
        )

        if not disable_cache:
            # Get project_id from URL kwargs
            project_id = kwargs.get("project_id")
            # Try to get cached response with comprehensive error handling
            (
                cached_response,
                _,
            ) = service_locator.project_service.get_cached_project_document_response_with_fallback(
                request, project_id
            )
            if cached_response is not None:
                return Response(cached_response)

        # Cache miss or cache disabled - get fresh data
        logger.debug(
            f"Cache miss for project document list - user: {request.user.id}"
        )
        return self._get_and_cache_response(
            request, service_locator, *args, **kwargs
        )

    def _get_and_cache_response(
        self, request, service_locator, *args, **kwargs
    ):
        """Get response and cache it."""
        from core.dependency_injection import service_locator

        response = super().list(request, *args, **kwargs)

        # Only cache successful responses
        if response.status_code == 200:
            # Get project_id from URL kwargs
            project_id = kwargs.get("project_id")
            service_locator.project_service.cache_project_document_response_safe(
                request, response.data, project_id
            )

        return response


class RetrieveUpdateDestroyProjectDocumentView(
    CreateRecentActivityMixin,
    BaseProjectDocumentView,
    RetrieveUpdateDestroyAPIView,
):
    def get_object(self) -> ProjectDocument:
        obj = super().get_object()
        user = self.request.user
        access_level = obj.get_user_access_level(user)

        if access_level == "no_access" or (
            obj.visibility == ProjectDocument.Visibility.PRIVATE
            and obj.project.created_by != user
        ):
            raise PermissionDenied("You don't have access to this document.")

        return obj

    def update(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        instance = self.get_object()
        access_level = instance.get_user_access_level(request.user)

        if access_level != "write":
            raise PermissionDenied(
                "You don't have write access to this document."
            )

        response = super().update(request, *args, **kwargs)

        # Clear cache after successful update
        if response.status_code == 200:
            service_locator.project_service.handle_project_document_cache_invalidation(
                instance.project.id, operation="update"
            )

        return response

    def delete(self, request, *args, **kwargs):
        from core.dependency_injection import service_locator

        instance = self.get_object()
        access_level = instance.get_user_access_level(request.user)

        if access_level != "write":
            raise PermissionDenied(
                "You don't have write access to this document."
            )

        # Store project ID before deletion
        project_id = instance.project.id

        instance.is_deleted = True
        instance.save()

        if instance.file:
            instance.file.is_deleted = True
            instance.file.save()

        # Clear cache after successful deletion
        service_locator.project_service.handle_project_document_cache_invalidation(
            project_id, operation="delete"
        )

        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_category(self):
        """
        Returns the category of the project document for the recent activity record.
        Maps the project document's category to the appropriate feature category.
        """
        # Map the project document category to the Features class
        category_map = {
            ProjectDocument.Category.PLAN_AND_ELEVATION: Features.PLAN_AND_ELEVATIONS,
            ProjectDocument.Category.ESTIMATE: Features.ESTIMATES,
            ProjectDocument.Category.CONTRACT: Features.CONTRACTS,
            ProjectDocument.Category.CHANGE_ORDER: Features.CHANGE_ORDERS,
            ProjectDocument.Category.PAYMENT_SCHEDULE: Features.PAYMENT_SCHEDULES,
            ProjectDocument.Category.PERFORMANCE_SCHEDULE: Features.PERFORMANCE_SCHEDULES,
            ProjectDocument.Category.SPECIFICATION: Features.SPECIFICATIONS,
            ProjectDocument.Category.PERMIT: Features.PERMITS,
            ProjectDocument.Category.ADDITIONAL_DOCUMENTS: Features.ADDITIONAL_DOCUMENTS,
            ProjectDocument.Category.GALLERY: Features.GALLERY,
        }

        # Get the category from the instance and map it
        instance: ProjectDocument = self.get_object()
        document_category = instance.category

        # Return the mapped category for the recent activity record
        return category_map.get(document_category, None)


class BulkDeleteProjectDocumentsView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Bulk delete project documents",
        operation_summary="Delete multiple project documents",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["document_ids"],
            properties={
                "document_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING, format="uuid"
                    ),
                    description="List of document UUIDs to delete",
                )
            },
        ),
        responses={
            200: openapi.Response(
                description="Documents successfully deleted",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "message": openapi.Schema(type=openapi.TYPE_STRING),
                        "deleted_document_ids": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_STRING, format="uuid"
                            ),
                        ),
                    },
                ),
            ),
            400: "Invalid UUIDs provided",
            403: "Insufficient permissions for one or more documents",
            404: "documents not found",
        },
    )
    def delete(self, request):
        document_ids = request.data.get("document_ids", [])

        if not document_ids:
            return Response(
                {
                    "detail": "document_ids must be provided as a list of UUIDs."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate UUID format
        try:
            document_ids = [uuid.UUID(doc_id) for doc_id in document_ids]
        except ValueError:
            return Response(
                {"detail": "Invalid UUID format in document_ids."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = request.user

        # Retrieve documents and validate permissions
        documents = ProjectDocument.objects.filter(id__in=document_ids)

        if not documents.exists():
            return Response(
                {"detail": "No matching documents found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        unauthorized_documents = []
        for document in documents:
            access_level = document.get_user_access_level(user)
            if access_level != "write":
                unauthorized_documents.append(str(document.id))

        if unauthorized_documents:
            return Response(
                {
                    "detail": "Access denied to one or more documents.",
                    "unauthorized_documents": unauthorized_documents,
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Mark documents and associated files as deleted
        for document in documents:
            document.is_deleted = True
            document.save()

            if document.file:
                document.file.is_deleted = True
                document.file.save()

        return Response(
            {
                "message": "Documents successfully deleted",
            },
            status=status.HTTP_200_OK,
        )


class BaseChangeOrderView:
    serializer_class = ChangeOrderSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")
        user = self.request.user

        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return ChangeOrder.objects.none()
        except ValidationError:
            return ChangeOrder.objects.none()

        if not project.user_has_access(user):
            return ChangeOrder.objects.none()

        # Filter change orders by project
        change_orders = ChangeOrder.objects.filter(project=project)

        # If the user is not the project creator, filter change orders based on access level
        if project.created_by != user:
            # Get categories the user has access to
            accessible_categories = (
                DocumentCategoryAccess.objects.filter(
                    project_invite__user=user,
                    project_invite__project=project,
                    project_invite__status="accepted",
                )
                .exclude(access_level="no_access")
                .values_list("document_category", flat=True)
            )

            # Check if the user has access to the change order category
            if ProjectDocument.Category.CHANGE_ORDER in accessible_categories:
                # Filter change orders based on visibility (public)
                change_orders = change_orders.filter(visibility="public")
            else:
                return ChangeOrder.objects.none()
            # If the user does not have access to the change order category, return none
        return change_orders


class CreateListChangeOrderView(BaseChangeOrderView, ListCreateAPIView):
    filter_backends = [DjangoFilterBackend]
    filterset_class = ChangeOrderFilter

    def perform_create(self, serializer):
        project_id = self.kwargs.get("project_id")
        project = get_object_or_404(Project, pk=project_id)

        if project.created_by != self.request.user:
            raise PermissionDenied(
                "You don't have permission to create change orders for this project."
            )

        serializer.save(project=project)


class RetrieveUpdateDestroyChangeOrderView(
    CreateRecentActivityMixin,
    BaseChangeOrderView,
    RetrieveUpdateDestroyAPIView,
):
    def get_object(self):
        obj = super().get_object()
        user = self.request.user

        if obj.project.created_by != user:
            # Check if user has access to the change order category
            has_access = (
                DocumentCategoryAccess.objects.filter(
                    project_invite__user=user,
                    project_invite__project=obj.project,
                    project_invite__status="accepted",
                    document_category=ProjectDocument.Category.CHANGE_ORDER,
                )
                .exclude(access_level="no_access")
                .exists()
            )

            if not has_access or (
                obj.visibility == ChangeOrder.Visibility.PRIVATE
            ):
                raise PermissionDenied(
                    "You don't have access to this change order."
                )

        return obj

    def update(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.project.created_by != request.user:
            raise PermissionDenied(
                "You don't have permission to update this change order."
            )

        return super().update(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.project.created_by != request.user:
            raise PermissionDenied(
                "You don't have permission to delete this change order."
            )

        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_category(self):
        return Features.CHANGE_ORDERS


class GenerateChangeOrderPDFView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, project_id, change_order_id):
        project = get_object_or_404(Project, id=project_id)
        change_order = get_object_or_404(
            ChangeOrder, id=change_order_id, project=project
        )

        # Check if user has access to the project and change order
        if not project.user_has_access(request.user):
            return Response(
                {"error": "You don't have access to this project"},
                status=status.HTTP_403_FORBIDDEN,
            )

        company = Company.objects.get_user_company(self.request.user)

        project_creator = project.created_by

        context = {
            "company": company,
            "project": project,
            "change_order": change_order,
            "project_creator": project_creator,
            "current_user": request.user,
            "current_date": timezone.now(),
        }

        template = get_template("change_order_template.html")
        html = template.render(context)

        # Create a PDF
        result = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result)

        if not pdf.err:
            response = HttpResponse(
                result.getvalue(), content_type="application/pdf"
            )
            response[
                "Content-Disposition"
            ] = f'attachment; filename="change_order_{change_order.id}.pdf"'
            return response

        return HttpResponse("Error generating PDF", status=400)


class ProjectSubcontractorView(BaseProjectView, RetrieveUpdateDestroyAPIView):
    serializer_class = ProjectSubcontractorSerializer


# Documentation of the search api
query_param = openapi.Parameter(
    "category",
    openapi.IN_QUERY,
    description=str(ProjectDocument.Category.ALL),
    type=openapi.TYPE_STRING,
    required=True,
)


class CompanyProjectDocumentView(ListAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = ProjectDocumentSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = ProjectDocumentFilter
    search_fields = ["category", "name", "visibility"]

    def get_queryset(self):
        category = self.request.query_params.get("category")
        if not category:
            raise ParseError("Category is required")

        user = self.request.user

        # Get projects created by the user
        created_projects = Project.objects.filter(
            created_by=user, is_deleted=False
        )

        # Get projects the user has been invited to
        invited_projects = Project.objects.filter(
            projectinvite__user=user,
            projectinvite__status="accepted",
            is_deleted=False,
        )

        # Initialize the document queryset
        documents = ProjectDocument.objects.none()

        # Get documents for projects created by the user
        if created_projects.exists():
            created_project_docs = ProjectDocument.objects.filter(
                category=category, project__in=created_projects
            )
            documents = documents | created_project_docs

        # Get documents for invited projects with proper access levels
        if invited_projects.exists():
            # Get categories the user has access to for each invited project
            accessible_categories = DocumentCategoryAccess.objects.filter(
                project_invite__user=user,
                project_invite__project__in=invited_projects,
                project_invite__status="accepted",
                document_category=category,
            ).exclude(access_level="no_access")

            # Get projects where user has access to the requested category
            projects_with_access = accessible_categories.values_list(
                "project_invite__project", flat=True
            )

            # Filter documents based on access level and visibility
            invited_project_docs = ProjectDocument.objects.filter(
                category=category,
                project__in=projects_with_access,
                visibility="public",
            )
            documents = documents | invited_project_docs

        return documents.distinct()

    @swagger_auto_schema(
        manual_parameters=[query_param],
    )
    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        grouped_data = self.group_project_documents(queryset)
        return Response(grouped_data, status=status.HTTP_200_OK)

    def group_project_documents(self, queryset):
        grouped_data = {}
        user = self.request.user
        category = self.request.query_params.get("category")

        for project_document in queryset:
            project = project_document.project
            project_id = project.id
            project_name = project.name

            # Skip if the user doesn't have proper access
            if project.created_by != user:
                # Check if user has access to this category in this project
                has_access = (
                    DocumentCategoryAccess.objects.filter(
                        project_invite__user=user,
                        project_invite__project=project,
                        project_invite__status="accepted",
                        document_category=category,
                    )
                    .exclude(access_level="no_access")
                    .exists()
                )

                if not has_access:
                    continue

            project_info = grouped_data.setdefault(
                project_id,
                {"name": project_name, "id": project_id, "files": []},
            )
            if len(project_info["files"]) < 5:
                project_info["files"].append(
                    ProjectDocumentSerializer(project_document).data
                )

        return list(grouped_data.values())


class ListSubcontractorsByProjectView(ListAPIView):
    serializer_class = SubcontractSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        user = self.request.user
        return Project.objects.filter(created_by=user)

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        grouped_data = self.group_subcontractors_by_project(queryset)
        return Response(grouped_data, status=status.HTTP_200_OK)

    def group_subcontractors_by_project(self, queryset):
        grouped_data = {}
        for project in queryset:
            project_id = project.id
            project_name = project.name

            project_info = grouped_data.setdefault(
                project_id,
                {
                    "project_name": project_name,
                    "project_id": project_id,
                    "subcontractors": [],
                },
            )
            subcontractors = project.subcontractors.all()
            for subcontractor in subcontractors:
                project_info["subcontractors"].append(
                    SubcontractSerializer(subcontractor).data
                )
        return list(grouped_data.values())


class CreateProjectInviteView(CreateAPIView):
    serializer_class = ProjectInviteSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.core_service = CoreService(sendgrid_service=SendGridService())

    def send_invite_email(
        self,
        email,
        project: Project,
        sender,
        invite_id,
        template_path,
        is_new_user,
        invitee_user,
    ):

        subject = (
            f"You’ve been invited to collaborate on {project.name} on tuulbox"
        )

        redirect_url = f"{settings.FRONTEND_URL}accept-invite-route?inviteId={invite_id}?is_new_user={is_new_user}?email={email}"

        context = {
            "project_name": project.name,
            "project_owner": project.created_by.full_name,
            "invitee_user": invitee_user,
            "redirect_url": redirect_url,
            "current_year": datetime.now().year,
            "play_store_app_url": settings.PLAY_STORE_APP_URL,
            "app_store_app_url": settings.APP_STORE_APP_URL,
        }

        self.core_service.send_email(
            subject=subject,
            template_path=template_path,
            template_context=context,
            to_emails=[email],
        )

    def perform_create(self, serializer):
        project = get_object_or_404(Project, id=self.kwargs["project_id"])

        # Check if the current user is the project creator
        if project.created_by != self.request.user:
            raise PermissionDenied(
                "You are not allowed to invite others to this project."
            )

        invitee_emails = self.request.data.get("invitee_emails", [])
        invites = []

        # Extract user statuses from the context
        user_statuses = serializer.context.get("user_status", [])

        for i, email in enumerate(invitee_emails):
            serializer = self.get_serializer(
                data={**self.request.data, "invitee_emails": [email]}
            )
            serializer.is_valid(raise_exception=True)

            # Save the invite and get the invitee user creation status
            invite = serializer.save(project=project)
            invitee_user = invite.user

            # Determine if the user is new or existing using the user_status list
            is_new_user = user_statuses[i] if i < len(user_statuses) else False

            # Select the correct email template based on the user's status
            template_path = (
                "invite_new_user.html"
                if is_new_user
                else "invite_existing_user.html"
            )

            # Send the invite email with the appropriate template
            self.send_invite_email(
                email,
                project,
                self.request.user,
                invite.id,
                template_path,
                is_new_user=is_new_user,
                invitee_user=invitee_user.first_name,
            )
            invites.append(invite)

            from utils.utils import create_or_update_notification

            create_or_update_notification(
                user=invite.user,
                category=Notification.Category.PENDING_INVITES,
                item_id=invite.id,
            )

            # Trigger the notification task

            notify_user_about_new_invite.delay(invite.id)

        return invites


class ListInvitationsSentView(ListAPIView):
    serializer_class = ProjectInviteSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ProjectInviteFilter
    ordering_fields = ["created_at"]

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")
        return ProjectInvite.objects.filter(
            project__created_by=self.request.user, project_id=project_id
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["view_type"] = "sent"
        return context


class ListInvitationsReceivedView(ListAPIView):
    serializer_class = ProjectInviteSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ProjectInviteFilter
    ordering_fields = ["created_at"]

    def get_queryset(self):
        return ProjectInvite.objects.filter(user=self.request.user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["view_type"] = "received"
        return context


class RetrieveUpdateDestroyInvitationView(RetrieveUpdateDestroyAPIView):
    queryset = ProjectInvite.objects.all()
    serializer_class = ProjectInviteSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        try:
            invite = ProjectInvite.objects.get(id=self.kwargs["invite_id"])
        except ProjectInvite.DoesNotExist as err:
            raise NotFound() from err

        user = self.request.user
        method = self.request.method
        created_by = invite.project.created_by
        invited_user = invite.user

        if invited_user != user and created_by != user:

            if method == "GET":
                raise NotFound()
            else:
                raise PermissionDenied()

        return invite


class ProjectInviteStatusUpdateView(NotificationCleanupMixin, UpdateAPIView):
    queryset = ProjectInvite.objects.all()
    serializer_class = ProjectInviteStatusSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        invite = get_object_or_404(ProjectInvite, id=self.kwargs["pk"])

        # Ensure only the invited user can accept or reject the invitation
        if invite.user != self.request.user:
            raise PermissionDenied(
                "You are not allowed to accept or reject this invitation."
            )

        # Check if the invite is older than 7 days and still pending
        expiration_date = invite.created_at + timedelta(days=7)
        if timezone.now() > expiration_date and invite.status in [
            "pending",
            "expired",
        ]:
            # Use the mixin to clear notifications for this invite.
            self.cleanup_item_notifications(
                str(invite.id), [Notification.Category.PENDING_INVITES]
            )

            # Update the invite status to expired
            invite.status = "expired"
            invite.save()

            # Notify the user that the invite has expired.
            raise ValidationError(
                "The invitation has expired and cannot be accepted or rejected."
            )

        return invite

    def perform_update(self, serializer):
        instance = serializer.save()

        # If the invite status changes from pending to accepted or rejected,
        # clear the notifications for this invite.
        if instance.status in ["accepted", "rejected"]:
            self.cleanup_item_notifications(
                str(instance.id), [Notification.Category.PENDING_INVITES]
            )


class CreateProjectShareView(CreateAPIView):
    serializer_class = ProjectShareSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.core_service = CoreService(sendgrid_service=SendGridService())

    def send_share_email(self, email, project: Project, permission_id):
        subject = f"{project.created_by.first_name + ' ' + project.created_by.last_name} granted you access to a project on tuulbox"
        template_path = "share_email_template.html"
        context = {
            "project_name": project.name,
            "project_owner": project.created_by.first_name
            + " "
            + project.created_by.last_name,
            "redirect_url": f"{settings.FRONTEND_URL}?permissionId={permission_id}",
            "current_year": datetime.now().year,
        }

        self.core_service.send_email(
            subject=subject,
            template_path=template_path,
            template_context=context,
            to_emails=[email],
        )

    def perform_create(self, serializer):
        project = get_object_or_404(Project, id=self.kwargs["project_id"])
        serializer.context["project"] = project
        project_share = serializer.save()

        # Send email to all addresses in the share instance
        for email in project_share.user_emails:
            self.send_share_email(
                email, project_share.project, project_share.permission_id
            )

        return Response(serializer.data, status=status.HTTP_201_CREATED)


class RetrieveUpdateDestroyProjectShareView(RetrieveUpdateDestroyAPIView):
    queryset = ProjectShare.objects.all()
    serializer_class = ProjectShareSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.core_service = CoreService(sendgrid_service=SendGridService())

    def get_object(self):
        invite = get_object_or_404(ProjectShare, id=self.kwargs["share_id"])
        if invite.project.created_by != self.request.user:
            raise PermissionDenied(
                "You do not have permission to access this invite."
            )
        return invite

    def send_share_email(self, email, project: Project, permission_id):
        subject = f"{project.created_by.first_name + ' ' + project.created_by.last_name} granted you access to a project on tuulbox"
        template_path = "share_email_template.html"
        context = {
            "project_name": project.name,
            "project_owner": project.created_by.first_name
            + " "
            + project.created_by.last_name,
            "redirect_url": f"https://tuulbox.app/?permissionId={permission_id}",
            "current_year": datetime.now().year,
        }

        self.core_service.send_email(
            subject=subject,
            template_path=template_path,
            template_context=context,
            to_emails=[email],
        )

    def perform_update(self, serializer):
        project_share = self.get_object()
        serializer.save()

        # Send email to all addresses in the share instance
        for email in project_share.user_emails:
            self.send_share_email(
                email, project_share.project, project_share.permission_id
            )

        return Response(serializer.data, status=status.HTTP_200_OK)


class ListShareSentView(ListAPIView):
    serializer_class = ProjectShareSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")

        return ProjectShare.objects.filter(
            project__created_by=self.request.user, project_id=project_id
        )


class RetrieveProjectByPermissionIdView(RetrieveAPIView):
    permission_classes = [AllowAny]
    serializer_class = ProjectPermissionSerializer

    def get_object(self):
        permission_id = self.kwargs.get("permission_id")
        project_share = get_object_or_404(
            ProjectShare, permission_id=permission_id
        )
        return project_share.project

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["permission_id"] = self.kwargs.get("permission_id")
        return context
