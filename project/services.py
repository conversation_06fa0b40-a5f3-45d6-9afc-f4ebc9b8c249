import logging
from itertools import combinations

from accounts.models import User
from django.conf import settings
from django.core.cache import cache
from django.db.models import Q
from general.cache_keys import REDIS_CACHE_KEY
from project.models import Project

logger = logging.getLogger(__name__)


class ProjectService:
    CACHE_VERSION = "v1"  # Increment when cache structure changes

    def _generate_cache_key(
        self,
        user_id,
        query_params=None,
        key_type="list",
        project_id=None,
        cache_version=None,
    ):
        """Generate a consistent cache key with collision resistance."""
        import hashlib

        # Use provided cache_version or fall back to class default
        version = cache_version or self.CACHE_VERSION

        # Sort query params for consistent hashing
        if query_params:
            sorted_params = sorted(query_params.items())
            params_str = "&".join(f"{k}={v}" for k, v in sorted_params)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        else:
            params_hash = "default"

        # Include project_id in cache key if provided
        if project_id:
            return f"project_{key_type}_{version}_{user_id}_{project_id}_{params_hash}"
        else:
            return f"project_{key_type}_{version}_{user_id}_{params_hash}"

    def cache_project_response(self, request, response_data):
        """Cache project list response with improved error handling."""
        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                request.user.id, request.query_params
            )
            cache.set(cache_key, response_data, settings.PROJECT_CACHE_TIMEOUT)
        except Exception as e:

            logger.warning(f"Failed to cache project response: {e}")

    def get_cached_project_response(self, request):
        """Retrieve cached project list response."""
        try:
            cache_key = self._generate_cache_key(
                request.user.id, request.query_params
            )
            return cache.get(cache_key)
        except Exception as e:

            logger.warning(f"Failed to retrieve cached project response: {e}")
            return None

    def clear_project_response_cache(self, user: User):
        """Clear all project response caches for a user."""
        try:
            if hasattr(cache, "delete_pattern"):
                # Use pattern deletion if available (Redis)
                pattern = f"project_list_{self.CACHE_VERSION}_{user.id}_*"
                cache.delete_pattern(pattern)
            else:
                # Fallback for other cache backends
                # Note: This is less efficient but works with all backends

                logger.info(
                    f"Pattern deletion not available, cache may persist for user {user.id}"
                )
        except Exception as e:

            logger.warning(f"Failed to clear project response cache: {e}")

    def cache_project_document_response(
        self, request, response_data, project_id=None, cache_version=None
    ):
        """Cache project document list response with improved error handling."""
        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                request.user.id,
                request.query_params,
                key_type="document_list",
                project_id=project_id,
                cache_version=cache_version,
            )
            cache.set(cache_key, response_data, settings.PROJECT_CACHE_TIMEOUT)
        except Exception as e:

            logger.warning(f"Failed to cache project document response: {e}")

    def get_cached_project_document_response(
        self, request, project_id, cache_version=None
    ):
        """Retrieve cached project document list response."""

        try:
            cache_key = self._generate_cache_key(
                request.user.id,
                request.query_params,
                key_type="document_list",
                project_id=project_id,
                cache_version=cache_version,
            )
            return cache.get(cache_key)
        except Exception as e:

            logger.warning(
                f"Failed to retrieve cached project document response: {e}"
            )
            return None

    def clear_project_document_cache(self, user: User, cache_version=None):
        """Clear all project document response caches for a user."""
        try:
            if hasattr(cache, "delete_pattern"):
                # Use pattern deletion if available (Redis)
                # Clear both old format (without project_id) and new format (with project_id)
                patterns = [
                    f"project_document_list_{cache_version}_{user.id}_*",  # Old format
                    f"project_document_list_{cache_version}_{user.id}_*_*",  # New format with project_id
                ]
                for pattern in patterns:
                    cache.delete_pattern(pattern)
            else:
                # Fallback for other cache backends
                # Note: This is less efficient but works with all backends

                logger.info(
                    f"Pattern deletion not available, cache may persist for user {user.id}"
                )
        except Exception as e:

            logger.warning(f"Failed to clear project document cache: {e}")

    def invalidate_project_document_caches_for_project(
        self, project_id, cache_version=None
    ):
        """Invalidate project document caches for all users who have access to a specific project."""
        try:
            from project.models import Project

            project = (
                Project.objects.select_related("created_by")
                .prefetch_related("projectinvite_set__user")
                .get(id=project_id)
            )

            # Get all users who have access to this project
            affected_users = set()
            affected_users.add(project.created_by)

            # Add invited users with accepted status
            for invite in project.projectinvite_set.filter(status="accepted"):
                affected_users.add(invite.user)

            # Clear cache for all affected users
            for user in affected_users:
                try:
                    self.clear_project_document_cache(user, cache_version)
                except Exception as e:

                    logger.warning(
                        f"Failed to clear project document cache for user {user.id}: {e}"
                    )

            logger.info(
                f"Invalidated project document caches for project {project_id}, affected {len(affected_users)} users"
            )

        except Exception as e:

            logger.error(
                f"Failed to invalidate project document caches for project {project_id}: {e}"
            )

    def get_cached_project_document_response_with_fallback(
        self, request, project_id=None, cache_version=None
    ):
        """
        Get cached project document response with comprehensive error handling and fallback.

        Returns:
            tuple: (cached_data, cache_status)
            - cached_data: The cached response data or None if not found/error
            - cache_status: 'hit', 'miss', or 'error'
        """
        try:
            cached_response = self.get_cached_project_document_response(
                request, project_id, cache_version=cache_version
            )
            if cached_response is not None:
                logger.debug(
                    f"Cache hit for project document list - user: {request.user.id}"
                )
                return cached_response, "hit"
            return None, "miss"
        except Exception as e:
            logger.warning(
                f"Cache retrieval failed for project document list: {e}"
            )
            # Clear potentially corrupted cache
            try:
                self.clear_project_document_cache(request.user, cache_version)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted project document cache: {clear_error}"
                )
            return None, "error"

    def handle_project_document_cache_invalidation(
        self,
        project_id,
        operation="operation",
        request=None,
        cache_version=None,
    ):
        """
        Handle cache invalidation after project document operations.

        Args:
            project_id: The ID of the project the document belongs to
            operation: The operation type ('create', 'update', 'delete') for logging purposes
            request: The HTTP request object (required for 'create' operation to clear user cache)
        """
        try:
            # For create operations, also clear cache for the specific user
            if operation == "create" and request:
                self.clear_project_document_cache(request.user, cache_version)
                logger.debug(
                    f"Cleared project document cache after {operation} - user: {request.user.id}"
                )

            # Invalidate caches for all users who have access to this project
            if project_id:
                self.invalidate_project_document_caches_for_project(project_id)
                logger.debug(
                    f"Cleared project document cache after {operation} - project: {project_id}"
                )

        except Exception as e:
            logger.warning(
                f"Failed to clear project document cache after {operation}: {e}"
            )

    def cache_project_document_response_safe(
        self, request, response_data, project_id, cache_version=None
    ):
        """
        Safely cache project document response with error handling.

        Args:
            request: The HTTP request object
            response_data: The response data to cache
            project_id: The project ID to include in cache key
            cache_version: Optional cache version to use
        """
        try:
            self.cache_project_document_response(
                request, response_data, project_id, cache_version=cache_version
            )
            logger.debug(
                f"Cached project document list response - user: {request.user.id}, project: {project_id}"
            )
        except Exception as e:
            logger.warning(
                f"Failed to cache project document list response: {e}"
            )

    def get_project_document_cache_stats(self, user: User):
        """Get project document cache statistics for monitoring and debugging."""
        stats = {
            "user_id": user.id,
            "cache_version": self.CACHE_VERSION,
            "document_response_caches": [],
        }

        try:
            # Check common response cache patterns for project documents
            common_params = [
                {},  # No params
                {"search": "test"},  # Search param
                {"category": "document"},  # Category filter
                {"visibility": "public"},  # Visibility filter
                {"ordering": "name"},  # Ordering param
            ]

            for params in common_params:
                cache_key = self._generate_cache_key(
                    user.id, params, key_type="document_list"
                )
                exists = cache.get(cache_key) is not None
                stats["document_response_caches"].append(
                    {"params": params, "exists": exists, "key": cache_key}
                )

        except Exception as e:

            logger.warning(
                f"Failed to get project document cache stats for user {user.id}: {e}"
            )
            stats["error"] = str(e)

        return stats

    def warm_project_document_cache(
        self, user: User, common_query_params=None
    ):
        """Proactively warm the project document cache for a user with common query patterns."""
        if common_query_params is None:
            common_query_params = [
                {},  # Default list
                {"ordering": "name"},  # Name ordering
                {"ordering": "-created_at"},  # Recent first
                {"category": "document"},  # Document category
                {"visibility": "public"},  # Public documents
            ]

        try:
            from django.test import RequestFactory

            # Create a mock request for cache key generation
            factory = RequestFactory()

            for params in common_query_params:
                try:
                    # Create mock request with query parameters
                    request = factory.get("/project-documents/", params)
                    request.user = user

                    # Generate cache key
                    cache_key = self._generate_cache_key(
                        user.id, params, key_type="document_list"
                    )

                    # Check if already cached
                    if cache.get(cache_key) is not None:
                        continue

                    # This would typically involve calling the actual view logic
                    # For now, we'll just log that we would warm this cache

                    logger.info(
                        f"Would warm project document cache for user {user.id} with params: {params}"
                    )

                except Exception as e:

                    logger.warning(
                        f"Failed to warm project document cache for params {params}: {e}"
                    )

            logger.info(
                f"Successfully attempted to warm project document cache for user {user.id}"
            )

        except Exception as e:

            logger.error(
                f"Failed to warm project document cache for user {user.id}: {e}"
            )

    def cache_project_detail(self, request, response_data):
        """Cache project detail response with improved error handling."""
        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                request.user.id, key_type=f"detail_{response_data['id']}"
            )
            cache.set(cache_key, response_data, settings.PROJECT_CACHE_TIMEOUT)
        except Exception as e:

            logger.warning(f"Failed to cache project detail response: {e}")

    def cache_project_detail_with_logging(
        self, request, response_data, project_id
    ):
        """
        Cache project detail response with comprehensive error handling and logging.

        Args:
            request: The HTTP request object
            response_data: The response data to cache
            project_id: The project ID for logging purposes

        Returns:
            bool: True if caching was successful, False otherwise
        """

        if not response_data:
            return False

        try:
            self.cache_project_detail(request, response_data)
            logger.debug(
                f"Cached project detail response - user: {request.user.id}, project: {project_id}"
            )
            return True
        except Exception as e:
            logger.warning(
                f"Failed to cache project detail response - user: {request.user.id}, project: {project_id}: {e}"
            )
            return False

    def get_cached_project_detail(self, request, project_id):
        """Retrieve cached project detail response with improved error handling."""
        try:
            cache_key = self._generate_cache_key(
                request.user.id, key_type=f"detail_{project_id}"
            )
            return cache.get(cache_key)
        except Exception as e:

            logger.warning(
                f"Failed to retrieve cached project detail response: {e}"
            )
            return None

    def get_cached_project_detail_with_fallback(self, request, project_id):
        """
        Retrieve cached project detail with comprehensive error handling and cache corruption cleanup.

        Returns:
            tuple: (cached_data, cache_status)
            - cached_data: The cached response data or None if not found/error
            - cache_status: 'hit', 'miss', or 'error'
        """

        try:
            cached_response = self.get_cached_project_detail(
                request, project_id
            )
            if cached_response is not None:
                logger.debug(
                    f"Cache hit for project detail - user: {request.user.id}, project: {project_id}"
                )
                return cached_response, "hit"
            else:
                logger.debug(
                    f"Cache miss for project detail - user: {request.user.id}, project: {project_id}"
                )
                return None, "miss"
        except Exception as e:
            logger.warning(
                f"Cache retrieval failed for project detail - user: {request.user.id}, project: {project_id}: {e}"
            )
            # Clear potentially corrupted cache
            try:
                self.clear_project_detail_cache(request.user, project_id)
                logger.debug(
                    f"Cleared corrupted project detail cache - user: {request.user.id}, project: {project_id}"
                )
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted project detail cache - user: {request.user.id}, project: {project_id}: {clear_error}"
                )
            return None, "error"

    def clear_project_detail_cache(self, user: User, project_id):
        """Clear project detail cache with improved error handling."""
        try:
            cache_key = self._generate_cache_key(
                user.id, key_type=f"detail_{project_id}"
            )
            cache.delete(cache_key)
        except Exception as e:

            logger.warning(f"Failed to clear project detail cache: {e}")

    def clear_all_project_detail_caches_for_user(self, user: User):
        """Clear all project detail caches for a user using pattern deletion."""
        try:
            if hasattr(cache, "delete_pattern"):
                # Use pattern deletion if available (Redis)
                pattern = f"project_detail_{self.CACHE_VERSION}_{user.id}_*"
                cache.delete_pattern(pattern)
            else:
                # Fallback for other cache backends

                logger.info(
                    f"Pattern deletion not available, project detail caches may persist for user {user.id}"
                )
        except Exception as e:

            logger.warning(
                f"Failed to clear project detail caches for user {user.id}: {e}"
            )

    def cache_projects(self, user: User, projects_data: list[Project]):
        collaborators = self.cache_keys(user)
        key = REDIS_CACHE_KEY.get_project_key(collaborators)

        cache.set(key, projects_data, settings.PROJECT_CACHE_TIMEOUT)

    def get_cached_projects(self, user):
        collaborators = self.cache_keys(user)
        key = REDIS_CACHE_KEY.get_project_key(collaborators)

        return cache.get(key)

    def clear_project_cache(self, user: User):
        """Clear project cache with improved efficiency and error handling."""
        try:
            collaborators = self.cache_keys(user)

            # Clear response cache first
            self.clear_project_response_cache(user)

            # Loop through all non-empty subsets of collaborators
            for i in range(1, len(collaborators) + 1):
                for combo in combinations(collaborators, i):
                    key = REDIS_CACHE_KEY.get_project_key(list(combo))
                    try:
                        cache.delete(key)
                    except Exception as e:

                        logger.warning(
                            f"Failed to delete cache key {key}: {e}"
                        )

            # Trigger background task to regenerate cache
            try:
                from project.tasks import regenerate_project_cache

                regenerate_project_cache.delay(user.id)
            except Exception as e:

                logger.warning(
                    f"Failed to trigger cache regeneration task: {e}"
                )

        except Exception as e:

            logger.error(
                f"Failed to clear project cache for user {user.id}: {e}"
            )

    def warm_project_cache(self, user: User):
        """Proactively warm the cache for a user."""
        try:
            from project.models import Project

            # Get fresh data with optimized query
            created_projects = (
                Project.objects.filter(created_by=user, is_deleted=False)
                .select_related("created_by")
                .prefetch_related("projectinvite_set")
                .order_by("created_at")
            )

            invited_projects = (
                Project.objects.filter(
                    projectinvite__user=user,
                    projectinvite__status="accepted",
                    is_deleted=False,
                )
                .select_related("created_by")
                .prefetch_related("projectinvite_set")
                .order_by("created_at")
            )

            queryset = created_projects | invited_projects
            queryset = queryset.distinct()

            # Cache the results
            self.cache_projects(user, queryset)

            logger.info(
                f"Successfully warmed project cache for user {user.id}"
            )

        except Exception as e:

            logger.error(
                f"Failed to warm project cache for user {user.id}: {e}"
            )

    def invalidate_project_caches_for_project(self, project_id):
        """Invalidate caches for all users who have access to a specific project."""
        try:
            from project.models import Project

            project = (
                Project.objects.select_related("created_by")
                .prefetch_related("projectinvite_set__user")
                .get(id=project_id)
            )

            # Get all users who have access to this project
            affected_users = set()
            affected_users.add(project.created_by)

            # Add invited users with accepted status
            for invite in project.projectinvite_set.filter(status="accepted"):
                affected_users.add(invite.user)

            # Clear cache for all affected users
            for user in affected_users:
                try:
                    # Clear project list caches
                    self.clear_project_response_cache(user)
                    self.clear_project_cache(user)

                    # Clear project detail cache for this specific project
                    self.clear_project_detail_cache(user, project_id)

                except Exception as e:

                    logger.warning(
                        f"Failed to clear cache for user {user.id}: {e}"
                    )

            logger.info(
                f"Invalidated project caches for project {project_id}, affected {len(affected_users)} users"
            )

        except Exception as e:

            logger.error(
                f"Failed to invalidate project caches for project {project_id}: {e}"
            )

    def get_project_collaborators(self, user: User, status: str = "accepted"):
        created_users = User.objects.filter(
            id=user.id, project__created_by=user
        )
        invited_users = User.objects.filter(
            Q(projectinvite__project__created_by=user)
            & Q(projectinvite__status=status)
        )
        collaborators = created_users | invited_users
        return collaborators.distinct()

    def cache_keys(self, user: User):

        collaborators = self.get_project_collaborators(user)

        return [
            str(user_id)
            for user_id in collaborators.values_list("id", flat=True)
        ]

    def clear_project_caches_after_write_operation(
        self, user: User, project_id
    ):
        """Clear project caches after successful write operation with comprehensive error handling."""
        try:
            # Clear the specific user's cache first
            self.clear_project_detail_cache(user, project_id)

            logger.debug(
                f"Cleared project detail cache for user - user: {user.id}, project: {project_id}"
            )

            # Then invalidate caches for all users who have access to this project
            self.invalidate_project_caches_for_project(project_id)
            logger.debug(
                f"Invalidated project caches for all users - project: {project_id}"
            )
        except Exception as e:

            logger.warning(
                f"Failed to clear project caches after update - user: {user.id}, project: {project_id}: {e}"
            )

    def get_cached_project_response_with_fallback(self, request):
        """Get cached project response with comprehensive error handling and fallback."""
        try:
            cached_response = self.get_cached_project_response(request)
            if cached_response is not None:
                logger.debug(
                    f"Cache hit for project list - user: {request.user.id}"
                )
                return cached_response, "hit"
            return None, "miss"
        except Exception as e:
            logger.warning(f"Cache retrieval failed for project list: {e}")
            # Clear potentially corrupted cache
            try:
                self.clear_project_response_cache(request.user)
            except Exception as clear_error:
                logger.error(f"Failed to clear corrupted cache: {clear_error}")
            return None, "error"

    def cache_project_response_with_logging(self, request, response_data):
        """Cache project response with comprehensive error handling and logging."""
        try:
            self.cache_project_response(request, response_data)

            logger.debug(
                f"Cached project list response - user: {request.user.id}"
            )
            return True
        except Exception as e:
            logger.warning(f"Failed to cache project list response: {e}")
            return False
