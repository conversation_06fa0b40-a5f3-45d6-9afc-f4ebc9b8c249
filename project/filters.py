import django_filters
from contacts.models import Subcontractor

from .models import ChangeOrder
from .models import Project
from .models import ProjectDocument
from .models import ProjectInvite


class ProjectDocumentFilter(django_filters.FilterSet):
    visibility = django_filters.ChoiceFilter(
        choices=ProjectDocument.Visibility.CHOICES, label="Visibility"
    )
    category = django_filters.CharFilter(lookup_expr="icontains")
    name = django_filters.CharFilter(lookup_expr="icontains")
    subcontractor = django_filters.ModelChoiceFilter(
        queryset=Subcontractor.objects.all(), label="Subcontractor"
    )

    class Meta:
        model = ProjectDocument
        fields = ["category", "name", "visibility", "subcontractor"]


class ChangeOrderFilter(django_filters.FilterSet):
    visibility = django_filters.ChoiceFilter(
        choices=ChangeOrder.Visibility.CHOICES, label="Visibility"
    )

    class Meta:
        model = ChangeOrder
        fields = ["visibility"]


class ProjectFilter(django_filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    status = django_filters.ChoiceFilter(
        choices=Project.Status.CHOICES, label="Status"
    )
    disable_cache = django_filters.BooleanFilter(
        label="Disable Cache", method="filter_disable_cache"
    )

    def filter_disable_cache(self, queryset, name, value):
        return queryset

    class Meta:
        model = Project
        fields = ["name", "status", "disable_cache"]


class ProjectInviteFilter(django_filters.FilterSet):
    status = django_filters.ChoiceFilter(
        choices=ProjectInvite._meta.get_field("status").choices
    )

    class Meta:
        model = ProjectInvite
        fields = ["status"]
