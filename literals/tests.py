from accounts.factories import UserFactory
from django.urls import reverse
from rest_framework import status
from testing.base import BaseAPITest


class LiteralsTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        super().setUp()

    def test_get_literals(self):
        url = reverse("literals:literals-data")
        self.client.force_authenticate(user=self.user)

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # timezones should be a data
        self.assertIn("timezones", response.data)


class CountryCodesTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        super().setUp()

    def test_get_country_codes(self):
        url = reverse("literals:country-codes")
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Ensure the response data has a 'countries' key that is a list
        self.assertIn("countries", response.data)
        self.assertIsInstance(response.data["countries"], list)

        # Optionally, check that each country item has the expected keys
        for country in response.data["countries"]:
            self.assertIn("code", country)
            self.assertIn("country_code", country)
            self.assertIn("country_name", country)

        # Ensure there's at least one country returned
        self.assertTrue(len(response.data["countries"]) > 0)
