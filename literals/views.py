from literals.serializers import CountryCodesSerializer
from literals.serializers import LiteralsSerializer
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

# Create your views here.


class LiteralsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = LiteralsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CountryCodesView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = CountryCodesSerializer(data={})
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
