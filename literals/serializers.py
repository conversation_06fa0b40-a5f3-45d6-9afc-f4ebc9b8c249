from datetime import datetime

import phonenumbers
import pycountry
import pytz
from rest_framework import serializers


class LiteralsSerializer(serializers.Serializer):
    timezones = serializers.SerializerMethodField()

    def get_timezones(self, obj):
        all_timezones = pytz.all_timezones

        data = []
        for timezone in all_timezones:
            tz = pytz.timezone(timezone)
            now = datetime.now(tz)
            offset = now.utcoffset()
            hours, remainder = divmod(offset.total_seconds(), 3600)
            minutes = remainder // 60
            sign = "+" if hours >= 0 else "-"
            utc_offset = f"UTC{sign}{int(abs(hours)):02d}:{int(minutes):02d}"

            try:
                location = timezone.split("/")[-1].replace("_", " ")
                name = tz.tzname(now, is_dst=bool(now.dst()))
                label = f"({utc_offset}) {name} - {location}"
            except Exception:
                label = f"({utc_offset}) {timezone}"

            data.append({"value": timezone, "label": label})

        return data


class CountryCodesSerializer(serializers.Serializer):
    countries = serializers.SerializerMethodField()

    def get_countries(self, obj):
        countries_list = []
        for (
            country_code,
            region_codes,
        ) in phonenumbers.COUNTRY_CODE_TO_REGION_CODE.items():
            for region_code in region_codes:
                country = pycountry.countries.get(alpha_2=region_code)
                if country:  # Only include valid countries
                    countries_list.append(
                        {
                            "code": f"+{country_code}",
                            "country_code": region_code,
                            "country_name": country.name,
                        }
                    )
        # Sort the list by numeric country code
        countries_list.sort(key=lambda x: int(x["code"][1:]))
        return countries_list
