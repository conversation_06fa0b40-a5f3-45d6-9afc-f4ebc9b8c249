import base64
import html
import json
import typing
from datetime import datetime
from email.message import EmailMessage
from typing import List

from django.conf import settings
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from typing_extensions import TypedDict

if typing.TYPE_CHECKING:
    from integrations.models import GoogleIntegration


class GetProfileResponse(TypedDict):
    emailAddress: str
    messagesTotal: int
    threadsTotal: int
    historyId: str


class GetEmailsQuery(TypedDict):
    after: datetime
    before: datetime
    from_email: str
    prefetch_details: bool
    additonal_query: str


class GmailService:
    gmail_client = None
    credentials = None

    def __init__(self, integration: "GoogleIntegration"):
        self.initialize_service(integration)

    def initialize_service(self, integration: "GoogleIntegration"):
        self.credentials = Credentials.from_authorized_user_info(
            json.loads(integration.credentials)
        )
        # https://googleapis.github.io/google-api-python-client/docs/dyn/gmail_v1.html
        self.gmail_client = build("gmail", "v1", credentials=self.credentials)

    def watch_emails(self):
        # Define the request body
        request_body = {
            "topicName": settings.GMAIL_TOPIC_NAME,
            "labelIds": ["INBOX"],
            "labelFilterBehavior": "INCLUDE",
        }

        # Execute the watch request
        response = (
            self.gmail_client.users()
            .watch(userId="me", body=request_body)
            .execute()
        )
        return response

    def get_all_messages(self, **kwargs: GetEmailsQuery) -> List[dict]:
        """
        Get emails from gmail api
        """
        before = kwargs.get("before")
        after = kwargs.get("after")
        prefetch_details = kwargs.get("prefetch_details", True)
        from_email = kwargs.get("from_email")

        query = ""

        if before:
            query += f" before:{before}"

        if after:
            query += f" after:{after}"

        if from_email:
            query += f" from:{from_email}"

        res = (
            self.gmail_client.users()
            .messages()
            .list(userId="me", q=query)
            .execute()
        )

        emails = []
        for message in res.get("messages", []):
            if prefetch_details:
                message["body"] = self.get_message_details(message["id"])
            emails.append(message)

        return emails

    def send_message(self, data: dict):
        """
        Send email using gmail api
        """
        message = EmailMessage()
        message.set_content(data.get("content"))

        message["To"] = data.get("to")
        message["From"] = self.userProfile["emailAddress"]
        message["Subject"] = data.get("subject")
        cc = ",".join(data.get("cc")) if data.get("cc") else None
        if cc:
            message["Cc"] = cc

        thread_id = data.get("thread_id")
        message["In-Reply-To"] = thread_id if thread_id else None

        encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

        create_message = {
            "raw": encoded_message,
            "threadId": thread_id if thread_id else None,
        }

        try:
            message = (
                self.gmail_client.users()
                .messages()
                .send(userId="me", body=create_message)
                .execute()
            )

        except HttpError as error:
            print(f"An error occurred: {error}")
        return self.get_message_details(message.get("id"))

    def get_message_details(self, message_id):
        msg_details = (
            self.gmail_client.users()
            .messages()
            .get(userId="me", id=message_id)
            .execute()
        )

        parsed_msg = self.parse_email(msg_details)

        return parsed_msg

    def get_all_messages_in_thread(self, thread_id):
        res = (
            self.gmail_client.users()
            .threads()
            .get(userId="me", id=thread_id)
            .execute()
        )
        return res

    def get_subcontractor_emails(
        self, subcontractor_emails: List[str], last_query_timestamp=None
    ):

        return self.sync_emails(subcontractor_emails, last_query_timestamp)

    def sync_project_owner_emails(
        self, project_email: str, last_query_timestamp=None
    ):
        return self.sync_emails([project_email], last_query_timestamp)

    def sync_emails(self, emails: List[str], last_query_timestamp=None):

        try:
            messages: List[EmailMessage] = []
            all_message = []
            seen_threads = set()

            for email in emails:
                query = f"from:{email} OR to:{email}"
                if last_query_timestamp:
                    query += f" after:{last_query_timestamp}"

                response = (
                    self.gmail_client.users()
                    .messages()
                    .list(userId="me", q=query)
                    .execute()
                )
                all_message.extend(response.get("messages", []))

            for message in all_message:
                thread_id = message.get("threadId")
                if thread_id and thread_id not in seen_threads:
                    seen_threads.add(thread_id)
                    msg_details = self.get_message_details(message["id"])
                    messages.append(msg_details)

            return messages

        except Exception as e:
            print("An error occurred:", e)
            return []

    def mark_message_as_read(self, message_id):
        try:
            self.gmail_client.users().messages().modify(
                userId="me",
                id=message_id,
                body={"removeLabelIds": ["UNREAD"]},
            ).execute()
        except Exception as e:
            print("An error occurred:", e)

    def parse_email(self, email_data: dict):
        headers: List[dict] = email_data.get("payload", {}).get("headers", [])

        subject = ("",)
        cc_recipients = []
        to_recipients = []
        sender = ""

        for header in headers:
            name = header.get("name")

            if name == "Subject":
                subject = html.unescape(header.get("value", ""))
                continue

            if name == "Cc":
                cc_recipients = header.get("value", []).split(",")
                continue

            if name == "To":
                to_recipients = header.get("value", []).split(",")
                continue

            if name == "From":
                sender = header.get("value", "")

        # if to[] container current user email, remove it and replace with sender
        if self.userProfile["emailAddress"] in to_recipients:
            to_recipients.remove(self.userProfile["emailAddress"])
            to_recipients.append(sender)

        # if cc[] container current user email, remove it
        if self.userProfile["emailAddress"] in cc_recipients:
            cc_recipients.remove(self.userProfile["emailAddress"])

        # Initialize an empty dictionary to store the extracted data
        email_dict = {
            "message_id": email_data.get("id"),
            "thread_id": email_data.get("threadId"),
            "subject": str(subject),
            "cc": cc_recipients if len(cc_recipients) else [],
            "to": to_recipients,
            "sender": sender,
            "content": email_data.get("snippet", ""),
            "is_read": not ("UNREAD" in email_data.get("labelIds", [])),
            "tags": email_data.get("labelIds", []),
            "json_data": email_data,
        }

        return email_dict

    @property
    def userProfile(self) -> GetProfileResponse:
        res = self.gmail_client.users().getProfile(userId="me").execute()
        return res

    def list_history(self, user_id: str, history_id: str):
        """Fetch history events starting from a specific history ID."""
        try:
            response = (
                self.gmail_client.users()
                .history()
                .list(userId=user_id, startHistoryId=history_id)
                .execute()
            )
            return response.get("history", [])  # List of history records
        except HttpError as error:
            print(f"An error occurred: {error}")
            return []
