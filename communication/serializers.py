from core.serializers import TimezoneConverterMixin
from rest_framework import serializers

from .models import EmailMessage
from .models import SmsMessage


class EmailThreadSerializer(serializers.Serializer):
    body = serializers.JSONField()
    id = serializers.CharField()


class EmailMessageSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    json_data = serializers.JSONField(required=False)

    class Meta:
        model = EmailMessage
        read_only_fields = [
            "id",
            "message_id",
            "created_by",
            "sender",
            "json_data",
        ]
        exclude = ("is_deleted",)

    def create(self, validated_data):
        return super().create(validated_data)


class SmsMessageSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = SmsMessage
        read_only_fields = ["id", "created_by"]
        exclude = ("is_deleted",)

    def create(self, validated_data):
        creadted_by = self.context.get("request").user
        validated_data["created_by"] = creadted_by
        return super().create(validated_data)


class SmsMessageThreadSerializer(serializers.Serializer):
    body = SmsMessageSerializer(many=True)
    id = serializers.Char<PERSON>ield()
