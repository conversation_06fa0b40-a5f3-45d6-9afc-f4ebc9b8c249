import json
from unittest.mock import patch

from accounts.factories import UserFactory
from communication.factories import EmailMessageFactory
from communication.factories import SmsMessageFactory
from communication.models import SmsMessage
from communication.services.gmail_service import GmailService
from django.urls import reverse
from integrations.factories import GoogleIntegrationFactory
from project.factories import ProjectFactory
from rest_framework import status
from testing.base import BaseAPITest


class EmailMessagesTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)

        return super().setUp()

    def test_unauthorized_access(self):
        # Given the user is not authenticated
        self.client.force_authenticate(user=None)
        list_create_url = reverse(
            "communication:emails",
        )
        # When the user tries to access the endpoint
        response = self.client.get(list_create_url)
        # Then the user should get a 401 response
        self.assertEqual(response.status_code, 401)

        # And When user tries to create an email message
        response = self.client.post(list_create_url)
        # Then the user should get a 401 response
        self.assertEqual(response.status_code, 401)

        retreive_delete_url = reverse("communication:emails", kwargs={"pk": 1})
        # When the user tries to retreive the endpoint
        response = self.client.get(retreive_delete_url)

        # Then the user should get a 401 response
        self.assertEqual(response.status_code, 401)

        # And When user tries to delete an email message
        response = self.client.delete(retreive_delete_url)

    @patch("communication.services.gmail_service.GmailService.send_message")
    def test_create_email_message(self, mock_send_message):
        # Given the user is authenticated
        self.client.force_authenticate(user=self.user)
        list_create_url = reverse("communication:emails")
        payload = {
            "content": "hello",
            "to": ["<EMAIL>"],
            "subject": "sampe",
            "project": self.project.id,
        }

        mock_send_message.return_value = {
            "message_id": "id",
            "thread_id": "123",
            "subject": "subject",
            "cc": [],
            "to": ["to"],
            "sender": "sender",
            "content": "snippets",
            "is_read": False,
            "tags": ["UREAD"],
            "json_data": {},
        }

        # When the user without integration tries to create an email message
        response = self.client.post(list_create_url, payload)
        # Then the user should get a 404 response
        self.assertEqual(response.status_code, 404)

        # When the user with integration tries to create an email message

        with patch.object(GmailService, "initialize_service"):

            GoogleIntegrationFactory(
                user=self.user, credentials=json.dumps({})
            )

            response = self.client.post(list_create_url, payload)
            # Then the user should get a 201 response
            self.assertEqual(response.status_code, 201)
            # And the email message should be created
            self.assertEqual(self.project.emailmessage_set.count(), 1)
            self.assertEqual(mock_send_message.call_count, 1)

    def test_list_email_messages(self):
        # Given the user is authenticated and has an integration
        self.client.force_authenticate(user=self.user)

        EmailMessageFactory(project=self.project)

        # When the user tries to list email messages
        with patch.object(GmailService, "initialize_service"):
            list_create_url = reverse("communication:emails")
            GoogleIntegrationFactory(
                user=self.user, credentials=json.dumps({})
            )

            response = self.client.get(list_create_url)
            # Then the user should get a 200 response
            self.assertEqual(response.status_code, status.HTTP_200_OK)


class SmsMessagesTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        self.project = ProjectFactory(created_by=self.user)

        return super().setUp()

    def test_unauthorized_access(self):
        # Given the user is not authenticated
        self.client.force_authenticate(user=None)
        list_create_url = reverse(
            "communication:sms",
        )
        # When the user tries to access the endpoint
        response = self.client.get(list_create_url)
        # Then the user should get a 401 response
        self.assertEqual(response.status_code, 401)

        # And When user tries to create an email message
        response = self.client.post(list_create_url)
        # Then the user should get a 401 response
        self.assertEqual(response.status_code, 401)

        retreive_delete_url = reverse("communication:sms", kwargs={"pk": 1})
        # When the user tries to retreive the endpoint
        response = self.client.get(retreive_delete_url)

        # Then the user should get a 401 response
        self.assertEqual(response.status_code, 401)

        # And When user tries to delete an email message
        response = self.client.delete(retreive_delete_url)

    def test_create_sms_message(self):
        # Given the user is authenticated
        self.client.force_authenticate(user=self.user)
        list_create_url = reverse("communication:sms")
        payload = {
            "content": "hello",
            "to": ["0541231234"],
            "thread_id": "123",
            "project": self.project.id,
            "tags": ["UNREAD"],
            "sender": "1234",
        }

        # When the user without integration tries to create an email message
        response = self.client.post(list_create_url, payload)
        # Then the user should get a 404 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_list_sms_messages(self):
        # Given the user is authenticated and has an integration
        self.client.force_authenticate(user=self.user)
        # When the user tries to list sms messages
        SmsMessageFactory(project=self.project, created_by=self.user)
        SmsMessageFactory(project=self.project)

        list_create_url = reverse("communication:sms")
        response = self.client.get(
            list_create_url, {"project": self.project.id}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)

    def test_list_sms_messages_with_same_thread_id(self):
        # Given the user is authenticated
        self.client.force_authenticate(user=self.user)
        # When the user tries to list sms messages
        sms = SmsMessageFactory(project=self.project, created_by=self.user)
        SmsMessageFactory(
            project=self.project, created_by=self.user, thread_id=sms.thread_id
        )

        list_create_url = reverse("communication:sms")
        response = self.client.get(
            list_create_url, {"project": self.project.id}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)

    def test_retreive_sms_messages(self):
        # Given the user is authenticated and has an integration
        self.client.force_authenticate(user=self.user)

        sms = SmsMessageFactory(project=self.project, created_by=self.user)

        # When the user tries to retreive sms messages
        retreive_update_url = reverse(
            "communication:sms", kwargs={"pk": sms.id}
        )
        response = self.client.get(
            retreive_update_url, {"project": self.project.id}
        )

        # THen the user should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        #  And the sms message should be returned
        self.assertEqual(response.data["id"], str(sms.id))

    def test_retreive_sms_messages_details(self):
        # Given the user is authenticated and has an integration
        self.client.force_authenticate(user=self.user)

        sms = SmsMessageFactory(project=self.project, created_by=self.user)
        SmsMessageFactory(
            project=self.project, created_by=self.user, thread_id=sms.thread_id
        )
        SmsMessageFactory(
            project=self.project, created_by=self.user, thread_id=sms.thread_id
        )

        # When the user tries to retreive sms messages
        retreive_update_url = reverse(
            "communication:sms", kwargs={"pk": sms.id}
        )

        # When the user tries to get sms messages details
        response = self.client.get(
            retreive_update_url, {"project": self.project.id}
        )

        # THen the user should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(len(response.data["body"]), 3)

        for message in response.data["body"]:
            self.assertEqual(message["thread_id"], sms.thread_id)

    def test_delete_sms_messages(self):
        # Given the user is authenticated and has an integration
        self.client.force_authenticate(user=self.user)

        sms = SmsMessageFactory(project=self.project, created_by=self.user)

        delete_url = (
            reverse("communication:sms", kwargs={"pk": sms.id})
            + f"?project={self.project.id}"
        )

        # When the user tries to delete sms messages
        response = self.client.delete(
            delete_url,
        )

        # THen the user should get a 204 response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        #  And the sms message should be deleted
        self.assertEqual(
            SmsMessage.objects.get_user_messages(self.user).count(), 0
        )

    def test_delete_sms_messages_by_thread_id(self):
        # Given the user is authenticated and has an integration
        self.client.force_authenticate(user=self.user)

        sms = SmsMessageFactory(project=self.project, created_by=self.user)
        SmsMessageFactory(
            project=self.project, created_by=self.user, thread_id=sms.thread_id
        )
        SmsMessageFactory(
            project=self.project, created_by=self.user, thread_id=sms.thread_id
        )

        # When the user tries to retreive sms messages
        retreive_update_url = reverse(
            "communication:sms", kwargs={"pk": sms.id}
        )

        # When the user tries to delete sms messages
        response = self.client.delete(
            retreive_update_url,
            {"thread_id": sms.thread_id},
        )

        # THen the user should get a 204 response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        #  And the sms message should be deleted
        self.assertEqual(
            SmsMessage.objects.get_user_messages(self.user).count(), 0
        )
