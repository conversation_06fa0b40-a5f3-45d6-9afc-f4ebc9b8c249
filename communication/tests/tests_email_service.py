import json
from unittest.mock import patch
from unittest.mock import PropertyMock

from accounts.factories import UserFactory
from communication.services.gmail_service import GmailService
from integrations.factories import GoogleIntegrationFactory
from testing.base import BaseAPITest


class GmailServiceTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()
        return super().setUp()

    @patch(
        "communication.services.gmail_service.GmailService.userProfile",
        new_callable=PropertyMock,
    )
    def test_google_service_parse_email_function(self, mock_userProfile):
        mock_userProfile.return_value = {
            "emailAddress": "<EMAIL>",
            "messagesTotal": 0,
            "threadsTotal": 0,
            "historyId": " ",
        }

        # Given GmailService instance
        gmail_service = None
        with patch.object(
            GmailService,
            "initialize_service",
        ):
            gmail_service = GmailService(
                integration=GoogleIntegrationFactory(
                    user=self.user, credentials=json.dumps({})
                )
            )

        # When parse_email function is called
        mock_email_data = {
            "threadId": "123",
            "id": "123",
        }

        result = gmail_service.parse_email(mock_email_data)

        # Then result should be a dict
        self.assertIsInstance(result, dict)
        self.assertIsInstance(result["to"], list)
        self.assertIsInstance(result["subject"], str)
        self.assertIsInstance(result["content"], str)
        self.assertIsInstance(result["thread_id"], str)
        self.assertIsInstance(result["cc"], list)

        # When parse_email function is called with data that has current user email in to field

        mock_email_data = {
            "threadId": "123",
            "id": "123",
            "payload": {
                "headers": [
                    {"name": "To", "value": "<EMAIL>"},
                    {"name": "From", "value": "<EMAIL>"},
                ]
            },
        }

        result = gmail_service.parse_email(mock_email_data)

        # Then result should be a dict
        self.assertIsInstance(result, dict)

        # And to should be a list with sender email
        self.assertIsInstance(result["to"], list)
        self.assertEqual(result["to"], ["<EMAIL>"])

        # And sender should be a string with user email
        self.assertIsInstance(result["sender"], str)
        self.assertEqual(result["sender"], "<EMAIL>")

        # When parse_email function is called with data that has without current user email in to field

        mock_email_data = {
            "threadId": "123",
            "id": "123",
            "payload": {
                "headers": [
                    {"name": "To", "value": "<EMAIL>"},
                    {"name": "From", "value": "<EMAIL>"},
                ]
            },
        }

        result = gmail_service.parse_email(mock_email_data)

        # Then result should be a dict
        self.assertIsInstance(result, dict)

        # And to should be a list with sender email
        self.assertIsInstance(result["to"], list)
        self.assertEqual(result["to"], ["<EMAIL>"])

        # And sender should be a string with user email
        self.assertIsInstance(result["sender"], str)
        self.assertEqual(result["sender"], "<EMAIL>")
