# Generated by Django 3.2.17 on 2023-08-18 11:43
import django.contrib.postgres.fields
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("communication", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="emailmessage",
            name="is_read",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="emailmessage",
            name="json_data",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="emailmessage",
            name="sender",
            field=models.Char<PERSON>ield(default="demo sender", max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="emailmessage",
            name="tags",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="smsmessage",
            name="is_read",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=True),
        ),
        migrations.AddField(
            model_name="smsmessage",
            name="sender",
            field=models.CharField(default="demo sender", max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="smsmessage",
            name="tags",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="smsmessage",
            name="thread_id",
            field=models.CharField(blank=True, max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name="emailmessage",
            name="to",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="smsmessage",
            name="to",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                size=None,
            ),
        ),
    ]
