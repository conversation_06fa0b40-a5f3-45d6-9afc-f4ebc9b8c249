import typing
from typing import List

from communication.models import EmailMessage
from communication.models import SmsMessage
from communication.services.gmail_service import GmailService
from contacts.models import Subcontractor
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from integrations.models import GoogleIntegration
from project.models import Project
from rest_framework import status
from rest_framework.generics import ListCreateAPIView
from rest_framework.generics import RetrieveDestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from utils.common import get_user_setting
from utils.contacts import get_subcontractors_emails

from .filters import EmailMessageFilter
from .filters import SmsMessageFilter
from .serializers import EmailMessageSerializer
from .serializers import EmailThreadSerializer
from .serializers import SmsMessageSerializer
from .serializers import SmsMessageThreadSerializer

if typing.TYPE_CHECKING:
    from django.db.models import QuerySet


class EmailMessageViewMixin:

    permission_classes = [IsAuthenticated]
    serializer_class = EmailMessageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = EmailMessageFilter

    def get_queryset(self, *args, **kwargs):
        return EmailMessage.objects.get_user_messages(self.request.user)

    def get_integration(self):
        integration = GoogleIntegration.objects.filter(
            user=self.request.user
        ).first()

        if not integration:
            raise GoogleIntegration.DoesNotExist(
                "Google integration not found"
            )
        return integration

    @property
    def gmail_service(self):
        integration = self.get_integration()
        return GmailService(integration)


class CreateListEmailMessagesView(EmailMessageViewMixin, ListCreateAPIView):
    """
    View to list all emails of a user.
    """

    def get(self, request, *args, **kwargs):

        project_id = self.request.query_params.get("project", None)
        project = Project.objects.filter(id=project_id).first()

        if project:
            project: Project
            subcontractors = Project.objects.get_subcontractors_by_project_id(
                project_id
            )
            subcontractor_emails = get_subcontractors_emails(subcontractors)

            # get last query timestamp from session
            user_settings = get_user_setting(self.request.user)

            last_query_timestamp = None

            if user_settings.last_gmail_sync:

                last_query_timestamp = str(
                    user_settings.last_gmail_sync.timestamp()
                ).split(".")[0]

            message = self.gmail_service.get_subcontractor_emails(
                subcontractor_emails, last_query_timestamp
            )
            project_emails = self.gmail_service.sync_project_owner_emails(
                [project.email], last_query_timestamp
            )

            if len(message):
                self.attach_email_to_subcontactors_projects(
                    message, subcontractor_emails
                )
                user_settings.update_last_gmail_sync()

            if len(project_emails):
                # self.save_emails_to_db(project_emails, project_id)
                self.attach_email_to_all_projects_belonging_to_owner(
                    project, project_emails
                )
                user_settings.update_last_gmail_sync()
                # Call the watch_emails() function to set up push notifications
            watch_response = self.gmail_service.watch_emails()

        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        try:
            return super().post(request, *args, **kwargs)
        except GoogleIntegration.DoesNotExist:
            return Response(
                {"detail": "Google integration not found"}, status=404
            )
        except Project.DoesNotExist:
            return Response({"detail": "Project not found"}, status=404)

    def perform_create(self, serializer: EmailMessageSerializer):
        message = self.gmail_service.send_message(serializer.validated_data)
        #  don't save message if it's a reply
        if not serializer.validated_data.get("thread_id"):
            return self.save_emails_to_db(
                [message], self.request.data["project"]
            )
        return

    def attach_email_to_subcontactors_projects(
        self, messages: List[dict], subcontractor_emails: List[str]
    ):
        for email in subcontractor_emails:
            subcontactor = Subcontractor.objects.filter(
                contact__email=email
            ).first()
            if not subcontactor:
                continue

            subcontactor_projects: "QuerySet[Subcontractor]" = (
                Project.objects.get_all_subcontractor_project(subcontactor.id)
            )

            for project in subcontactor_projects:
                self.save_emails_to_db(messages, project.id)

    def attach_email_to_all_projects_belonging_to_owner(
        self, project: Project, messages: List[dict]
    ):
        projects = Project.objects.filter(
            created_by=self.request.user, email=project.email
        )

        for proj in projects:
            self.save_emails_to_db(messages, proj.id)

    def save_emails_to_db(self, messages: List[dict], project_id: str):
        for msg in messages:
            project = get_object_or_404(Project, pk=project_id)
            serializer = EmailMessageSerializer(
                data={
                    **msg,
                    "project": project.id,
                }
            )

            # for now continue if serializer is not valid
            if not serializer.is_valid():
                continue

            email_message = EmailMessage.objects.filter(
                thread_id=msg["thread_id"],
                project=project,
            ).first()

            if email_message:
                email_message: EmailMessage
                #  don't change the original fields
                msg["to"] = email_message.to
                msg["cc"] = email_message.cc
                msg["thread_id"] = email_message.thread_id

            EmailMessage.objects.update_or_create(
                thread_id=msg["thread_id"],
                project=project,
                defaults={
                    **msg,
                    "message_id": msg["message_id"],
                    "thread_id": msg["thread_id"],
                    "created_by": self.request.user,
                },
            )


class RetrieveDestroyEmailMessageView(
    EmailMessageViewMixin, RetrieveDestroyAPIView
):
    """
    View to retrieve and delete an email message.
    """

    serializer_class = EmailThreadSerializer

    def get_object(self):
        message: EmailMessage = get_object_or_404(
            self.get_queryset(), pk=self.kwargs["pk"]
        )

        if self.request.method == "GET":
            # get all messages in the thread
            messages = self.gmail_service.get_all_messages_in_thread(
                message.thread_id
            )
            self.mark_message_as_read(message)

            return {"id": message.id, "body": messages}

        return message

    def perform_destroy(self, instance: EmailMessage):
        return super().perform_destroy(instance)

    def mark_message_as_read(self, instance: EmailMessage):
        if "UNREAD" in instance.tags:
            self.gmail_service.mark_message_as_read(instance.message_id)
            instance.tags.remove("UNREAD")
        instance.is_read = True

        instance.save()


class BaseSmsMessageViewMixin:
    """
    View to list all sms of a user.
    """

    permission_classes = [IsAuthenticated]
    serializer_class = SmsMessageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = SmsMessageFilter

    def get_queryset(self, *args, **kwargs):
        project_id = self.request.query_params.get("project", None)
        if self.request.method == "GET":
            return SmsMessage.objects.get_unique_user_messages_by_project_id(
                self.request.user, project_id
            )
        return SmsMessage.objects.get_user_messages_by_project_id(
            self.request.user, project_id
        )


class CreateListSmsMessageView(BaseSmsMessageViewMixin, ListCreateAPIView):
    """
    View to list all sms of a user.
    """


class RetrieveDestroySmsMessageView(
    BaseSmsMessageViewMixin, RetrieveDestroyAPIView
):
    """
    View to retrieve and delete an sms message.
    """

    serializer_class = SmsMessageThreadSerializer

    def get_object(self):
        message: SmsMessage = get_object_or_404(
            self.get_queryset(), pk=self.kwargs["pk"]
        )

        if self.request.method == "GET":
            # get all messages in the thread
            messages = SmsMessage.objects.get_thread(
                thread_id=message.thread_id
            )

            return {"id": message.id, "body": messages}

        return message

    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "thread_id": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
        responses={
            204: "Deleted",
            400: "Bad Request",
        },
    )
    def delete(self, request, *args, **kwargs):
        # get thread id from query params
        thread_id = self.request.data.get("thread_id", None)
        if thread_id:
            SmsMessage.objects.delete_thread(self.request.user.id, thread_id)
            return Response(status=status.HTTP_204_NO_CONTENT)

        return super().delete(request, *args, **kwargs)
