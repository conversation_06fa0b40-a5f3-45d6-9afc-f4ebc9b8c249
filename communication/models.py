from accounts.models import User
from core.models import BaseModel
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON><PERSON>
from django.db import models
from project.models import Project


class BaseMessageManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def soft_delete(self, ids):
        return (
            super().get_queryset().filter(id__in=ids).update(is_deleted=True)
        )

    def get_project_messages(self, project_id):
        return super().get_queryset().filter(project_id=project_id)

    def get_user_messages(self, user: User):
        return (
            super()
            .get_queryset()
            .filter(created_by=user)
            .order_by("-created_at")
        )


class SmsMessageManager(BaseMessageManager):
    def get_thread(self, thread_id):
        return (
            super()
            .get_queryset()
            .filter(thread_id=thread_id)
            .order_by("-created_at")
        )

    def delete_thread(self, user_id, thread_id):
        return (
            super()
            .get_queryset()
            .filter(thread_id=thread_id, created_by=user_id)
            .delete()
        )

    def get_user_messages_by_project_id(self, user: User, project_id):
        return (
            super()
            .get_queryset()
            .filter(created_by=user, project_id=project_id)
            .order_by(
                "-created_at",
            )
        )

    def get_unique_user_messages_by_project_id(self, user: User, project_id):
        return (
            super()
            .get_queryset()
            .filter(created_by=user, project_id=project_id)
            .distinct("thread_id")
            .order_by(
                "thread_id",
                "-created_at",
            )
        )


class BaseCachedMessage(BaseModel):
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    to = ArrayField(base_field=models.CharField(max_length=255), blank=True)
    sender = models.CharField(max_length=255)
    is_read = models.BooleanField(default=True)
    tags = ArrayField(
        base_field=models.CharField(max_length=255), blank=True, default=list
    )

    class Meta:
        abstract = True


class EmailMessage(BaseCachedMessage):
    objects = BaseMessageManager()
    thread_id = models.CharField(max_length=1024, blank=True, null=True)
    message_id = models.CharField(max_length=1024)
    cc = ArrayField(
        base_field=models.EmailField(max_length=255), blank=True, null=True
    )
    subject = models.TextField()
    json_data = models.JSONField(blank=True, null=True)


class SmsMessage(BaseCachedMessage):
    objects = SmsMessageManager()
    thread_id = models.CharField(max_length=1024, blank=True, null=True)
