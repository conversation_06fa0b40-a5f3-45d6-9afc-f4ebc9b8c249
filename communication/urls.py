from django.urls import path

from .views import CreateListEmailMessagesView
from .views import CreateListSmsMessageView
from .views import RetrieveDestroyEmailMessageView
from .views import RetrieveDestroySmsMessageView


app_name = "communication"
urlpatterns = [
    path("emails/", CreateListEmailMessagesView.as_view(), name="emails"),
    path(
        "emails/<pk>/",
        RetrieveDestroyEmailMessageView.as_view(),
        name="emails",
    ),
    path("sms/", CreateListSmsMessageView.as_view(), name="sms"),
    path(
        "sms/<pk>/",
        RetrieveDestroySmsMessageView.as_view(),
        name="sms",
    ),
]
