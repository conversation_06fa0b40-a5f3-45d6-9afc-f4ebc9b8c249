import factory

from .models import EmailMessage
from .models import SmsMessage


class EmailMessageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = EmailMessage

    project = factory.SubFactory("project.factories.ProjectFactory")
    content = factory.Faker("text")
    to = [factory.Faker("email")]
    subject = factory.Faker("sentence")
    message_id = factory.Faker("uuid4")
    thread_id = factory.Faker("uuid4")
    created_by = factory.SubFactory("accounts.factories.UserFactory")
    is_read = factory.Faker("boolean")
    json_data = factory.Faker("json")
    tags = [factory.Faker("word")]


class SmsMessageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SmsMessage

    project = factory.SubFactory("project.factories.ProjectFactory")
    content = factory.Faker("text")
    to = [factory.Faker("phone_number")]
    created_by = factory.SubFactory("accounts.factories.UserFactory")
    thread_id = factory.Faker("uuid4")
    tags = [factory.Faker("word")]
