from django.contrib import admin

from .models import EmailMessage
from .models import SmsMessage
from import_export.admin import ImportExportModelAdmin


class EmailMessageAdmin(ImportExportModelAdmin):

    list_display = (
        "id",
        "subject",
        "thread_id",
        "message_id",
        "created_by",
        "created_at",
        "updated_at",
    )
    list_display_links = ("id", "subject")
    search_fields = ("subject", "content", "created_by__email")
    list_filter = ("created_by",)
    list_per_page = 25


class SmsMessageAdmin(ImportExportModelAdmin):

    list_display = (
        "id",
        "created_by",
        "thread_id",
        "created_at",
        "updated_at",
    )
    list_display_links = ("id",)
    search_fields = ("content", "created_by__email")
    list_filter = ("created_by",)
    list_per_page = 25


admin.site.register(EmailMessage, EmailMessageAdmin)
admin.site.register(SmsMessage, SmsMessageAdmin)
