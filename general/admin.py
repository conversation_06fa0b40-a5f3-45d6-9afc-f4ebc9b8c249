from calendar_app.models import CalendarEvent
from contacts.models import Subcontractor
from contacts.models import SubContractorEstimate
from django.contrib import admin
from import_export.admin import ExportActionMixin
from import_export.admin import ImportExportModelAdmin
from integrations.models import GoogleCalendarIntegration
from integrations.models import GoogleIntegration
from resources.models import Tag

from .models import AppSettings
from .models import Favorite
from .models import Feedback
from .models import Setting


class ImportExportModelAdminMixin(ImportExportModelAdmin, ExportActionMixin):
    pass


@admin.register(Setting)
class SettingAdmin(ImportExportModelAdminMixin):
    list_display = ("user", "last_gmail_sync", "timezone", "created_at")
    search_fields = ("user__username", "timezone")
    list_filter = ("timezone", "user")
    readonly_fields = ("created_at", "updated_at")
    date_hierarchy = "last_gmail_sync"
    fieldsets = (
        (None, {"fields": ("user", "company", "last_gmail_sync")}),
        ("Preferences", {"fields": ("favorite", "module", "timezone")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(Favorite)
class FavoriteAdmin(ImportExportModelAdminMixin):
    list_display = (
        "created_by",
        "object_type",
        "object_id",
        "company",
        "created_at",
    )
    search_fields = ("created_by__username", "object_type", "object_id")
    list_filter = ("object_type", "company")
    readonly_fields = ("created_at", "updated_at")
    date_hierarchy = "created_at"


@admin.register(Feedback)
class FeedbackAdmin(ImportExportModelAdminMixin):
    list_display = ("id", "message", "category", "created_at")
    search_fields = ("message",)
    list_filter = ("category",)
    readonly_fields = ("created_at", "updated_at")
    date_hierarchy = "created_at"


@admin.register(AppSettings)
class AppSettingsAdmin(ImportExportModelAdminMixin):
    list_display = ("maintenance_mode", "created_at")
    readonly_fields = ("created_at", "updated_at")


CUSTOM_REGISTRATION_MODELS = [
    Subcontractor,
    SubContractorEstimate,
    GoogleIntegration,
    GoogleCalendarIntegration,
    CalendarEvent,
    Tag,
]


for model in CUSTOM_REGISTRATION_MODELS:
    admin.site.register(model, ImportExportModelAdminMixin)
