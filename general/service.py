from django.conf import settings
from general.models import Setting
from rest_framework.request import Request
from user_agents import parse


class GeneralService:
    @property
    def app_setting(self):
        data = Setting.objects.first()
        return data

    def get_user_agent(self, user_agent_data):

        return parse(user_agent_data)

    def get_user_agent_url(self, request: Request):

        user_agent_string = request.META.get("HTTP_USER_AGENT", "")

        user_agent = self.get_user_agent(user_agent_string)

        user_agent_map = {
            "Android": settings.PLAY_STORE_APP_URL,
            "iOS": settings.APP_STORE_APP_URL,
        }

        return user_agent_map.get(user_agent.os.family, settings.FRONTEND_URL)
