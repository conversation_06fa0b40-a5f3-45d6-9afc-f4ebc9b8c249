# Generated by Django 3.2.17 on 2024-01-11 07:17
import uuid

from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("general", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Feedback",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("message", models.TextField()),
                (
                    "category",
                    models.Char<PERSON>ield(
                        blank=True,
                        choices=[
                            ("account_deletion", "Account Deletion"),
                            ("suggestion", "Suggestion"),
                            ("other", "Other"),
                        ],
                        max_length=255,
                        null=True,
                    ),
                ),
                ("extra_data", models.J<PERSON><PERSON><PERSON>(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
