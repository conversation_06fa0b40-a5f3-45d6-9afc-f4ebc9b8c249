from django.urls import path

from . import views

app_name = "general"
urlpatterns = [
    path("feedback", views.FeedbackView.as_view(), name="feedback"),
    path(
        "settings/", views.SettingDetailView.as_view(), name="setting-detail"
    ),
    path("support", views.SupportMessageView.as_view(), name="support"),
    path(
        "get-favorite",
        views.FavoriteRetrievalView.as_view(),
        name="get-favorite",
    ),
    path(
        "create-favorite",
        views.FavoriteCreationView.as_view(),
        name="create-favorite",
    ),
    path(
        "delete-favorite/<uuid:pk>/",
        views.FavoriteDeleteView.as_view(),
        name="delete-favorite",
    ),
    path("waiting-list", views.WaitingListView.as_view(), name="waiting-list"),
    path(
        "app-settings/", views.AppSettingsView.as_view(), name="app-settings"
    ),
]
