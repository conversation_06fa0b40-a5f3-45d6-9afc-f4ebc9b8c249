from accounts.models import User
from contacts.models import Contact
from django.db.models.signals import post_delete
from django.db.models.signals import post_save
from django.dispatch import receiver
from project.models import Project
from project.models import ProjectDocument
from resources.models import Resource
from storage.models import File

from .models import Favorite
from .models import Setting


# Helper function to delete Favorite entries
def delete_favorite_entries(instance_id, object_type):
    Favorite.objects.filter(
        object_id=instance_id, object_type=object_type
    ).delete()


# Common signal handler function
def handle_favorite_deletion(sender, instance, created=False, **kwargs):
    model_to_type = {
        Contact: Favorite.TYPE.CONTACT,
        Project: Favorite.TYPE.PROJECT,
        ProjectDocument: Favorite.TYPE.GALLERY,
        Resource: Favorite.TYPE.RESOURCE,
        File: Favorite.TYPE.STORAGE,
    }

    favorite_type = model_to_type.get(type(instance))
    if not favorite_type:
        return

    if created:
        return

    # Only delete favorites if instance is soft deleted
    if hasattr(instance, "is_deleted") and instance.is_deleted:
        delete_favorite_entries(instance.id, favorite_type)


# Connect post_save signal for models that support soft delete
post_save_models = [Contact, Project, ProjectDocument, Resource, File]
for model in post_save_models:
    post_save.connect(handle_favorite_deletion, sender=model)

# Connect post_delete signal for all models
for model in post_save_models:
    post_delete.connect(handle_favorite_deletion, sender=model)


@receiver(post_save, sender=User)
def create_or_update_user_settings(sender, instance, created, **kwargs):
    """
    Signal to create or update user settings when a user is created/updated.
    Automatically assigns the user's first company ID to settings.
    """
    if created:
        company = instance.company_set.first()
        Setting.objects.create(
            user=instance, company=company.id if company else None
        )
    else:
        # Update existing settings if company changes
        try:
            settings = instance.settings.first()
            if settings and not settings.company:
                company = instance.company_set.first()
                if company:
                    settings.company = company.id
                    settings.save()
        except Setting.DoesNotExist:
            pass
