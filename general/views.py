from company.models import Company
from core.constants import Features
from django_filters.rest_framework import <PERSON>jango<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.generics import DestroyAPIView
from rest_framework.generics import ListAPIView
from rest_framework.generics import RetrieveAP<PERSON>View
from rest_framework.generics import RetrieveUpdateAPIView
from rest_framework.permissions import AllowAny
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from utils.utils import get_model_and_serializer

from .filters import FavoriteFilter
from .models import AppSettings
from .models import Favorite
from .models import Feedback
from .models import Setting
from .models import SupportMessage
from .models import WaitingList
from .serializers import AppSettingsSerializer
from .serializers import FavoriteSerializer
from .serializers import FeedbackSerializer
from .serializers import SettingSerializer
from .serializers import SupportMessageSerializer
from .serializers import WaitingListSerializer


class FeedbackView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = FeedbackSerializer
    queryset = Feedback.objects.all()


class SettingDetailView(RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Setting.objects.all()
    serializer_class = SettingSerializer

    def get_object(self):
        user = self.request.user
        # Get the first setting or create a new one
        setting = Setting.objects.filter(user=user).first()
        if not setting:
            setting = Setting.objects.create(user=user)
        return setting


class SupportMessageView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = SupportMessageSerializer
    queryset = SupportMessage.objects.all()


class FavoriteRetrievalView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = FavoriteSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = FavoriteFilter
    ordering_fields = ["created_at", "object_type"]
    ordering = ["-created_at"]

    def get_queryset(self):
        company = Company.objects.filter(user=self.request.user).first()
        if not company:
            return Favorite.objects.none()
        return Favorite.objects.filter(
            created_by=self.request.user, company=company
        ).order_by(*self.ordering)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        data = []

        for favorite in queryset:
            favorite_object = {
                "id": favorite.id,
                "type": favorite.object_type,
                "actual_object": None,
            }

            # Get model and serializer using utility function
            feature = self.get_feature_from_favorite_type(favorite.object_type)
            if feature:
                model_class, serializer_class = get_model_and_serializer(
                    feature
                )
                if model_class:
                    model_instance = model_class.objects.filter(
                        id=favorite.object_id
                    ).first()
                    if model_instance:
                        favorite_object["actual_object"] = serializer_class(
                            model_instance
                        ).data

            data.append(favorite_object)

        return Response(data, status=status.HTTP_200_OK)

    def get_feature_from_favorite_type(self, favorite_type):
        """Map favorite type to feature"""
        type_to_feature = {
            Favorite.TYPE.RESOURCE: Features.RESOURCES,
            Favorite.TYPE.STORAGE: Features.STORAGES,
            Favorite.TYPE.PROJECT: Features.PROJECTS,
            Favorite.TYPE.GALLERY: Features.GALLERY,
            Favorite.TYPE.CONTACT: Features.CONTACTS,
        }
        return type_to_feature.get(favorite_type)


class FavoriteCreationView(CreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = FavoriteSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        company = Company.objects.filter(user=self.request.user).first()
        object_type = request.data.get("object_type")
        object_id = request.data.get("object_id")

        favorite, created = Favorite.objects.get_or_create(
            object_type=object_type,
            object_id=object_id,
            created_by=request.user,
            company=company,
        )

        serializer.instance = favorite
        headers = self.get_success_headers(serializer.data)
        status_code = (
            status.HTTP_201_CREATED if created else status.HTTP_200_OK
        )

        return Response(serializer.data, status=status_code, headers=headers)


class FavoriteDeleteView(DestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Favorite.objects.all()

    def perform_destroy(self, instance):
        instance.delete()


class WaitingListView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = WaitingListSerializer
    queryset = WaitingList.objects.all()


class AppSettingsView(RetrieveAPIView):
    queryset = AppSettings.objects.all()
    serializer_class = AppSettingsSerializer
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        settings = self.get_queryset().first()
        if settings:
            return Response({"maintenance_mode": settings.maintenance_mode})
        else:
            return Response({"maintenance_mode": False})
