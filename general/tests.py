from accounts.factories import UserFactory
from company.factories import CompanyFactory
from contacts.factories import ContactFactory
from django.urls import reverse
from general.models import AppSettings
from general.models import Favorite
from general.models import Feedback
from general.models import Setting
from general.models import SupportMessage
from project.factories import ProjectFactory
from resources.factories import ResourceFactory
from rest_framework import status
from storage.factories import FileFactory
from testing.base import BaseAPITest


class FeedbackTest(BaseAPITest):
    def setUp(self):
        super().setUp()

    def test_create(self):
        # When I create a Feedback
        url = reverse("general:feedback")
        data = {
            "message": "This is a test message",
            "category": Feedback.Category.ACCOUNT_DELETION,
            "extra_data": {
                "optional": "data",
                "can": "be",
            },
        }
        response = self.client.post(url, data, format="json")

        # Then I should get a 201 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # And the Feedback should be created
        feedback = Feedback.objects.get()
        self.assertEqual(feedback.message, data["message"])


class SettingTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        # Create a user and a setting for testing using the factory
        self.user = UserFactory()
        self.setting = Setting.objects.create(
            user=self.user,
            favorite={"key": "value"},
            module={"name": "test"},
            timezone="UTC",
        )
        # Create another user without a setting
        self.other_user = UserFactory()
        # Set the url for the setting view
        self.url = reverse("general:setting-detail")

    def test_get_setting(self):
        # When I retrieve the Setting as the user
        self.client.force_authenticate(self.user)
        response = self.client.get(self.url)
        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # And the response data should match the serialized Setting
        self.assertEqual(response.data["user"], self.user.id)

    def test_get_setting_without_setting(self):
        # When I retrieve the Setting as the other user
        self.client.force_authenticate(self.other_user)
        response = self.client.get(self.url)
        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # And the response data should match the default Setting
        self.assertEqual(response.data["user"], self.other_user.id)
        self.assertEqual(response.data["favorite"], {})
        self.assertEqual(response.data["module"], {})
        self.assertEqual(response.data["timezone"], None)

    def test_patch_setting(self):
        # When I update the Setting as the user
        self.client.force_authenticate(self.user)
        data = {
            "favorite": {"new_key": "new_value"},
            "module": {"new_name": "new_test"},
            "timezone": "America/Los_Angeles",
        }
        response = self.client.patch(self.url, data, format="json")
        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # And the Setting should be updated
        updated_setting = Setting.objects.filter(user=self.user).first()
        self.assertEqual(updated_setting.favorite, {"new_key": "new_value"})
        self.assertEqual(updated_setting.module, {"new_name": "new_test"})
        self.assertEqual(updated_setting.timezone, "America/Los_Angeles")


class FavoriteCreationRetrievalTest(BaseAPITest):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.company = CompanyFactory(user=self.user)
        # Create company settings
        self.setting = Setting.objects.create(
            user=self.user,
            favorite={"key": "value"},
            module={"name": "test"},
            timezone="UTC",
            company=self.company.id,
        )
        self.project = ProjectFactory()
        self.file = FileFactory()
        self.resource = ResourceFactory()

    def test_create_retrieve_favorite(self):
        self.client.force_authenticate(user=self.user)

        for obj, obj_type in [
            (self.project, Favorite.TYPE.PROJECT),
            (self.file, Favorite.TYPE.STORAGE),
            (self.resource, Favorite.TYPE.RESOURCE),
        ]:
            # When I create a Favorite
            url = reverse("general:create-favorite")
            data = {
                "object_id": obj.id,
                "object_type": obj_type,
                "created_by": self.user.id,
                "company": self.company.id,
            }
            response = self.client.post(url, data, format="json")

            # Then I should get a 201 response
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

            # And the Favorite should be created
            favorite = Favorite.objects.get(
                object_id=obj.id, object_type=obj_type
            )
            self.assertEqual(
                str(favorite.object_id), str(data["object_id"])
            )  # Convert UUID to string for comparison
            self.assertEqual(favorite.object_type, data["object_type"])

        # When I retrieve the Favorite
        url = reverse("general:get-favorite")
        response = self.client.get(url)

        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # And the Favorite should be retrieved
        self.assertEqual(len(response.data), 3)

    def test_delete_favorite(self):
        self.client.force_authenticate(user=self.user)

        # Create a favorite for testing
        favorite = Favorite.objects.create(
            object_id=self.project.id,
            object_type=Favorite.TYPE.PROJECT,
            created_by=self.user,
            company=self.company,
        )

        # When I delete the Favorite
        url = reverse("general:delete-favorite", kwargs={"pk": favorite.pk})
        response = self.client.delete(url)

        # Then I should get a 204 No Content response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # And the Favorite should be deleted
        self.assertFalse(Favorite.objects.filter(pk=favorite.pk).exists())

    def test_signal_triggered_on_project_delete(self):
        project = ProjectFactory(created_by=self.user)

        self.client.force_authenticate(user=self.user)

        project_delete_url = reverse(
            "project:project", kwargs={"pk": project.id}
        )
        response = self.client.delete(project_delete_url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            Favorite.objects.filter(
                object_id=project.pk, object_type=Favorite.TYPE.PROJECT
            ).exists()
        )

    def test_signal_triggered_on_contact_delete(self):
        contact = ContactFactory(uploaded_by=self.user)

        self.client.force_authenticate(user=self.user)

        contact_delete_url = reverse(
            "contacts:contact", kwargs={"pk": contact.id}
        )
        response = self.client.delete(contact_delete_url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            Favorite.objects.filter(
                object_id=contact.pk, object_type=Favorite.TYPE.CONTACT
            ).exists()
        )


class SupportMessageTest(BaseAPITest):
    def setUp(self):
        super().setUp()

    def test_create_support_message(self):
        # When I create a SupportMessage
        url = reverse("general:support")
        data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "message": "This is a test support message",
        }
        response = self.client.post(url, data, format="json")

        # Then I should get a 201 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # And the SupportMessage should be created
        support_message = SupportMessage.objects.get()
        self.assertEqual(support_message.first_name, data["first_name"])
        self.assertEqual(support_message.last_name, data["last_name"])
        self.assertEqual(support_message.email, data["email"])
        self.assertEqual(support_message.message, data["message"])


class WaitingListTest(BaseAPITest):
    def setUp(self):
        super().setUp()

    def test_create_waiting_list(self):
        # When I create a WaitingList
        url = reverse("general:waiting-list")
        data = {
            "email": "<EMAIL>",
        }
        response = self.client.post(url, data, format="json")

        # Then I should get a 201 response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)


class AppSettingsTest(BaseAPITest):
    def setUp(self):
        # Create an instance of AppSettings for testing
        self.app_settings = AppSettings.objects.create(maintenance_mode=True)
        # Set the URL for the AppSettings view
        self.url = reverse("general:app-settings")

    def test_get_app_settings(self):
        # When I retrieve the AppSettings
        response = self.client.get(self.url)
        # Then I should get a 200 response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
