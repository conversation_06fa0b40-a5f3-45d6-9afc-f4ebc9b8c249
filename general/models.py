from accounts.models import User
from company.models import Company
from core.models import BaseModel
from django.conf import settings
from django.db import models
from django.utils import timezone


class SettingManager(models.Manager):
    def get_or_create_settings(self, user):
        """
        Get or create Settings for the given user with timezone, company, and phone_number_region.
        This ensures that every user always has settings with proper defaults.
        """
        company = user.company_set.first()

        # Default values for creating new settings
        defaults = {
            "timezone": settings.TIME_ZONE,
            "phone_number_region": "US",  # Default region
        }

        # Only set company if it exists
        if company:
            defaults["company"] = company.id

        setting, created = self.get_or_create(user=user, defaults=defaults)

        # If setting exists but doesn't have timezone, update it
        if not created and not setting.timezone:
            setting.timezone = defaults["timezone"]
            setting.save()

        # If setting exists but doesn't have phone_number_region, update it
        if not created and not setting.phone_number_region:
            setting.phone_number_region = defaults["phone_number_region"]
            setting.save()

        # If setting exists but doesn't have company and user has a company, update it
        if not created and not setting.company and company:
            setting.company = company.id
            setting.save()

        return setting

    def get_user_timezone(self, user) -> str:
        """
        Get the timezone setting for the given user.
        Creates settings with default timezone if they don't exist.
        """
        setting = self.get_or_create_settings(user)
        return setting.timezone

    def get_user_phone_region(self, user) -> str:
        """
        Get the phone number region setting for the given user.
        Creates settings with default region if they don't exist.
        """
        setting = self.get_or_create_settings(user)
        return setting.phone_number_region


class Setting(BaseModel):
    user = models.ForeignKey(
        User, related_name="settings", on_delete=models.CASCADE
    )
    company = models.UUIDField(null=True, blank=True, default=None)
    last_gmail_sync = models.DateTimeField(null=True, blank=True)
    favorite = models.JSONField(default=dict)
    module = models.JSONField(default=dict)
    timezone = models.CharField(max_length=255, blank=True, null=True)
    phone_number_region = models.CharField(
        max_length=2, default="US", blank=True
    )
    account_verification_timeout_seconds = models.IntegerField(
        default=24 * 60 * 60
    )
    auto_logout_timeout = models.IntegerField(default=120)

    objects: SettingManager = SettingManager()

    def update_last_gmail_sync(self):
        self.last_gmail_sync = timezone.now()
        self.save()


class Favorite(BaseModel):
    class TYPE:
        RESOURCE = "resource"
        STORAGE = "storage"
        PROJECT = "project"
        GALLERY = "galleries"
        CONTACT = "contact"

        CHOICES = (
            (RESOURCE, "Resource"),
            (STORAGE, "Storage"),
            (PROJECT, "Project"),
            (GALLERY, "Galleries"),
            (CONTACT, "Contact"),
        )

        ALL = [RESOURCE, STORAGE, PROJECT, CONTACT]

    object_type = models.CharField(max_length=20, choices=TYPE.CHOICES)
    object_id = models.CharField(max_length=1024)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)


class Feedback(BaseModel):
    class Category:
        ACCOUNT_DELETION = "account_deletion"
        SUGGESTION = "suggestion"
        OTHER = "other"

        CHOICES = (
            (ACCOUNT_DELETION, "Account Deletion"),
            (SUGGESTION, "Suggestion"),
            (OTHER, "Other"),
        )

        ALL = [ACCOUNT_DELETION, SUGGESTION, OTHER]

    message = models.TextField()
    category = models.CharField(
        max_length=255, blank=True, null=True, choices=Category.CHOICES
    )
    extra_data = models.JSONField(blank=True, null=True)


class SupportMessage(BaseModel):
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    email = models.EmailField()
    message = models.TextField()


class WaitingList(BaseModel):
    email = models.EmailField()


class AppSettings(BaseModel):
    maintenance_mode = models.BooleanField(default=False)
