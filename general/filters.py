import django_filters
from core.constants import Features
from search_engine.search_engine_service import SearchEngineService
from utils.utils import get_model_and_serializer

from .models import Favorite


class FavoriteFilter(django_filters.FilterSet):
    object_type = django_filters.ChoiceFilter(choices=Favorite.TYPE.CHOICES)
    search = django_filters.CharFilter(method="filter_by_search")

    class Meta:
        model = Favorite
        fields = ["object_type"]

    def filter_by_search(self, queryset, name, value):
        if not value:
            return queryset

        # Initialize search engine service
        search_engine = SearchEngineService(self.request.user)

        # Map Favorite types to Features
        type_to_feature = {
            Favorite.TYPE.RESOURCE: Features.RESOURCES,
            Favorite.TYPE.STORAGE: Features.STORAGES,
            Favorite.TYPE.PROJECT: Features.PROJECTS,
            Favorite.TYPE.GALLERY: Features.GALLERY,
            Favorite.TYPE.CONTACT: Features.CONTACTS,
        }

        # Get favorite types from current queryset
        favorite_types = set(queryset.values_list("object_type", flat=True))

        # Convert Favorite types to Features
        search_categories = []
        for fav_type in favorite_types:
            feature = type_to_feature.get(fav_type)
            if feature:
                model, serializer = get_model_and_serializer(feature)
                if model is not None:
                    search_categories.append(feature)

        # Perform search using SearchEngineService
        search_results = search_engine.search(
            value, categories=search_categories
        )

        # Collect matching IDs from search results
        matching_ids = set()
        for fav_type in favorite_types:
            feature = type_to_feature.get(fav_type)
            if feature and feature in search_results:
                matching_ids.update(
                    str(item.id) for item in search_results[feature]
                )

        if not matching_ids:
            return queryset.none()

        return queryset.filter(object_id__in=matching_ids)
