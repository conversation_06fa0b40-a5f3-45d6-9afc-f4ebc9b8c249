from core.serializers import TimezoneConverterMixin
from project.models import Project
from rest_framework import serializers

from .models import AppSettings
from .models import Favorite
from .models import Feedback
from .models import Setting
from .models import SupportMessage
from .models import WaitingList


class ProjectSettingSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "project_logo",
            "created_at",
        ]


class FeedbackSerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    class Meta:
        model = Feedback
        read_only_fields = ("created", "updated")
        exclude = ("is_deleted",)


class SettingSerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    projects = serializers.SerializerMethodField()
    unread_notifications_count = serializers.SerializerMethodField()

    class Meta:
        model = Setting
        read_only_fields = ("user", "created", "updated")
        exclude = ("is_deleted",)

    def get_projects(self, obj):
        user = obj.user
        # Get all projects and filter using the user_has_access method
        projects = Project.objects.filter(
            pk__in=[
                project.pk
                for project in Project.objects.all()
                if project.user_has_access(user)
            ]
        )

        return ProjectSettingSerializer(projects, many=True).data

    def get_unread_notifications_count(self, obj):
        """
        Return count of unread notifications for the user
        """
        from notifications.models import Notification

        return Notification.objects.filter(
            user=obj.user, is_read=False
        ).count()

    def create(self, validated_data):
        """
        Override create method to ensure company is set from user's first company
        """
        user = self.context["request"].user
        company = user.company_set.first()
        if company:
            validated_data["company"] = company.id
        return super().create(validated_data)


class FavoriteSerializer(TimezoneConverterMixin, serializers.ModelSerializer):
    class Meta:
        model = Favorite
        read_only_fields = ("created", "updated", "created_by")
        exclude = ("is_deleted",)


class InFavoriteSerializerMixin(serializers.Serializer):
    in_favorite = serializers.SerializerMethodField()
    favorite_id = serializers.SerializerMethodField()

    def get_favorite_info(self, obj):

        if not hasattr(self, "_favorite_cache"):
            self._favorite_cache = {}

        if obj.id not in self._favorite_cache:
            favorite = Favorite.objects.filter(object_id=obj.id).first()
            self._favorite_cache[obj.id] = favorite

        return self._favorite_cache[obj.id]

    def get_in_favorite(self, obj):
        favorite = self.get_favorite_info(obj)
        return favorite is not None

    def get_favorite_id(self, obj):
        favorite = self.get_favorite_info(obj)
        return favorite.id if favorite else None


class SupportMessageSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = SupportMessage
        read_only_fields = ("created", "updated")
        exclude = ("is_deleted",)


class WaitingListSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = WaitingList
        read_only_fields = ("created", "updated")
        exclude = ("is_deleted",)


class AppSettingsSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = AppSettings
        exclude = ("id", "created_at", "updated_at", "is_deleted")
