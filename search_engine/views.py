from core.constants import Features
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from general.models import Favorite
from recent_app.serializers import RecentActivitySerializer
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from .search_engine_service import FavoriteSearchService
from .search_engine_service import RecentActivitiesSearchService
from .search_engine_service import SearchEngineService
from .serializers import SearchEngineResultSerializer


# Update documentation for the search API
query_param = openapi.Parameter(
    "query",
    openapi.IN_QUERY,
    description="search string",
    type=openapi.TYPE_STRING,
    required=True,
)

categories_param = openapi.Parameter(
    "categories",
    openapi.IN_QUERY,
    description="comma separated categories",
    type=openapi.TYPE_ARRAY,
    items=openapi.Items(
        type=openapi.TYPE_STRING,
        enum=[
            "resources",
            "storages",
            "contacts",
            "projects",
            "calendar_events",
            "officers",
            "galleries",
            "recents",
            "favorites",
        ],
    ),
    required=False,
)

# Add recent_categories parameter for filtering when 'recents' is selected
recent_categories_param = openapi.Parameter(
    "recent_categories",
    openapi.IN_QUERY,
    description="comma separated categories to filter recent activities (only used when 'recents' category is selected)",
    type=openapi.TYPE_ARRAY,
    items=openapi.Items(
        type=openapi.TYPE_STRING,
        enum=[
            Features.CONTACTS,
            Features.SUBCONTRACTORS,
            Features.PROJECTS,
            Features.CHANGE_ORDERS,
            Features.PLAN_AND_ELEVATIONS,
            Features.ESTIMATES,
            Features.CONTRACTS,
            Features.PAYMENT_SCHEDULES,
            Features.PERFORMANCE_SCHEDULES,
            Features.SPECIFICATIONS,
            Features.PERMITS,
            Features.ADDITIONAL_DOCUMENTS,
            Features.GALLERY,
            Features.RESOURCES,
            Features.STORAGES,
            Features.LICENSES,
            Features.INSURANCES,
            Features.CALENDAR_EVENTS,
        ],
    ),
    required=False,
)

favorite_categories_param = openapi.Parameter(
    "favorite_categories",
    openapi.IN_QUERY,
    description="comma separated favorite categories to filter favorites (only used when 'favorites' category is selected)",
    type=openapi.TYPE_ARRAY,
    items=openapi.Items(
        type=openapi.TYPE_STRING,
        enum=[
            Favorite.TYPE.RESOURCE,
            Favorite.TYPE.STORAGE,
            Favorite.TYPE.PROJECT,
            Favorite.TYPE.GALLERY,
            Favorite.TYPE.CONTACT,
        ],
    ),
    required=False,
)

search_response = openapi.Response(
    "success responses", SearchEngineResultSerializer
)


class SearchEngineView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SearchEngineResultSerializer

    @swagger_auto_schema(
        manual_parameters=[
            query_param,
            categories_param,
            recent_categories_param,
            favorite_categories_param,
        ],
        responses={200: search_response},
    )
    def get(self, request):
        query = request.query_params.get("query")
        categories = request.query_params.get("categories")
        recent_categories = request.query_params.get("recent_categories")
        favorite_categories = request.query_params.get("favorite_categories")

        categories = categories.split(",") if categories else []
        recent_categories = (
            recent_categories.split(",") if recent_categories else []
        )
        favorite_categories = (
            favorite_categories.split(",") if favorite_categories else []
        )

        if not query:
            return Response(
                {"error": "query is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Initialize services
        search_engine_service = SearchEngineService(request.user)
        result = search_engine_service.search(query, categories=categories)

        # Handle recents category if present
        if "recents" in categories:
            recent_activities_service = RecentActivitiesSearchService(
                request.user
            )
            recent_activities = recent_activities_service.search(
                query=query, categories=recent_categories
            )
            result["recents"] = {
                "results": RecentActivitySerializer(
                    recent_activities, many=True, context={"request": request}
                ).data,
                "count": recent_activities.count(),
            }

        # Handle favorites category if present
        if Features.FAVORITES in categories:
            favorite_search_service = FavoriteSearchService(request.user)
            favorites = favorite_search_service.search(
                query=query,
                favorite_categories=favorite_categories
                if favorite_categories
                else None,
            )
            result[Features.FAVORITES] = favorites

        serializer = self.serializer_class(result)
        return Response(serializer.data, status=status.HTTP_200_OK)
