from calendar_app.models import CalendarEvent
from calendar_app.serializers import CalendarEventSerializer
from company.models import Insurance
from company.models import Licenses
from company.models import Officer
from company.serializers import InsuranceSerializer
from company.serializers import LicensesSerializer
from company.serializers import OfficerSerializer
from contacts.models import Contact
from contacts.serializers import ContactSerializer
from django.db.models.query import QuerySet
from general.models import Favorite
from project.models import Project
from project.models import ProjectDocument
from project.serializers import ProjectDocumentSerializer
from project.serializers import ProjectSerializer
from resources.models import Resource
from resources.serializers import ResourceSerializer
from rest_framework import serializers
from search_engine.search_engine_service import SearchEngineResult
from storage.models import File
from storage.serializers import FileSerializer

from .search_engine_service import FavoriteSearchService


class SearchEngineResultSerializer(serializers.Serializer):
    resources = serializers.SerializerMethodField()
    storages = serializers.SerializerMethodField()
    contacts = serializers.SerializerMethodField()
    projects = serializers.SerializerMethodField()
    plan_and_elevations = serializers.SerializerMethodField()
    estimates = serializers.SerializerMethodField()
    contracts = serializers.SerializerMethodField()
    change_orders = serializers.SerializerMethodField()
    payment_schedules = serializers.SerializerMethodField()
    performance_schedules = serializers.SerializerMethodField()
    specifications = serializers.SerializerMethodField()
    permits = serializers.SerializerMethodField()
    additional_documents = serializers.SerializerMethodField()
    galleries = serializers.SerializerMethodField()
    licenses = serializers.SerializerMethodField()
    insurances = serializers.SerializerMethodField()
    officers = serializers.SerializerMethodField()
    calendar_events = serializers.SerializerMethodField()
    recents = serializers.SerializerMethodField()
    favorites = serializers.SerializerMethodField()

    def get_recents(self, obj: SearchEngineResult):
        recents_data = obj.get("recents")
        if recents_data is None:
            return {"results": [], "count": 0}
        return recents_data

    def get_favorites(self, obj: SearchEngineResult):
        favorites: QuerySet[Favorite] = obj.get("favorites")
        if not favorites:
            return {"results": [], "count": 0}

        serialized_favorites = []
        for favorite in favorites:
            favorite_data = {
                "id": favorite.id,
                "type": favorite.object_type,
                "actual_object": None,
            }

            (
                model_class,
                serializer_class,
            ) = FavoriteSearchService.MODEL_MAP.get(
                favorite.object_type, (None, None)
            )
            if model_class:
                model_instance = model_class.objects.filter(
                    id=favorite.object_id
                ).first()
                if model_instance:
                    favorite_data["actual_object"] = serializer_class(
                        model_instance
                    ).data

            serialized_favorites.append(favorite_data)

        return {
            "results": serialized_favorites,
            "count": len(serialized_favorites),
        }

    def get_resources(self, obj: SearchEngineResult):
        resources: QuerySet[Resource] = obj.get("resources")
        return self.process_result(resources, ResourceSerializer)

    def get_storages(self, obj: SearchEngineResult):
        storages: QuerySet[File] = obj.get("storages")
        return self.process_result(storages, FileSerializer)

    def get_contacts(self, obj: SearchEngineResult):
        contacts: QuerySet[Contact] = obj.get("contacts")
        return self.process_result(contacts, ContactSerializer)

    def get_projects(self, obj: SearchEngineResult):
        projects: QuerySet[Project] = obj.get("projects")
        return self.process_result(projects, ProjectSerializer)

    def get_plan_and_elevations(self, obj: SearchEngineResult):
        plan_and_elevations: QuerySet[ProjectDocument] = obj.get(
            "plan_and_elevations"
        )
        return self.process_result(
            plan_and_elevations, ProjectDocumentSerializer
        )

    def get_estimates(self, obj: SearchEngineResult):
        estimates: QuerySet[ProjectDocument] = obj.get("estimates")
        return self.process_result(estimates, ProjectDocumentSerializer)

    def get_contracts(self, obj: SearchEngineResult):
        contracts: QuerySet[ProjectDocument] = obj.get("contracts")
        return self.process_result(contracts, ProjectDocumentSerializer)

    def get_change_orders(self, obj: SearchEngineResult):
        change_orders: QuerySet[ProjectDocument] = obj.get("change_orders")
        return self.process_result(change_orders, ProjectDocumentSerializer)

    def get_payment_schedules(self, obj: SearchEngineResult):
        payment_schedules: QuerySet[ProjectDocument] = obj.get(
            "payment_schedules"
        )
        return self.process_result(
            payment_schedules, ProjectDocumentSerializer
        )

    def get_performance_schedules(self, obj: SearchEngineResult):
        performance_schedules: QuerySet[ProjectDocument] = obj.get(
            "performance_schedules"
        )
        return self.process_result(
            performance_schedules, ProjectDocumentSerializer
        )

    def get_specifications(self, obj: SearchEngineResult):
        specifications: QuerySet[ProjectDocument] = obj.get("specifications")
        return self.process_result(specifications, ProjectDocumentSerializer)

    def get_permits(self, obj: SearchEngineResult):
        permits: QuerySet[ProjectDocument] = obj.get("permits")
        return self.process_result(permits, ProjectDocumentSerializer)

    def get_additional_documents(self, obj: SearchEngineResult):
        additional_documents: QuerySet[ProjectDocument] = obj.get(
            "additional_documents"
        )
        return self.process_result(
            additional_documents, ProjectDocumentSerializer
        )

    def get_galleries(self, obj: SearchEngineResult):
        galleries: QuerySet[ProjectDocument] = obj.get("galleries")
        return self.process_result(galleries, ProjectDocumentSerializer)

    def get_licenses(self, obj: SearchEngineResult):
        licenses: QuerySet[Licenses] = obj.get("licenses")
        return self.process_result(licenses, LicensesSerializer)

    def get_insurances(self, obj: SearchEngineResult):
        insurances: QuerySet[Insurance] = obj.get("insurances")
        return self.process_result(insurances, InsuranceSerializer)

    def get_officers(self, obj: SearchEngineResult):
        officers: QuerySet[Officer] = obj.get("officers")
        return self.process_result(officers, OfficerSerializer)

    def process_result(self, result: QuerySet, serializer_class):
        serializer = serializer_class(result, many=True)

        return {"results": serializer.data, "count": result.count()}

    def get_calendar_events(self, obj: SearchEngineResult):
        calendar_events: QuerySet[CalendarEvent] = obj.get("calendar_events")
        return self.process_result(calendar_events, CalendarEventSerializer)
