from accounts.factories import UserFactory
from calendar_app.factories import CalendarEventFactory
from calendar_app.models import CalendarEvent
from company.factories import InsuranceFactory
from company.factories import LicensesFactory
from company.factories import OfficerFactory
from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.models import Officer
from contacts.factories import ContactFactory
from contacts.models import Contact
from django.urls import reverse
from project.factories import ProjectDocumentFactory
from project.factories import ProjectFactory
from project.models import Project
from project.models import ProjectDocument
from resources.factories import ResourceFactory
from resources.factories import TagFactory
from resources.models import Resource
from search_engine.search_engine_service import SearchEngineResult
from search_engine.search_engine_service import SearchEngineService
from storage.factories import FileFactory
from storage.models import File
from testing.base import BaseAPITest


class SearchEngineServiceTest(BaseAPITest):
    def setUp(self):
        self.user = UserFactory()

        xyz_tag = TagFactory(name="xyz", user=self.user)
        summary = "abc"
        description = "test"
        url = "http://abc.org"

        # Resources
        ResourceFactory(
            user=self.user, description=description, url=url, tags=[xyz_tag]
        )
        ResourceFactory()
        self.total_user_resources = Resource.objects.get_user_resources(
            self.user
        ).count()
        self.assertEqual(self.total_user_resources, 1)

        # Files
        FileFactory(
            uploaded_by=self.user,
            file_name=description,
            original_file_name="abc",
            tags=[xyz_tag],
        )
        FileFactory()
        self.total_user_files = File.objects.get_user_files(self.user).count()
        self.assertEqual(self.total_user_files, 1)

        # Contacts
        ContactFactory(
            uploaded_by=self.user,
            first_name=description,
            last_name=description,
            email="abc",
            phone_number="123",
        )
        ContactFactory()
        self.total_user_contacts = (
            Contact.objects.get_contacts_uploaded_by_user(self.user).count()
        )
        self.assertEqual(self.total_user_contacts, 1)

        # Projects
        ProjectFactory(created_by=self.user, name=description)
        ProjectFactory()
        self.total_user_projects = Project.objects.get_project_for_user(
            self.user
        ).count()
        self.assertEqual(self.total_user_projects, 1)

        def create_project_document(user, description, category):
            return ProjectDocumentFactory(
                project__created_by=user, name=description, category=category
            )

        # Project Documents
        document_categories = [
            ProjectDocument.Category.PLAN_AND_ELEVATION,
            ProjectDocument.Category.ESTIMATE,
            ProjectDocument.Category.CONTRACT,
            ProjectDocument.Category.CHANGE_ORDER,
            ProjectDocument.Category.PAYMENT_SCHEDULE,
            ProjectDocument.Category.PERFORMANCE_SCHEDULE,
            ProjectDocument.Category.SPECIFICATION,
            ProjectDocument.Category.PERMIT,
            ProjectDocument.Category.ADDITIONAL_DOCUMENTS,
        ]

        for category in document_categories:
            create_project_document(self.user, description, category)

        company = Company.objects.get_user_company(self.user)

        # Licenses
        LicensesFactory(
            company=company,
            created_by=self.user,
            name=description,
            attachment=FileFactory(original_file_name="abc"),
        )
        self.total_user_licenses = Licenses.objects.get_company_licenses(
            company
        ).count()
        self.assertEqual(self.total_user_licenses, 1)

        # Insurances
        InsuranceFactory(
            company=company,
            created_by=self.user,
            carrier=description,
            policy=FileFactory(original_file_name="abc"),
        )
        self.total_user_insurances = Insurance.objects.get_company_insurance(
            company
        ).count()
        self.assertEqual(self.total_user_insurances, 1)

        # Officers
        OfficerFactory(
            company=company,
            firstname=description,
            lastname="Smith",
            job_position="Manager",
            mobile_number="************",
            email="<EMAIL>",
        )
        self.total_company_officers = Officer.objects.get_company_officers(
            company
        ).count()
        self.assertEqual(self.total_company_officers, 1)

        # Calendar Events
        CalendarEventFactory(
            created_by=self.user, summary=summary, description=description
        )
        self.total_user_calendar_events = (
            CalendarEvent.objects.get_user_events(self.user).count()
        )

        return super().setUp()

    def test_search_engine(self):
        # Given user has the following resources

        # When user searches for "test"
        search_engine_service = SearchEngineService(self.user)

        # Then user should get the dict with resources queryset
        result = search_engine_service.search("test")
        resources = result["resources"]
        storage = result["storages"]
        contacts = result["contacts"]
        projects = result["projects"]
        licenses = result["licenses"]
        insurances = result["insurances"]
        calendar_events = result["calendar_events"]
        officers = result["officers"]

        self.assertEqual(resources.count(), self.total_user_resources)
        self.assertEqual(storage.count(), self.total_user_files)
        self.assertEqual(contacts.count(), self.total_user_contacts)
        self.assertEqual(projects.count(), self.total_user_projects)
        self.assertEqual(licenses.count(), self.total_user_licenses)
        self.assertEqual(insurances.count(), self.total_user_insurances)
        self.assertEqual(
            calendar_events.count(), self.total_user_calendar_events
        )
        self.assertEqual(officers.count(), self.total_company_officers)

        result = search_engine_service.search("abc")
        self.assertEqual(
            result["resources"].count(), self.total_user_resources
        )
        self.assertEqual(result["storages"].count(), self.total_user_files)
        self.assertEqual(result["contacts"].count(), self.total_user_contacts)
        self.assertEqual(result["projects"].count(), 0)
        self.assertEqual(result["licenses"].count(), self.total_user_licenses)
        self.assertEqual(
            result["insurances"].count(), self.total_user_insurances
        )
        self.assertEqual(
            result["calendar_events"].count(), self.total_user_calendar_events
        )
        self.assertEqual(result["officers"].count(), 0)

        result = search_engine_service.search("xyz")
        self.assertEqual(
            result["resources"].count(), self.total_user_resources
        )
        self.assertEqual(result["storages"].count(), self.total_user_files)
        self.assertEqual(result["contacts"].count(), 0)
        self.assertEqual(result["projects"].count(), 0)
        self.assertEqual(result["licenses"].count(), 0)
        self.assertEqual(result["insurances"].count(), 0)
        self.assertEqual(result["calendar_events"].count(), 0)
        self.assertEqual(result["officers"].count(), 0)

        result = search_engine_service.search("not found")
        self.assertEqual(result["resources"].count(), 0)
        self.assertEqual(result["storages"].count(), 0)
        self.assertEqual(result["contacts"].count(), 0)
        self.assertEqual(result["projects"].count(), 0)
        self.assertEqual(result["licenses"].count(), 0)
        self.assertEqual(result["insurances"].count(), 0)
        self.assertEqual(result["calendar_events"].count(), 0)
        self.assertEqual(result["officers"].count(), 0)

    def test_search_api(self):
        # Given user is authenticated
        self.client.force_authenticate(user=self.user)

        # When user searches for "test" without categories
        url = reverse("search_engine:search")
        response = self.client.get(url, data={"query": "test"})

        # Then response should be 200
        self.assertEqual(response.status_code, 200)

        result: SearchEngineResult = response.data

        def check_result(results, key, expected_count):
            self.assertIn(key, response.data)
            self.assertEqual(len(results[key]["results"]), expected_count)

        # Then response should have the following keys with the following count
        check_result(result, "resources", 1)
        check_result(result, "plan_and_elevations", 1)
        check_result(result, "estimates", 1)
        check_result(result, "contracts", 1)
        check_result(result, "change_orders", 1)
        check_result(result, "payment_schedules", 1)
        check_result(result, "performance_schedules", 1)
        check_result(result, "specifications", 1)
        check_result(result, "permits", 1)
        check_result(result, "additional_documents", 1)
        check_result(result, "storages", 1)
        check_result(result, "contacts", 1)
        check_result(result, "projects", 1)
        check_result(result, "licenses", 1)
        check_result(result, "insurances", 1)
        check_result(result, "officers", 1)

    def test_search_without_query_string(self):
        # Given user is authenticated
        self.client.force_authenticate(user=self.user)

        # When user searches without query string
        url = reverse("search_engine:search")
        response = self.client.get(url)

        # Then response should be 400
        self.assertEqual(response.status_code, 400)

    def test_search_api_with_categories(self):
        # Given user is authenticated
        self.client.force_authenticate(user=self.user)

        def check_result(results, key, expected_count):
            self.assertIn(key, response.data)
            self.assertEqual(len(results[key]["results"]), expected_count)

        # When user searches for "test" with categories
        url = reverse("search_engine:search")
        response = self.client.get(
            url,
            data={
                "query": "test",
                "categories": "resources,storages,officers",
            },
        )
        self.assertEqual(response.status_code, 200)

        result = response.data

        check_result(result, "resources", 1)
        check_result(result, "storages", 1)
        check_result(result, "contacts", 0)
        check_result(result, "projects", 0)
        check_result(result, "officers", 1)
