from typing import List
from typing import Optional

from accounts.models import User
from calendar_app.models import CalendarEvent
from company.models import Company
from company.models import Insurance
from company.models import Licenses
from company.models import Officer
from contacts.models import Contact
from contacts.serializers import ContactSerializer
from core.constants import Features
from django.db.models.query import Q
from django.db.models.query import QuerySet
from general.models import Favorite
from project.models import Project
from project.models import ProjectDocument
from project.serializers import ProjectDocumentSerializer
from project.serializers import ProjectSerializer
from recent_app.models import RecentActivity
from resources.models import Resource
from resources.serializers import ResourceSerializer
from storage.models import File
from storage.serializers import FileSerializer
from typing_extensions import TypedDict


class SearchEngineResult(TypedDict):
    resources: QuerySet[Resource]
    storages: QuerySet[File]
    contacts: QuerySet[Contact]
    projects: QuerySet[Project]
    plan_and_elevations: QuerySet[ProjectDocument]
    estimates: QuerySet[ProjectDocument]
    contracts: QuerySet[ProjectDocument]
    change_orders: QuerySet[ProjectDocument]
    payment_schedules: QuerySet[ProjectDocument]
    performance_schedules: QuerySet[ProjectDocument]
    specifications: QuerySet[ProjectDocument]
    permits: QuerySet[ProjectDocument]
    additional_documents: QuerySet[ProjectDocument]
    licenses: QuerySet[Licenses]
    insurances: QuerySet[Insurance]
    calendar_events: QuerySet[CalendarEvent]
    officer: QuerySet[Officer]


class FavoriteSearchService:
    """
    Service class for searching through favorites based on the actual object data
    """

    # Move model_map to class level
    MODEL_MAP = {
        Favorite.TYPE.PROJECT: (Project, ProjectSerializer),
        Favorite.TYPE.RESOURCE: (Resource, ResourceSerializer),
        Favorite.TYPE.STORAGE: (File, FileSerializer),
        Favorite.TYPE.CONTACT: (Contact, ContactSerializer),
        Favorite.TYPE.GALLERY: (ProjectDocument, ProjectDocumentSerializer),
    }

    def __init__(self, user):
        self.user = user
        self.company = Company.objects.filter(user=user).first()

    def search(
        self, query: str, favorite_categories: List[str] = None
    ) -> QuerySet:
        """
        Search favorites based on query and optional favorite categories

        Args:
            query (str): Search query
            favorite_categories (List[str], optional): List of favorite categories to filter by

        Returns:
            QuerySet: Filtered queryset of Favorite objects
        """
        base_queryset = Favorite.objects.filter(
            created_by=self.user, company=self.company
        )

        if favorite_categories:
            base_queryset = base_queryset.filter(
                object_type__in=favorite_categories
            )

        favorite_q = Q()

        # Search in Project favorites
        if (
            not favorite_categories
            or Favorite.TYPE.PROJECT in favorite_categories
        ):
            project_ids = Project.objects.filter(
                Q(name__icontains=query)
            ).values_list("id", flat=True)
            project_ids = [str(id) for id in project_ids]
            favorite_q |= Q(
                object_type=Favorite.TYPE.PROJECT, object_id__in=project_ids
            )

        # Search in Resource favorites
        if (
            not favorite_categories
            or Favorite.TYPE.RESOURCE in favorite_categories
        ):
            resource_ids = Resource.objects.filter(
                Q(tags__name__icontains=query)
                | Q(description__icontains=query)
                | Q(url__icontains=query)
            ).values_list("id", flat=True)
            resource_ids = [str(id) for id in resource_ids]
            favorite_q |= Q(
                object_type=Favorite.TYPE.RESOURCE, object_id__in=resource_ids
            )

        # Search in Storage favorites
        if (
            not favorite_categories
            or Favorite.TYPE.STORAGE in favorite_categories
        ):
            storage_ids = File.objects.filter(
                Q(tags__name__icontains=query)
                | Q(original_file_name__icontains=query)
                | Q(file_name__icontains=query)
            ).values_list("id", flat=True)
            storage_ids = [str(id) for id in storage_ids]
            favorite_q |= Q(
                object_type=Favorite.TYPE.STORAGE, object_id__in=storage_ids
            )

        # Search in Contact favorites
        if (
            not favorite_categories
            or Favorite.TYPE.CONTACT in favorite_categories
        ):
            contact_ids = Contact.objects.filter(
                Q(first_name__icontains=query)
                | Q(last_name__icontains=query)
                | Q(email__icontains=query)
                | Q(phone_number__icontains=query)
            ).values_list("id", flat=True)
            contact_ids = [str(id) for id in contact_ids]
            favorite_q |= Q(
                object_type=Favorite.TYPE.CONTACT, object_id__in=contact_ids
            )

        # Search in Gallery favorites
        if (
            not favorite_categories
            or Favorite.TYPE.GALLERY in favorite_categories
        ):
            gallery_ids = ProjectDocument.objects.filter(
                name__icontains=query
            ).values_list("id", flat=True)
            gallery_ids = [str(id) for id in gallery_ids]
            favorite_q |= Q(
                object_type=Favorite.TYPE.GALLERY, object_id__in=gallery_ids
            )

        return base_queryset.filter(favorite_q).distinct()


class SearchEngineService:
    resource_queryset: QuerySet[Resource] = []
    storage_queryset: QuerySet[File] = []
    contacts_queryset: QuerySet[Contact] = []
    projects_queryset: QuerySet[Project] = []
    project_documents_queryset: QuerySet[
        ProjectDocument
    ] = ProjectDocument.objects.none()
    licenses_queryset: QuerySet[Licenses] = Licenses.objects.none()
    insurances_queryset: QuerySet[Insurance] = Insurance.objects.none()
    calendars_events_queryset: QuerySet[
        CalendarEvent
    ] = CalendarEvent.objects.none()
    officer_queryset: QuerySet[Officer] = Officer.objects.none()

    user: User = None

    def __init__(self, user: User):
        self.user = user
        self.resource_queryset = Resource.objects.get_user_resources(user)
        self.storage_queryset = File.objects.get_user_files(user)
        self.contacts_queryset = Contact.objects.get_contacts_uploaded_by_user(
            user
        )
        self.projects_queryset = Project.objects.get_project_for_user(user)
        self.project_documents_queryset = (
            ProjectDocument.objects.get_user_documents(self.user)
        )
        company = Company.objects.get_user_company(user)
        self.licenses_queryset = Licenses.objects.get_company_licenses(company)
        self.insurances_queryset = Insurance.objects.get_company_insurance(
            company
        )
        self.calendars_events_queryset = CalendarEvent.objects.get_user_events(
            user
        )
        self.officer_queryset = Officer.objects.get_company_officers(company)
        self.favorite_search_service = FavoriteSearchService(user)

    def search(self, query, categories: List[str] = None) -> dict:
        categories = categories or []
        categories_set = set(categories)

        def can_search_in_category(category):
            if not len(categories):
                return True
            return category in categories_set

        category_mapping = {
            Features.RESOURCES: self.search_resources,
            Features.STORAGES: self.search_storage,
            Features.CONTACTS: self.search_contacts,
            Features.PROJECTS: self.search_projects,
            Features.PLAN_AND_ELEVATIONS: lambda q: self.search_project_document(
                q, ProjectDocument.Category.PLAN_AND_ELEVATION
            ),
            Features.ESTIMATES: lambda q: self.search_project_document(
                q, ProjectDocument.Category.ESTIMATE
            ),
            Features.CONTRACTS: lambda q: self.search_project_document(
                q, ProjectDocument.Category.CONTRACT
            ),
            Features.CHANGE_ORDERS: lambda q: self.search_project_document(
                q, ProjectDocument.Category.CHANGE_ORDER
            ),
            Features.PAYMENT_SCHEDULES: lambda q: self.search_project_document(
                q, ProjectDocument.Category.PAYMENT_SCHEDULE
            ),
            Features.PERFORMANCE_SCHEDULES: lambda q: self.search_project_document(
                q, ProjectDocument.Category.PERFORMANCE_SCHEDULE
            ),
            Features.SPECIFICATIONS: lambda q: self.search_project_document(
                q, ProjectDocument.Category.SPECIFICATION
            ),
            Features.PERMITS: lambda q: self.search_project_document(
                q, ProjectDocument.Category.PERMIT
            ),
            Features.ADDITIONAL_DOCUMENTS: lambda q: self.search_project_document(
                q, ProjectDocument.Category.ADDITIONAL_DOCUMENTS
            ),
            Features.GALLERY: lambda q: self.search_project_document(
                q, ProjectDocument.Category.GALLERY
            ),
            Features.LICENSES: self.search_licenses,
            Features.INSURANCES: self.search_insurance,
            Features.CALENDAR_EVENTS: self.search_calendar_events,
            Features.OFFICERS: self.search_officer,
            Features.FAVORITES: lambda q: self.search_favorites(q),
        }

        output = {
            Features.RESOURCES: Resource.objects.none(),
            Features.STORAGES: File.objects.none(),
            Features.CONTACTS: Contact.objects.none(),
            Features.PROJECTS: Project.objects.none(),
            Features.PLAN_AND_ELEVATIONS: ProjectDocument.objects.none(),
            Features.ESTIMATES: ProjectDocument.objects.none(),
            Features.CONTRACTS: ProjectDocument.objects.none(),
            Features.CHANGE_ORDERS: ProjectDocument.objects.none(),
            Features.PAYMENT_SCHEDULES: ProjectDocument.objects.none(),
            Features.PERFORMANCE_SCHEDULES: ProjectDocument.objects.none(),
            Features.SPECIFICATIONS: ProjectDocument.objects.none(),
            Features.PERMITS: ProjectDocument.objects.none(),
            Features.ADDITIONAL_DOCUMENTS: ProjectDocument.objects.none(),
            Features.GALLERY: ProjectDocument.objects.none(),
            Features.LICENSES: Licenses.objects.none(),
            Features.INSURANCES: Insurance.objects.none(),
            Features.CALENDAR_EVENTS: CalendarEvent.objects.none(),
            Features.OFFICERS: Officer.objects.none(),
            Features.FAVORITES: Favorite.objects.none(),
        }
        for category, search_function in category_mapping.items():
            if can_search_in_category(category):
                output[category] = search_function(query)

        return output

    def search_resources(self, query):
        # search in tags, description, url
        return (
            self.resource_queryset.filter(tags__name__icontains=query)
            | self.resource_queryset.filter(description__icontains=query)
            | self.resource_queryset.filter(url__icontains=query)
        )

    def search_storage(self, query):
        # search in original_file_name, file_name, tags
        return (
            self.storage_queryset.filter(tags__name__icontains=query)
            | self.storage_queryset.filter(original_file_name__icontains=query)
            | self.storage_queryset.filter(file_name__icontains=query)
        )

    def search_contacts(self, query):
        # search in first_name, email, phone_number
        return (
            self.contacts_queryset.filter(first_name__icontains=query)
            | self.contacts_queryset.filter(last_name__icontains=query)
            | self.contacts_queryset.filter(email__icontains=query)
            | self.contacts_queryset.filter(phone_number__icontains=query)
        )

    def search_projects(self, query):
        # search in name
        return self.projects_queryset.filter(name__icontains=query)

    def search_project_document(self, query, category):

        return self.project_documents_queryset.filter(
            name__icontains=query, category=category
        )

    def search_licenses(self, query):
        return (
            self.licenses_queryset.filter(name__icontains=query)
            | self.licenses_queryset.filter(
                attachment__original_file_name__icontains=query
            )
            | self.licenses_queryset.filter(
                additional_attachments__original_file_name__icontains=query
            )
        ).distinct()

    def search_insurance(self, query):
        return (
            self.insurances_queryset.filter(carrier__icontains=query)
            | self.insurances_queryset.filter(
                policy__original_file_name__icontains=query
            )
            | self.insurances_queryset.filter(
                additional_attachments__original_file_name__icontains=query
            )
        ).distinct()

    def search_calendar_events(self, query):
        return self.calendars_events_queryset.filter(
            summary__icontains=query
        ) | self.calendars_events_queryset.filter(description__icontains=query)

    def search_officer(self, query):
        return (
            self.officer_queryset.filter(firstname__icontains=query)
            | self.officer_queryset.filter(lastname__icontains=query)
            | self.officer_queryset.filter(job_position__icontains=query)
            | self.officer_queryset.filter(mobile_number__icontains=query)
            | self.officer_queryset.filter(extension__icontains=query)
            | self.officer_queryset.filter(email__icontains=query)
        )

    def search_favorites(self, query):
        """Search in favorites"""
        favorite_categories = (
            self.request.query_params.get("favorite_categories", "").split(",")
            if hasattr(self, "request")
            else None
        )
        return self.favorite_search_service.search(
            query,
            favorite_categories=favorite_categories
            if favorite_categories and favorite_categories[0]
            else None,
        )


class RecentActivitiesSearchService:
    """
    Service class for searching and filtering recent activities.
    Allows filtering by category and searching within original objects.
    """

    def __init__(self, user):
        self.user = user
        self.search_engine_service = SearchEngineService(user)

    def search(
        self, query: Optional[str] = None, categories: List[str] = None
    ) -> QuerySet:
        """
        Search recent activities based on query and categories.

        Args:
            query (str, optional): Search query to filter results
            categories (List[str], optional): List of categories to filter by

        Returns:
            QuerySet: Filtered queryset of RecentActivity objects
        """
        # Start with base queryset filtered by user and ordered by updated_at
        queryset = RecentActivity.objects.filter(
            created_by=self.user
        ).order_by("-updated_at")

        # Apply category filters if specified
        if categories:
            # Filter out empty strings
            categories = [cat for cat in categories if cat]
            if categories:
                queryset = queryset.filter(category__in=categories)

        # If there's a search query, we need to search both in RecentActivity and original objects
        if query:
            # First, use SearchEngineService to find matching original objects
            search_results = self.search_engine_service.search(query)

            # Build a Q object for category-specific ID filtering
            category_q = Q()
            for category, results in search_results.items():
                if results.exists():
                    category_q |= Q(
                        category=category,
                        item_id__in=results.values_list("id", flat=True),
                    )

            # Combine with direct RecentActivity filtering
            queryset = queryset.filter(category_q).distinct()

        return queryset
